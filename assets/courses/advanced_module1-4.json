{"level": "Advanced", "modules": [{"id": "module_1", "title": {"en": "Technical Analysis Basics", "sw": "<PERSON><PERSON><PERSON> ya Uchambuzi wa Kiufundi"}, "objectives": {"en": ["Use charts and indicators to analyze price trends.", "Understand key tools like RSI, MACD, and Moving Averages."], "sw": ["Tumia chati na viashiria kuchambua mienendo ya bei.", "<PERSON>ewa zana muhimu kama <PERSON>, MACD, na <PERSON><PERSON> wa <PERSON>ga."]}, "content": {"en": "Technical analysis involves examining stock price and volume data using charts and indicators. Tools like Relative Strength Index (RSI), Moving Averages, and MACD help identify trends, momentum, and reversal signals.", "sw": "Uchambuzi wa kiufundi unahusisha kuchunguza bei ya hisa na kiasi cha biashara kupitia chati na viashiria. <PERSON><PERSON>ma <PERSON>, <PERSON><PERSON> wa <PERSON>, na MAC<PERSON> husaidia kutambua mwenendo, kasi, na dalili za mabad<PERSON>."}, "official_links": ["https://investing.com/technical/indicators", "https://dse.co.tz/market_reports"], "sample_exercises": {"en": ["What does RSI indicate in technical analysis?"], "sw": ["RSI inaonyesha nini katika uchambuzi wa kiufundi?"]}}, {"id": "module_2", "title": {"en": "Portfolio Management", "sw": "Usimamizi wa Uwekezaji wa <PERSON>oja"}, "objectives": {"en": ["Construct a diversified portfolio.", "Manage risk and return across different assets."], "sw": ["Tengeneza uwekezaji uliogawanyika vizuri.", "<PERSON><PERSON><PERSON> hatari na faida katika mali tofauti."]}, "content": {"en": "Portfolio management involves balancing risk and reward by investing in a mix of assets such as equities, bonds, and cash. Diversification and periodic rebalancing are key techniques used.", "sw": "Usimamizi wa uwekezaji wa pamoja unahusisha kusawazisha hatari na faida kwa kuwekeza katika mali tofauti kama vile hisa, hati fungani, na fedha taslimu. Kugawanya uwekezaji na kusawazisha upya mara kwa mara ni mbinu muhimu."}, "official_links": ["https://cfa.iswp.org", "https://cmsa.go.tz"], "sample_exercises": {"en": ["Why is diversification important in portfolio management?"], "sw": ["Kwa nini kugawanya uwekezaji ni muhimu katika usimamizi wa uwekezaji wa pamoja?"]}}, {"id": "module_3", "title": {"en": "Macroeconomic Indicators & Stock Performance", "sw": "Via<PERSON>ria vya Kiuchumi na Utendaji wa Soko"}, "objectives": {"en": ["Interpret how inflation, interest rates, and GDP affect stocks.", "Correlate macro indicators with market cycles."], "sw": ["<PERSON><PERSON><PERSON> jinsi m<PERSON><PERSON>ko wa bei, v<PERSON><PERSON><PERSON> vya riba, na <PERSON>o <PERSON> vinavy<PERSON>hiri hisa.", "<PERSON><PERSON><PERSON> via<PERSON><PERSON> vya kiuchumi na mizunguko ya soko."]}, "content": {"en": "Rising interest rates can hurt stocks by increasing borrowing costs. High inflation reduces consumer spending and company profits. GDP growth signals business expansion and stock optimism.", "sw": "Kuongezeka kwa viwango vya riba kunaweza kuathiri hisa kwa kuongeza gharama za mikopo. <PERSON><PERSON><PERSON><PERSON> wa bei mkubwa hupunguza matumizi ya wateja na faida za kampuni. <PERSON><PERSON><PERSON><PERSON> wa Pato la Taifa unaashiria upanuzi wa biashara na matarajio mazuri ya soko."}, "official_links": ["https://bot.go.tz", "https://nbs.go.tz"], "sample_exercises": {"en": ["How does GDP growth influence stock prices?"], "sw": ["<PERSON><PERSON><PERSON><PERSON> wa Pato la <PERSON> unaa<PERSON>rije bei ya hisa?"]}}, {"id": "module_4", "title": {"en": "Algorithmic & Automated Trading", "sw": "Biashara ya Kiotomatiki na Algoriti"}, "objectives": {"en": ["Understand the basics of algorithmic trading strategies.", "Explore automated order execution and risks involved."], "sw": ["<PERSON><PERSON>a misingi ya mbinu za biashara kwa kutumia algoriti.", "Gundua jinsi maagizo ya kiotomatiki hufanya kazi na hatari zinazohusiana."]}, "content": {"en": "Algorithmic trading uses predefined rules based on time, price, volume, or other metrics to execute orders. While faster and efficient, it also increases systemic risk and requires technical expertise.", "sw": "Biashara kwa kutumia algoriti hutumia kanuni zilizowekwa kulingana na muda, bei, kiasi, au vigezo vingine kuweka maagizo. In<PERSON> ni haraka na bora, huongeza hatari ya mfumo na huhitaji utaalamu wa kiufundi."}, "official_links": ["https://nseindia.com/education/content/algorithms"], "sample_exercises": {"en": ["What is one key advantage of algorithmic trading?"], "sw": ["<PERSON><PERSON> faida moja ya <PERSON>hara kwa kutumia algoriti."]}}]}