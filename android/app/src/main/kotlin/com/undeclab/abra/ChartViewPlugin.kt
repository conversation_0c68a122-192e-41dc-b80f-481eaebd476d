package com.undeclab.abra

import android.graphics.SurfaceTexture
import android.view.Surface
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import io.flutter.view.TextureRegistry
import java.util.concurrent.ConcurrentHashMap

class ChartViewPlugin : FlutterPlugin, MethodCallHandler {
    private lateinit var channel: MethodChannel
    private lateinit var textureRegistry: TextureRegistry
    private val textureEntries = ConcurrentHashMap<Int, TextureRegistry.SurfaceTextureEntry>()
    private val surfaces = ConcurrentHashMap<Int, Surface>()

    companion object {
        init {
            System.loadLibrary("native-lib")
        }
    }

    override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        channel = MethodChannel(flutterPluginBinding.binaryMessenger, "com.abra/chart_view")
        channel.setMethodCallHandler(this)
        textureRegistry = flutterPluginBinding.textureRegistry
    }

    override fun onMethodCall(call: MethodCall, result: Result) {
        when (call.method) {
            "createTexture" -> {
                createTexture(call, result)
            }
            "resizeTexture" -> {
                resizeTexture(call, result)
            }
            "disposeTexture" -> {
                disposeTexture(call, result)
            }
            "feedTick" -> {
                feedTick(call, result)
            }
            "setHighPrice" -> {
                setHighPrice(call, result)
            }
            "setStartTime" -> {
                setStartTime(call, result)
            }
            "setZoomScales" -> {
                setZoomScales(call, result)
            }
            "setPanOffsets" -> {
                setPanOffsets(call, result)
            }
            "getCandlestickCount" -> {
                getCandlestickCount(call, result)
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    private fun createTexture(call: MethodCall, result: Result) {
        try {
            val width = call.argument<Int>("width") ?: 800
            val height = call.argument<Int>("height") ?: 600
            val timeIntervalSeconds = call.argument<Int>("timeIntervalSeconds") ?: 300
            val includePriceAxis = call.argument<Boolean>("includePriceAxis") ?: false
            val includeTimeAxis = call.argument<Boolean>("includeTimeAxis") ?: false
            val priceAxisWidth = call.argument<Int>("priceAxisWidth") ?: 50
            val timeAxisHeight = call.argument<Int>("timeAxisHeight") ?: 30

            // Create texture entry
            val textureEntry = textureRegistry.createSurfaceTexture()
            val surfaceTexture = textureEntry.surfaceTexture()
            surfaceTexture.setDefaultBufferSize(width, height)
            
            val surface = Surface(surfaceTexture)
            
            // Create native texture
            val nativeTextureId = nativeCreateTexture(
                surface, width, height, timeIntervalSeconds,
                includePriceAxis, includeTimeAxis, priceAxisWidth, timeAxisHeight
            )
            
            if (nativeTextureId < 0) {
                textureEntry.release()
                surface.release()
                result.error("TEXTURE_CREATION_FAILED", "Failed to create native texture", null)
                return
            }
            
            // Store references
            val flutterTextureId = textureEntry.id().toInt()
            textureEntries[nativeTextureId] = textureEntry
            surfaces[nativeTextureId] = surface
            
            // Start rendering loop
            startRenderingLoop(nativeTextureId)
            
            result.success(flutterTextureId)
        } catch (e: Exception) {
            result.error("TEXTURE_CREATION_ERROR", e.message, null)
        }
    }

    private fun resizeTexture(call: MethodCall, result: Result) {
        try {
            val textureId = call.argument<Int>("textureId") ?: return result.error("INVALID_ARGS", "Missing textureId", null)
            val width = call.argument<Int>("width") ?: return result.error("INVALID_ARGS", "Missing width", null)
            val height = call.argument<Int>("height") ?: return result.error("INVALID_ARGS", "Missing height", null)

            val textureEntry = textureEntries[textureId]
            if (textureEntry != null) {
                textureEntry.surfaceTexture().setDefaultBufferSize(width, height)
                nativeResizeTexture(textureId, width, height)
                result.success(null)
            } else {
                result.error("TEXTURE_NOT_FOUND", "Texture not found", null)
            }
        } catch (e: Exception) {
            result.error("RESIZE_ERROR", e.message, null)
        }
    }

    private fun disposeTexture(call: MethodCall, result: Result) {
        try {
            val textureId = call.argument<Int>("textureId") ?: return result.error("INVALID_ARGS", "Missing textureId", null)

            // Dispose native texture
            nativeDisposeTexture(textureId)
            
            // Clean up Flutter resources
            textureEntries[textureId]?.release()
            textureEntries.remove(textureId)
            
            surfaces[textureId]?.release()
            surfaces.remove(textureId)
            
            result.success(null)
        } catch (e: Exception) {
            result.error("DISPOSE_ERROR", e.message, null)
        }
    }

    private fun feedTick(call: MethodCall, result: Result) {
        try {
            val textureId = call.argument<Int>("textureId") ?: return result.error("INVALID_ARGS", "Missing textureId", null)
            val price = call.argument<Double>("price") ?: return result.error("INVALID_ARGS", "Missing price", null)
            val timestamp = call.argument<Long>("timestamp") ?: return result.error("INVALID_ARGS", "Missing timestamp", null)

            nativeFeedTick(textureId, price, timestamp)
            result.success(null)
        } catch (e: Exception) {
            result.error("FEED_TICK_ERROR", e.message, null)
        }
    }

    private fun setHighPrice(call: MethodCall, result: Result) {
        try {
            val textureId = call.argument<Int>("textureId") ?: return result.error("INVALID_ARGS", "Missing textureId", null)
            val high = call.argument<Double>("high") ?: return result.error("INVALID_ARGS", "Missing high", null)

            nativeSetHighPrice(textureId, high)
            result.success(null)
        } catch (e: Exception) {
            result.error("SET_HIGH_PRICE_ERROR", e.message, null)
        }
    }

    private fun setStartTime(call: MethodCall, result: Result) {
        try {
            val textureId = call.argument<Int>("textureId") ?: return result.error("INVALID_ARGS", "Missing textureId", null)
            val timestamp = call.argument<Long>("timestamp") ?: return result.error("INVALID_ARGS", "Missing timestamp", null)

            nativeSetStartTime(textureId, timestamp)
            result.success(null)
        } catch (e: Exception) {
            result.error("SET_START_TIME_ERROR", e.message, null)
        }
    }

    private fun setZoomScales(call: MethodCall, result: Result) {
        try {
            val textureId = call.argument<Int>("textureId") ?: return result.error("INVALID_ARGS", "Missing textureId", null)
            val horizontalZoom = call.argument<Double>("horizontalZoom") ?: return result.error("INVALID_ARGS", "Missing horizontalZoom", null)
            val verticalZoom = call.argument<Double>("verticalZoom") ?: return result.error("INVALID_ARGS", "Missing verticalZoom", null)

            nativeSetZoomScales(textureId, horizontalZoom, verticalZoom)
            result.success(null)
        } catch (e: Exception) {
            result.error("SET_ZOOM_SCALES_ERROR", e.message, null)
        }
    }

    private fun setPanOffsets(call: MethodCall, result: Result) {
        try {
            val textureId = call.argument<Int>("textureId") ?: return result.error("INVALID_ARGS", "Missing textureId", null)
            val horizontalOffset = call.argument<Double>("horizontalOffset") ?: return result.error("INVALID_ARGS", "Missing horizontalOffset", null)
            val verticalOffset = call.argument<Double>("verticalOffset") ?: return result.error("INVALID_ARGS", "Missing verticalOffset", null)

            nativeSetPanOffsets(textureId, horizontalOffset, verticalOffset)
            result.success(null)
        } catch (e: Exception) {
            result.error("SET_PAN_OFFSETS_ERROR", e.message, null)
        }
    }

    private fun getCandlestickCount(call: MethodCall, result: Result) {
        try {
            val textureId = call.argument<Int>("textureId") ?: return result.error("INVALID_ARGS", "Missing textureId", null)

            val count = nativeGetCandlestickCount(textureId)
            result.success(count)
        } catch (e: Exception) {
            result.error("GET_CANDLESTICK_COUNT_ERROR", e.message, null)
        }
    }

    private fun startRenderingLoop(textureId: Int) {
        Thread {
            while (textureEntries.containsKey(textureId)) {
                nativeRenderFrame(textureId)
                Thread.sleep(16) // ~60 FPS
            }
        }.start()
    }

    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        channel.setMethodCallHandler(null)
        
        // Clean up all textures
        textureEntries.values.forEach { it.release() }
        textureEntries.clear()
        
        surfaces.values.forEach { it.release() }
        surfaces.clear()
    }

    // Native method declarations
    private external fun nativeCreateTexture(
        surface: Surface, width: Int, height: Int, timeIntervalSeconds: Int,
        includePriceAxis: Boolean, includeTimeAxis: Boolean, 
        priceAxisWidth: Int, timeAxisHeight: Int
    ): Int

    private external fun nativeResizeTexture(textureId: Int, width: Int, height: Int)
    private external fun nativeDisposeTexture(textureId: Int)
    private external fun nativeRenderFrame(textureId: Int)
    private external fun nativeFeedTick(textureId: Int, price: Double, timestamp: Long)
    private external fun nativeSetHighPrice(textureId: Int, high: Double)
    private external fun nativeSetStartTime(textureId: Int, timestamp: Long)
    private external fun nativeSetZoomScales(textureId: Int, horizontalZoom: Double, verticalZoom: Double)
    private external fun nativeSetPanOffsets(textureId: Int, horizontalOffset: Double, verticalOffset: Double)
    private external fun nativeGetCandlestickCount(textureId: Int): Int
}
