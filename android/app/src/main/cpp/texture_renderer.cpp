#include "texture_renderer.h"
#include "chart_engine_rust.h"
#include <android/log.h>
#include <vector>
#include <algorithm>

#define LOG_TAG "TextureRenderer"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

// Shader sources
const char* TextureRenderer::vertexShaderSource_ = R"(
attribute vec2 a_position;
attribute vec3 a_color;
varying vec3 v_color;
uniform mat4 u_mvpMatrix;

void main() {
    gl_Position = u_mvpMatrix * vec4(a_position, 0.0, 1.0);
    v_color = a_color;
}
)";

const char* TextureRenderer::fragmentShaderSource_ = R"(
precision mediump float;
varying vec3 v_color;

void main() {
    gl_FragColor = vec4(v_color, 1.0);
}
)";

// Indicator shader sources
const char* TextureRenderer::indicatorVertexShaderSource_ = R"(
attribute vec2 a_position;
uniform vec4 u_color;
uniform mat4 u_mvpMatrix;
varying vec4 v_color;

void main() {
    gl_Position = u_mvpMatrix * vec4(a_position, 0.0, 1.0);
    v_color = u_color;
}
)";

const char* TextureRenderer::indicatorFragmentShaderSource_ = R"(
precision mediump float;
varying vec4 v_color;

void main() {
    gl_FragColor = v_color;
}
)";

TextureRenderer::TextureRenderer()
    : display_(EGL_NO_DISPLAY), context_(EGL_NO_CONTEXT), surface_(EGL_NO_SURFACE),
      window_(nullptr), width_(0), height_(0), timeIntervalSeconds_(300),
      includePriceAxis_(false), includeTimeAxis_(false), priceAxisWidth_(50),
      timeAxisHeight_(30), shaderProgram_(0), vertexBuffer_(0), indexBuffer_(0),
      indicatorShaderProgram_(0), indicatorVertexBuffer_(0) {
}

TextureRenderer::~TextureRenderer() {
    cleanup();
}

bool TextureRenderer::initialize(ANativeWindow* window, int width, int height,
                                int timeIntervalSeconds, bool includePriceAxis,
                                bool includeTimeAxis, int priceAxisWidth, int timeAxisHeight) {
    window_ = window;
    width_ = width;
    height_ = height;
    timeIntervalSeconds_ = timeIntervalSeconds;
    includePriceAxis_ = includePriceAxis;
    includeTimeAxis_ = includeTimeAxis;
    priceAxisWidth_ = priceAxisWidth;
    timeAxisHeight_ = timeAxisHeight;
    
    if (!initializeEGL()) {
        LOGE("Failed to initialize EGL");
        return false;
    }
    
    if (!createShaders()) {
        LOGE("Failed to create shaders");
        return false;
    }

    if (!createIndicatorShaders()) {
        LOGE("Failed to create indicator shaders");
        return false;
    }
    
    // Generate buffers
    glGenBuffers(1, &vertexBuffer_);
    glGenBuffers(1, &indexBuffer_);
    
    // Set viewport
    glViewport(0, 0, width_, height_);
    
    // Enable blending for transparency
    glEnable(GL_BLEND);
    glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);
    
    LOGI("TextureRenderer initialized successfully");
    return true;
}

bool TextureRenderer::initializeEGL() {
    display_ = eglGetDisplay(EGL_DEFAULT_DISPLAY);
    if (display_ == EGL_NO_DISPLAY) {
        LOGE("eglGetDisplay failed");
        return false;
    }
    
    if (!eglInitialize(display_, nullptr, nullptr)) {
        LOGE("eglInitialize failed");
        return false;
    }
    
    // Choose config
    EGLint configAttribs[] = {
        EGL_SURFACE_TYPE, EGL_WINDOW_BIT,
        EGL_RENDERABLE_TYPE, EGL_OPENGL_ES2_BIT,
        EGL_RED_SIZE, 8,
        EGL_GREEN_SIZE, 8,
        EGL_BLUE_SIZE, 8,
        EGL_ALPHA_SIZE, 8,
        EGL_DEPTH_SIZE, 16,
        EGL_NONE
    };
    
    EGLint numConfigs;
    if (!eglChooseConfig(display_, configAttribs, &config_, 1, &numConfigs)) {
        LOGE("eglChooseConfig failed");
        return false;
    }
    
    // Create surface
    surface_ = eglCreateWindowSurface(display_, config_, window_, nullptr);
    if (surface_ == EGL_NO_SURFACE) {
        LOGE("eglCreateWindowSurface failed");
        return false;
    }
    
    // Create context
    EGLint contextAttribs[] = {
        EGL_CONTEXT_CLIENT_VERSION, 2,
        EGL_NONE
    };
    
    context_ = eglCreateContext(display_, config_, EGL_NO_CONTEXT, contextAttribs);
    if (context_ == EGL_NO_CONTEXT) {
        LOGE("eglCreateContext failed");
        return false;
    }
    
    // Make current
    if (!eglMakeCurrent(display_, surface_, surface_, context_)) {
        LOGE("eglMakeCurrent failed");
        return false;
    }
    
    return true;
}

bool TextureRenderer::createShaders() {
    GLuint vertexShader = compileShader(GL_VERTEX_SHADER, vertexShaderSource_);
    if (vertexShader == 0) return false;
    
    GLuint fragmentShader = compileShader(GL_FRAGMENT_SHADER, fragmentShaderSource_);
    if (fragmentShader == 0) {
        glDeleteShader(vertexShader);
        return false;
    }
    
    shaderProgram_ = glCreateProgram();
    glAttachShader(shaderProgram_, vertexShader);
    glAttachShader(shaderProgram_, fragmentShader);
    glLinkProgram(shaderProgram_);
    
    GLint linkStatus;
    glGetProgramiv(shaderProgram_, GL_LINK_STATUS, &linkStatus);
    if (linkStatus != GL_TRUE) {
        GLchar infoLog[512];
        glGetProgramInfoLog(shaderProgram_, 512, nullptr, infoLog);
        LOGE("Shader program linking failed: %s", infoLog);
        glDeleteShader(vertexShader);
        glDeleteShader(fragmentShader);
        return false;
    }
    
    glDeleteShader(vertexShader);
    glDeleteShader(fragmentShader);
    
    return true;
}

bool TextureRenderer::createIndicatorShaders() {
    GLuint vertexShader = compileShader(GL_VERTEX_SHADER, indicatorVertexShaderSource_);
    if (vertexShader == 0) return false;

    GLuint fragmentShader = compileShader(GL_FRAGMENT_SHADER, indicatorFragmentShaderSource_);
    if (fragmentShader == 0) {
        glDeleteShader(vertexShader);
        return false;
    }

    indicatorShaderProgram_ = glCreateProgram();
    glAttachShader(indicatorShaderProgram_, vertexShader);
    glAttachShader(indicatorShaderProgram_, fragmentShader);
    glLinkProgram(indicatorShaderProgram_);

    GLint linkStatus;
    glGetProgramiv(indicatorShaderProgram_, GL_LINK_STATUS, &linkStatus);
    if (linkStatus != GL_TRUE) {
        GLchar infoLog[512];
        glGetProgramInfoLog(indicatorShaderProgram_, 512, nullptr, infoLog);
        LOGE("Indicator shader program linking failed: %s", infoLog);
        glDeleteShader(vertexShader);
        glDeleteShader(fragmentShader);
        return false;
    }

    glDeleteShader(vertexShader);
    glDeleteShader(fragmentShader);

    // Generate indicator vertex buffer
    glGenBuffers(1, &indicatorVertexBuffer_);

    return true;
}

GLuint TextureRenderer::compileShader(GLenum type, const char* source) {
    GLuint shader = glCreateShader(type);
    glShaderSource(shader, 1, &source, nullptr);
    glCompileShader(shader);
    
    GLint compileStatus;
    glGetShaderiv(shader, GL_COMPILE_STATUS, &compileStatus);
    if (compileStatus != GL_TRUE) {
        GLchar infoLog[512];
        glGetShaderInfoLog(shader, 512, nullptr, infoLog);
        LOGE("Shader compilation failed: %s", infoLog);
        glDeleteShader(shader);
        return 0;
    }
    
    return shader;
}

void TextureRenderer::resize(int width, int height) {
    width_ = width;
    height_ = height;
    glViewport(0, 0, width_, height_);
}

void TextureRenderer::render(usize chartEngineId) {
    if (chartEngineId == 0) return;
    
    // Clear the screen
    glClearColor(0.1f, 0.1f, 0.15f, 1.0f);
    glClear(GL_COLOR_BUFFER_BIT);
    
    // Use shader program
    glUseProgram(shaderProgram_);
    
    // Get candlestick data
    int candleCount = chart_engine_get_candlestick_count(chartEngineId);
    if (candleCount == 0) {
        eglSwapBuffers(display_, surface_);
        return;
    }

    // Prepare vertex data for candlesticks
    std::vector<float> vertices;
    std::vector<unsigned int> indices;

    double minPrice = chart_engine_get_min_price(chartEngineId);
    double maxPrice = chart_engine_get_max_price(chartEngineId);
    double priceRange = maxPrice - minPrice;
    
    if (priceRange <= 0.0) {
        eglSwapBuffers(display_, surface_);
        return;
    }
    
    float candleWidth = 8.0f;
    float spacing = 2.0f;
    float totalWidth = candleWidth + spacing;
    
    // Calculate chart area
    float chartWidth = static_cast<float>(width_) - (includePriceAxis_ ? priceAxisWidth_ : 0);
    float chartHeight = static_cast<float>(height_) - (includeTimeAxis_ ? timeAxisHeight_ : 0);
    
    for (int i = 0; i < candleCount; ++i) {
        Candlestick candle;
        if (chart_engine_get_candlestick(chartEngineId, i, &candle) == 0) {
            continue; // Skip if failed to get candlestick
        }
        
        // Calculate positions
        float x = chartWidth - (candleCount - i) * totalWidth;
        float openY = chartHeight * (1.0f - static_cast<float>((candle.open - minPrice) / priceRange));
        float closeY = chartHeight * (1.0f - static_cast<float>((candle.close - minPrice) / priceRange));
        float highY = chartHeight * (1.0f - static_cast<float>((candle.high - minPrice) / priceRange));
        float lowY = chartHeight * (1.0f - static_cast<float>((candle.low - minPrice) / priceRange));
        
        // Convert to normalized coordinates (-1 to 1)
        x = (x / chartWidth) * 2.0f - 1.0f;
        openY = (openY / chartHeight) * 2.0f - 1.0f;
        closeY = (closeY / chartHeight) * 2.0f - 1.0f;
        highY = (highY / chartHeight) * 2.0f - 1.0f;
        lowY = (lowY / chartHeight) * 2.0f - 1.0f;
        
        float candleWidthNorm = (candleWidth / chartWidth) * 2.0f;
        
        // Color based on candle direction
        float r = candle.close >= candle.open ? 0.0f : 1.0f;
        float g = candle.close >= candle.open ? 1.0f : 0.0f;
        float b = 0.0f;
        
        // Add wick vertices (line from high to low)
        unsigned int baseIndex = vertices.size() / 5; // 5 components per vertex (x, y, r, g, b)
        
        // Wick
        vertices.insert(vertices.end(), {
            x + candleWidthNorm / 2.0f, highY, r, g, b,
            x + candleWidthNorm / 2.0f, lowY, r, g, b
        });
        
        indices.insert(indices.end(), {baseIndex, baseIndex + 1});
        
        // Body rectangle
        float bodyTop = std::max(openY, closeY);
        float bodyBottom = std::min(openY, closeY);
        
        baseIndex = vertices.size() / 5;
        vertices.insert(vertices.end(), {
            x, bodyTop, r, g, b,
            x + candleWidthNorm, bodyTop, r, g, b,
            x + candleWidthNorm, bodyBottom, r, g, b,
            x, bodyBottom, r, g, b
        });
        
        indices.insert(indices.end(), {
            baseIndex, baseIndex + 1, baseIndex + 2,
            baseIndex, baseIndex + 2, baseIndex + 3
        });
    }
    
    if (!vertices.empty()) {
        // Upload vertex data
        glBindBuffer(GL_ARRAY_BUFFER, vertexBuffer_);
        glBufferData(GL_ARRAY_BUFFER, vertices.size() * sizeof(float), vertices.data(), GL_DYNAMIC_DRAW);
        
        glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, indexBuffer_);
        glBufferData(GL_ELEMENT_ARRAY_BUFFER, indices.size() * sizeof(unsigned int), indices.data(), GL_DYNAMIC_DRAW);
        
        // Set vertex attributes
        GLint positionAttrib = glGetAttribLocation(shaderProgram_, "a_position");
        GLint colorAttrib = glGetAttribLocation(shaderProgram_, "a_color");
        
        glEnableVertexAttribArray(positionAttrib);
        glVertexAttribPointer(positionAttrib, 2, GL_FLOAT, GL_FALSE, 5 * sizeof(float), (void*)0);
        
        glEnableVertexAttribArray(colorAttrib);
        glVertexAttribPointer(colorAttrib, 3, GL_FLOAT, GL_FALSE, 5 * sizeof(float), (void*)(2 * sizeof(float)));
        
        // Set MVP matrix (identity for now)
        GLint mvpLocation = glGetUniformLocation(shaderProgram_, "u_mvpMatrix");
        float identity[16] = {
            1.0f, 0.0f, 0.0f, 0.0f,
            0.0f, 1.0f, 0.0f, 0.0f,
            0.0f, 0.0f, 1.0f, 0.0f,
            0.0f, 0.0f, 0.0f, 1.0f
        };
        glUniformMatrix4fv(mvpLocation, 1, GL_FALSE, identity);
        
        // Draw
        glDrawElements(GL_TRIANGLES, static_cast<GLsizei>(indices.size()), GL_UNSIGNED_INT, 0);
        
        glDisableVertexAttribArray(positionAttrib);
        glDisableVertexAttribArray(colorAttrib);
    }

    // Render indicators on top of candlesticks
    renderIndicators(chartEngineId);

    // Swap buffers
    eglSwapBuffers(display_, surface_);
}

void TextureRenderer::renderIndicators(usize chartEngineId) {
    if (chartEngineId == 0) return;

    // Check if we have indicators
    int indicatorCount = chart_engine_get_indicator_count(chartEngineId);
    if (indicatorCount == 0) return;

    // Use indicator shader program
    glUseProgram(indicatorShaderProgram_);

    // Set MVP matrix (identity for now)
    GLint mvpLocation = glGetUniformLocation(indicatorShaderProgram_, "u_mvpMatrix");
    float identity[16] = {
        1.0f, 0.0f, 0.0f, 0.0f,
        0.0f, 1.0f, 0.0f, 0.0f,
        0.0f, 0.0f, 1.0f, 0.0f,
        0.0f, 0.0f, 0.0f, 1.0f
    };
    glUniformMatrix4fv(mvpLocation, 1, GL_FALSE, identity);

    // Get chart dimensions
    double minPrice = chart_engine_get_min_price(chartEngineId);
    double maxPrice = chart_engine_get_max_price(chartEngineId);
    double priceRange = maxPrice - minPrice;

    if (priceRange <= 0.0) return;

    // TODO: Implement indicator rendering using Rust FFI
    // For now, we'll skip indicator rendering to get basic chart working
    // The indicator system will need to be implemented using the Rust FFI functions:
    // - chart_engine_get_indicator_count()
    // - chart_engine_get_indicator_name()
    // - chart_engine_get_indicator_data_count()
    // - chart_engine_get_indicator_data_point()

    (void)minPrice;  // Suppress unused variable warning
    (void)maxPrice;  // Suppress unused variable warning
    (void)priceRange; // Suppress unused variable warning
}

void TextureRenderer::renderIndicatorLine(const std::vector<float>& vertices,
                                        float r, float g, float b, float a) {
    if (vertices.size() < 4) return; // Need at least 2 points

    // Set color uniform
    GLint colorLocation = glGetUniformLocation(indicatorShaderProgram_, "u_color");
    glUniform4f(colorLocation, r, g, b, a);

    // Upload vertex data
    glBindBuffer(GL_ARRAY_BUFFER, indicatorVertexBuffer_);
    glBufferData(GL_ARRAY_BUFFER, vertices.size() * sizeof(float), vertices.data(), GL_DYNAMIC_DRAW);

    // Set vertex attributes
    GLint positionAttrib = glGetAttribLocation(indicatorShaderProgram_, "a_position");
    glEnableVertexAttribArray(positionAttrib);
    glVertexAttribPointer(positionAttrib, 2, GL_FLOAT, GL_FALSE, 2 * sizeof(float), (void*)0);

    // Set line width
    glLineWidth(2.0f);

    // Draw line strip
    glDrawArrays(GL_LINE_STRIP, 0, static_cast<GLsizei>(vertices.size() / 2));
    glDisableVertexAttribArray(positionAttrib);
}

void TextureRenderer::renderIndicatorHistogram(const std::vector<float>& vertices,
                                             float r, float g, float b, float a) {
    // TODO: Implement histogram rendering for indicators like MACD histogram
    // This would render vertical bars instead of lines
}

void TextureRenderer::cleanup() {
    if (shaderProgram_ != 0) {
        glDeleteProgram(shaderProgram_);
        shaderProgram_ = 0;
    }

    if (indicatorShaderProgram_ != 0) {
        glDeleteProgram(indicatorShaderProgram_);
        indicatorShaderProgram_ = 0;
    }

    if (vertexBuffer_ != 0) {
        glDeleteBuffers(1, &vertexBuffer_);
        vertexBuffer_ = 0;
    }

    if (indicatorVertexBuffer_ != 0) {
        glDeleteBuffers(1, &indicatorVertexBuffer_);
        indicatorVertexBuffer_ = 0;
    }

    if (indexBuffer_ != 0) {
        glDeleteBuffers(1, &indexBuffer_);
        indexBuffer_ = 0;
    }
    
    if (context_ != EGL_NO_CONTEXT) {
        eglDestroyContext(display_, context_);
        context_ = EGL_NO_CONTEXT;
    }
    
    if (surface_ != EGL_NO_SURFACE) {
        eglDestroySurface(display_, surface_);
        surface_ = EGL_NO_SURFACE;
    }
    
    if (display_ != EGL_NO_DISPLAY) {
        eglTerminate(display_);
        display_ = EGL_NO_DISPLAY;
    }
}

void TextureRenderer::destroy() {
    cleanup();
}
