#include <jni.h>
#include <android/native_window.h>
#include <android/native_window_jni.h>
#include <android/log.h>
#include <map>
#include <memory>
#include "texture_renderer.h"
#include "chart_engine_rust.h"

#define LOG_TAG "ChartViewJNI"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

// Global storage for texture renderers and chart engine IDs
static std::map<int, std::unique_ptr<TextureRenderer>> g_renderers;
static std::map<int, usize> g_chartEngineIds;
static int g_nextTextureId = 1;

extern "C" {

JNIEXPORT jint JNICALL
Java_com_undeclab_abra_ChartViewPlugin_nativeCreateTexture(
    JNIEnv* env, jobject /* this */, jobject surface, jint width, jint height,
    jint timeIntervalSeconds, jboolean includePriceAxis, jboolean includeTimeAxis,
    jint priceAxisWidth, jint timeAxisHeight) {
    
    LOGI("Creating texture: %dx%d", width, height);
    
    // Get native window from surface
    ANativeWindow* window = ANativeWindow_fromSurface(env, surface);
    if (!window) {
        LOGE("Failed to get native window from surface");
        return -1;
    }
    
    // Create texture renderer
    auto renderer = std::make_unique<TextureRenderer>();
    if (!renderer->initialize(window, width, height, timeIntervalSeconds,
                             includePriceAxis, includeTimeAxis, priceAxisWidth, timeAxisHeight)) {
        LOGE("Failed to initialize texture renderer");
        ANativeWindow_release(window);
        return -1;
    }
    
    // Create chart engine using Rust FFI
    usize engineId = create_chart_engine();
    if (engineId == 0) {
        LOGE("Failed to create chart engine");
        ANativeWindow_release(window);
        return -1;
    }
    chart_engine_set_time_interval(engineId, timeIntervalSeconds);

    int textureId = g_nextTextureId++;
    g_renderers[textureId] = std::move(renderer);
    g_chartEngineIds[textureId] = engineId;
    
    LOGI("Created texture with ID: %d", textureId);
    return textureId;
}

JNIEXPORT void JNICALL
Java_com_undeclab_abra_ChartViewPlugin_nativeResizeTexture(
    JNIEnv* /* env */, jobject /* this */, jint textureId, jint width, jint height) {
    
    LOGI("Resizing texture %d to %dx%d", textureId, width, height);
    
    auto it = g_renderers.find(textureId);
    if (it != g_renderers.end()) {
        it->second->resize(width, height);
    } else {
        LOGE("Texture ID %d not found for resize", textureId);
    }
}

JNIEXPORT void JNICALL
Java_com_undeclab_abra_ChartViewPlugin_nativeDisposeTexture(
    JNIEnv* /* env */, jobject /* this */, jint textureId) {
    
    LOGI("Disposing texture %d", textureId);
    
    auto rendererIt = g_renderers.find(textureId);
    if (rendererIt != g_renderers.end()) {
        rendererIt->second->destroy();
        g_renderers.erase(rendererIt);
    }
    
    auto engineIt = g_chartEngineIds.find(textureId);
    if (engineIt != g_chartEngineIds.end()) {
        destroy_chart_engine(engineIt->second);
        g_chartEngineIds.erase(engineIt);
    }
}

JNIEXPORT void JNICALL
Java_com_undeclab_abra_ChartViewPlugin_nativeRenderFrame(
    JNIEnv* /* env */, jobject /* this */, jint textureId) {
    
    auto rendererIt = g_renderers.find(textureId);
    auto engineIt = g_chartEngineIds.find(textureId);

    if (rendererIt != g_renderers.end() && engineIt != g_chartEngineIds.end()) {
        rendererIt->second->render(engineIt->second);
    }
}

JNIEXPORT void JNICALL
Java_com_undeclab_abra_ChartViewPlugin_nativeFeedTick(
    JNIEnv* /* env */, jobject /* this */, jint textureId, jdouble price, jlong timestamp) {
    
    auto engineIt = g_chartEngineIds.find(textureId);
    if (engineIt != g_chartEngineIds.end()) {
        chart_engine_feed_tick(engineIt->second, price, timestamp);
    }
}

JNIEXPORT void JNICALL
Java_com_undeclab_abra_ChartViewPlugin_nativeSetHighPrice(
    JNIEnv* /* env */, jobject /* this */, jint textureId, jdouble high) {
    
    auto engineIt = g_chartEngineIds.find(textureId);
    if (engineIt != g_chartEngineIds.end()) {
        chart_engine_set_high_price(engineIt->second, high);
    }
}

JNIEXPORT void JNICALL
Java_com_undeclab_abra_ChartViewPlugin_nativeSetStartTime(
    JNIEnv* /* env */, jobject /* this */, jint textureId, jlong timestamp) {
    
    auto engineIt = g_chartEngineIds.find(textureId);
    if (engineIt != g_chartEngineIds.end()) {
        chart_engine_set_start_time(engineIt->second, timestamp);
    }
}

JNIEXPORT void JNICALL
Java_com_undeclab_abra_ChartViewPlugin_nativeSetZoomScales(
    JNIEnv* /* env */, jobject /* this */, jint textureId, jdouble horizontalZoom, jdouble verticalZoom) {
    
    auto engineIt = g_chartEngineIds.find(textureId);
    if (engineIt != g_chartEngineIds.end()) {
        chart_engine_set_zoom_scales(engineIt->second, horizontalZoom, verticalZoom);
    }
}

JNIEXPORT void JNICALL
Java_com_undeclab_abra_ChartViewPlugin_nativeSetPanOffsets(
    JNIEnv* /* env */, jobject /* this */, jint textureId, jdouble horizontalOffset, jdouble verticalOffset) {
    
    auto engineIt = g_chartEngineIds.find(textureId);
    if (engineIt != g_chartEngineIds.end()) {
        chart_engine_set_pan_offsets(engineIt->second, horizontalOffset, verticalOffset);
    }
}

JNIEXPORT jint JNICALL
Java_com_undeclab_abra_ChartViewPlugin_nativeGetCandlestickCount(
    JNIEnv* /* env */, jobject /* this */, jint textureId) {
    
    auto engineIt = g_chartEngineIds.find(textureId);
    if (engineIt != g_chartEngineIds.end()) {
        return chart_engine_get_candlestick_count(engineIt->second);
    }
    return 0;
}

} // extern "C"
