cmake_minimum_required(VERSION 3.22.1)
project(native-lib)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
find_library(log-lib log)
find_library(android-lib android)
find_library(EGL-lib EGL)
find_library(GLESv2-lib GLESv2)

# Native source directories
set(NATIVE_DIR "${CMAKE_CURRENT_SOURCE_DIR}/../../../../../native")
set(RUST_LIB_DIR "${NATIVE_DIR}/android_libs")

# Determine the Android ABI
if(ANDROID_ABI STREQUAL "arm64-v8a")
    set(RUST_TARGET_DIR "${RUST_LIB_DIR}/arm64-v8a")
elseif(ANDROID_ABI STREQUAL "armeabi-v7a")
    set(RUST_TARGET_DIR "${RUST_LIB_DIR}/armeabi-v7a")
elseif(ANDROID_ABI STREQUAL "x86_64")
    set(RUST_TARGET_DIR "${RUST_LIB_DIR}/x86_64")
elseif(ANDROID_ABI STREQUAL "x86")
    set(RUST_TARGET_DIR "${RUST_LIB_DIR}/x86")
else()
    message(FATAL_ERROR "Unsupported Android ABI: ${ANDROID_ABI}")
endif()

# Check if Rust library exists
set(RUST_LIB_PATH "${RUST_TARGET_DIR}/libchart_engine_rust.so")
if(NOT EXISTS ${RUST_LIB_PATH})
    message(FATAL_ERROR "Rust library not found at ${RUST_LIB_PATH}. Please run build_android.sh first.")
endif()

# Import the Rust library
add_library(chart_engine_rust SHARED IMPORTED)
set_target_properties(chart_engine_rust PROPERTIES
    IMPORTED_LOCATION ${RUST_LIB_PATH}
)

# JNI wrapper sources (updated to use Rust instead of C++)
set(JNI_SOURCES
    chart_view_jni.cpp
    texture_renderer.cpp
    texture_renderer.h
)

# Create the native library
add_library(native-lib SHARED
    ${JNI_SOURCES}
)

# Include directories
target_include_directories(native-lib PRIVATE
    ${RUST_LIB_DIR}  # For Rust FFI headers
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Link libraries
target_link_libraries(native-lib
    chart_engine_rust  # Link against the Rust library
    ${log-lib}
    ${android-lib}
    ${EGL-lib}
    ${GLESv2-lib}
)

# Compiler flags
target_compile_options(native-lib PRIVATE
    -Wall
    -Wextra
    -O3
    -ffunction-sections
    -fdata-sections
)

# Linker flags
set_target_properties(native-lib PROPERTIES
    LINK_FLAGS "-Wl,--gc-sections"
)
