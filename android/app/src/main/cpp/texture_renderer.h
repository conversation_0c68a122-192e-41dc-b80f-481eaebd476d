#ifndef TEXTURE_RENDERER_H
#define TEXTURE_RENDERER_H

#include <EGL/egl.h>
#include <GLES2/gl2.h>
#include <android/native_window.h>
#include <memory>
#include <cstddef>

// Forward declaration for Rust engine ID type
using usize = std::size_t;

class TextureRenderer {
private:
    EGLDisplay display_;
    EGLContext context_;
    EGLSurface surface_;
    EGLConfig config_;
    ANativeWindow* window_;
    
    int width_;
    int height_;
    int timeIntervalSeconds_;
    bool includePriceAxis_;
    bool includeTimeAxis_;
    int priceAxisWidth_;
    int timeAxisHeight_;
    
    GLuint shaderProgram_;
    GLuint vertexBuffer_;
    GLuint indexBuffer_;

    // Indicator rendering resources
    GLuint indicatorShaderProgram_;
    GLuint indicatorVertexBuffer_;

    // Shader sources
    static const char* vertexShaderSource_;
    static const char* fragmentShaderSource_;
    static const char* indicatorVertexShaderSource_;
    static const char* indicatorFragmentShaderSource_;
    
    // Helper methods
    bool initializeEGL();
    bool createShaders();
    bool createIndicatorShaders();
    GLuint compileShader(GLenum type, const char* source);
    void cleanup();

    // Indicator rendering methods
    void renderIndicators(usize chartEngineId);
    void renderIndicatorLine(const std::vector<float>& vertices, float r, float g, float b, float a);
    void renderIndicatorHistogram(const std::vector<float>& vertices, float r, float g, float b, float a);
    
public:
    TextureRenderer();
    ~TextureRenderer();
    
    bool initialize(ANativeWindow* window, int width, int height, 
                   int timeIntervalSeconds, bool includePriceAxis, 
                   bool includeTimeAxis, int priceAxisWidth, int timeAxisHeight);
    
    void resize(int width, int height);
    void render(usize chartEngineId);
    void destroy();
    
    // Getters
    int getWidth() const { return width_; }
    int getHeight() const { return height_; }
};

#endif // TEXTURE_RENDERER_H
