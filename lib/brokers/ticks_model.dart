// Represents a single trade tick
import '../presentation/symbol/models/symbol_models.dart';

class Tick {
  final String symbol;
  final double price;
  final double size;
  final DateTime timestamp;
  final String exchange;

  Tick({
    required this.symbol,
    required this.price,
    required this.size,
    required this.timestamp,
    required this.exchange,
  });
}

/// Represents a candlestick (OHLCV)
class Candle {
  final double open;
  final double high;
  final double low;
  final double close;
  final double volume;
  final DateTime timestamp;

  Candle({
    required this.open,
    required this.high,
    required this.low,
    required this.close,
    required this.volume,
    required this.timestamp,
  });

  Candle copyWith({
    double? open,
    double? high,
    double? low,
    double? close,
    double? volume,
    DateTime? timestamp,
  }) {
    return Candle(
      open: open ?? this.open,
      high: high ?? this.high,
      low: low ?? this.low,
      close: close ?? this.close,
      volume: volume ?? this.volume,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  static Candle fromPolygonJson(Map<String, dynamic> json) {
    return Candle(
      open: (json['o'] as num).toDouble(),
      high: (json['h'] as num).toDouble(),
      low: (json['l'] as num).toDouble(),
      close: (json['c'] as num).toDouble(),
      volume: (json['v'] as num).toDouble(),
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['t'] as int),
    );
  }
}

class CachedQuote {
  final SymbolMeta data;
  final DateTime timestamp;

  CachedQuote(this.data, this.timestamp);

  bool isValid(Duration cacheDuration) {
    return DateTime.now().difference(timestamp) < cacheDuration;
  }
}
