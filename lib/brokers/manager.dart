/// Broker connection model
class BrokerConnection {
  final String brokerId;
  final String brokerName;
  final BrokerConnectionStatus status;
  final DateTime connectedAt;
  final Map<String, dynamic> credentials;

  BrokerConnection({
    required this.brokerId,
    required this.brokerName,
    required this.status,
    required this.connectedAt,
    required this.credentials,
  });

  BrokerConnection copyWith({
    String? brokerId,
    String? brokerName,
    BrokerConnectionStatus? status,
    DateTime? connectedAt,
    Map<String, dynamic>? credentials,
  }) {
    return BrokerConnection(
      brokerId: brokerId ?? this.brokerId,
      brokerName: brokerName ?? this.brokerName,
      status: status ?? this.status,
      connectedAt: connectedAt ?? this.connectedAt,
      credentials: credentials ?? this.credentials,
    );
  }
}

/// Broker connection status
enum BrokerConnectionStatus {
  connecting,
  connected,
  disconnecting,
  disconnected,
  error,
}

/// Broker connection result
class BrokerConnectionResult {
  final BrokerConnection? connection;
  final String? error;
  final bool isSuccess;

  BrokerConnectionResult._(this.connection, this.error, this.isSuccess);

  factory BrokerConnectionResult.success(BrokerConnection connection) {
    return BrokerConnectionResult._(connection, null, true);
  }

  factory BrokerConnectionResult.failure(String error) {
    return BrokerConnectionResult._(null, error, false);
  }
}

/// Order request model
class OrderRequest {
  final String symbol;
  final OrderSide side;
  final double quantity;
  final OrderType orderType;
  final double? price;
  final double? stopPrice;
  final DateTime? timeInForce;

  OrderRequest({
    required this.symbol,
    required this.side,
    required this.quantity,
    required this.orderType,
    this.price,
    this.stopPrice,
    this.timeInForce,
  });
}

/// Order model
class Order {
  final String id;
  final String brokerId;
  final String symbol;
  final OrderSide side;
  final double quantity;
  final OrderType orderType;
  final double? price;
  final double? stopPrice;
  final OrderStatus status;
  final DateTime submittedAt;
  final DateTime? filledAt;
  final double? filledPrice;
  final double? filledQuantity;

  Order({
    required this.id,
    required this.brokerId,
    required this.symbol,
    required this.side,
    required this.quantity,
    required this.orderType,
    this.price,
    this.stopPrice,
    required this.status,
    required this.submittedAt,
    this.filledAt,
    this.filledPrice,
    this.filledQuantity,
  });
}

/// Order side
enum OrderSide { buy, sell }

/// Order type
enum OrderType { market, limit, stopLoss, stopLimit }

/// Order status
enum OrderStatus { submitted, pending, filled, cancelled, rejected }

/// Order result
class OrderResult {
  final Order? order;
  final String? error;
  final bool isSuccess;

  OrderResult._(this.order, this.error, this.isSuccess);

  factory OrderResult.success(Order? order) {
    return OrderResult._(order, null, true);
  }

  factory OrderResult.failure(String error) {
    return OrderResult._(null, error, false);
  }
}

/// Order validation
class OrderValidation {
  final bool isValid;
  final String? errorMessage;

  OrderValidation._(this.isValid, this.errorMessage);

  factory OrderValidation.valid() {
    return OrderValidation._(true, null);
  }

  factory OrderValidation.invalid(String errorMessage) {
    return OrderValidation._(false, errorMessage);
  }
}

/// Account information model
class AccountInfo {
  final String brokerId;
  final String accountId;
  final double balance;
  final double availableBalance;
  final double equity;
  final double marginUsed;
  final String currency;
  final DateTime lastUpdated;

  AccountInfo({
    required this.brokerId,
    required this.accountId,
    required this.balance,
    required this.availableBalance,
    required this.equity,
    required this.marginUsed,
    required this.currency,
    required this.lastUpdated,
  });
}

/// Position model
class Position {
  final String brokerId;
  final String symbol;
  final double quantity;
  final double averagePrice;
  final double currentPrice;
  final double unrealizedPnL;
  final PositionSide side;
  final DateTime openedAt;

  Position({
    required this.brokerId,
    required this.symbol,
    required this.quantity,
    required this.averagePrice,
    required this.currentPrice,
    required this.unrealizedPnL,
    required this.side,
    required this.openedAt,
  });
}

/// Position side
enum PositionSide { long, short }

/// Broker event model
class BrokerEvent {
  final String brokerId;
  final BrokerEventType type;
  final DateTime timestamp;
  final Map<String, dynamic> data;

  BrokerEvent._({
    required this.brokerId,
    required this.type,
    required this.timestamp,
    required this.data,
  });

  factory BrokerEvent.connecting(String brokerId) {
    return BrokerEvent._(
      brokerId: brokerId,
      type: BrokerEventType.connecting,
      timestamp: DateTime.now(),
      data: {},
    );
  }

  factory BrokerEvent.connected(String brokerId) {
    return BrokerEvent._(
      brokerId: brokerId,
      type: BrokerEventType.connected,
      timestamp: DateTime.now(),
      data: {},
    );
  }

  factory BrokerEvent.disconnecting(String brokerId) {
    return BrokerEvent._(
      brokerId: brokerId,
      type: BrokerEventType.disconnecting,
      timestamp: DateTime.now(),
      data: {},
    );
  }

  factory BrokerEvent.disconnected(String brokerId) {
    return BrokerEvent._(
      brokerId: brokerId,
      type: BrokerEventType.disconnected,
      timestamp: DateTime.now(),
      data: {},
    );
  }

  factory BrokerEvent.connectionFailed(String brokerId, String error) {
    return BrokerEvent._(
      brokerId: brokerId,
      type: BrokerEventType.connectionFailed,
      timestamp: DateTime.now(),
      data: {'error': error},
    );
  }

  factory BrokerEvent.orderPlaced(String brokerId, Order order) {
    return BrokerEvent._(
      brokerId: brokerId,
      type: BrokerEventType.orderPlaced,
      timestamp: DateTime.now(),
      data: {'order': order},
    );
  }

  factory BrokerEvent.orderCancelled(String brokerId, String orderId) {
    return BrokerEvent._(
      brokerId: brokerId,
      type: BrokerEventType.orderCancelled,
      timestamp: DateTime.now(),
      data: {'orderId': orderId},
    );
  }
}

/// Broker event types
enum BrokerEventType {
  connecting,
  connected,
  disconnecting,
  disconnected,
  connectionFailed,
  orderPlaced,
  orderCancelled,
}