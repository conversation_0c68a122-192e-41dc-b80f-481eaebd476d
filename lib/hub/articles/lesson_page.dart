import 'package:abra/core/constants.dart';
import 'package:abra/hub/articles/lesson_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';


class ArticlesScreen extends StatefulWidget {
  final LessonModule? selectedLesson;

  const ArticlesScreen({super.key, this.selectedLesson});

  @override
  ArticlesScreenState createState() => ArticlesScreenState();
}

class ArticlesScreenState extends State<ArticlesScreen> {
  late List<LessonModule> lessons = [];
  LessonModule? selectedLesson;
  Set<int> completedLessonIds = {};
  Lang language = Lang.sw;

  @override
  void initState() {
    super.initState();
    selectedLesson = widget.selectedLesson;
    loadModules();
  }

  Future<void> loadModules() async {
    final files = [
      {'path': 'assets/courses/beginner_module1-3.json', 'level': LessonLevel.beginner},
      {'path': 'assets/courses/beginner_module4-10.json', 'level': LessonLevel.beginner},
      {'path': 'assets/courses/intermediate_module1-3.json', 'level': LessonLevel.intermediate},
      {'path': 'assets/courses/intermediate_module4-7.json', 'level': LessonLevel.intermediate},
      {'path': 'assets/courses/advanced_module1-4.json', 'level': LessonLevel.advanced},
    ];

    List<LessonModule> loadedModules = [];

    for (final file in files) {
      final jsonString = await rootBundle.loadString(file['path']! as String);
      final dynamic jsonData = json.decode(jsonString);

      List<dynamic> modulesJson;
      if (jsonData is List) {
        // Direct array format
        modulesJson = jsonData;
      } else if (jsonData is Map<String, dynamic> && jsonData.containsKey('modules')) {
        // Object with modules key format
        modulesJson = jsonData['modules'];
      } else {
        // Unknown format, skip this file
        continue;
      }

      loadedModules.addAll(
        modulesJson.map((m) => LessonModule.fromJson(m, file['level']! as LessonLevel)),
      );
    }

    setState(() {
      lessons = loadedModules;
      // If a lesson was passed, find the full object from loadedModules (to get links, etc)
      if (selectedLesson != null) {
        selectedLesson = loadedModules.firstWhere(
          (l) => l.id == selectedLesson!.id,
          orElse: () => selectedLesson!,
        );
      }
    });
  }

  void _toggleLessonCompleted(int lessonId) {
    setState(() {
      completedLessonIds.contains(lessonId)
          ? completedLessonIds.remove(lessonId)
          : completedLessonIds.add(lessonId);
    });
  }

  Future<void> _launchURL(String url) async {
    if (url.isEmpty) return;
    final uri = Uri.tryParse(url);
    if (uri != null && await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;

    return Scaffold(
      body: _buildLessonContent(theme),
    );
  }

  Widget _buildLessonContent(ColorScheme theme) {
    final lessonList = selectedLesson != null ? [selectedLesson!] : lessons;
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: ListView(
        children: [
          _buildCardInfo(
            theme,
            title: 'Your Level',
            subtitle: 'Beginner',
            icon: Icons.star,
          ),
          _buildCardInfo(
            theme,
            title: 'Progress',
            subtitle: '${completedLessonIds.length} of ${lessons.length} completed',
            icon: Icons.check_circle,
            trailing: LinearProgressIndicator(
              value: lessons.isEmpty ? 0 : completedLessonIds.length / lessons.length,
              backgroundColor: theme.surface,
              valueColor: AlwaysStoppedAnimation<Color>(theme.primary),
            ),
          ),
          const SizedBox(height: 16),
          Text('Lessons', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: theme.primary)),
          const SizedBox(height: 8),
          ...lessonList.map((lesson) {
            final isCompleted = completedLessonIds.contains(lesson.id.hashCode);
            return _buildLessonCard(lesson, isCompleted, theme);
          }),
        ],
      ),
    );
  }

  Widget _buildCardInfo(ColorScheme theme,
    {required String title, required String subtitle, required IconData icon, Widget? trailing}) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      color: theme.secondaryContainer,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: ListTile(
        title: Text(title, style: const TextStyle(fontWeight: FontWeight.w600)),
        subtitle: Text(subtitle),
        leading: Icon(icon, color: theme.primary),
        trailing: SizedBox(
          width: 50, // Constrain the trailing widget's width
          child: trailing ?? Icon(Icons.arrow_forward_ios, size: 16, color: theme.onSecondary),
        ),
      ),
    );
  }

  Widget _buildLessonCard(LessonModule lesson, bool isCompleted, ColorScheme theme) {
    // Get title and content based on current language
    final title = lesson.title[language.name] ?? lesson.title['en'] ?? 'No title';
    final content = lesson.content[language.name] ?? lesson.content['en'] ?? 'No content';
    final hasExternalLink = lesson.officialLinks.isNotEmpty;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 6),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 2,
      child: ListTile(
        leading: Icon(
          isCompleted ? Icons.check_circle : Icons.radio_button_unchecked,
          color: isCompleted ? theme.primary : theme.onSurface,
        ),
        title: Text(title, style: const TextStyle(fontWeight: FontWeight.w600)),
        subtitle: Text(content, maxLines: 2, overflow: TextOverflow.ellipsis),
        trailing: hasExternalLink
            ? IconButton(
                icon: Icon(Icons.open_in_new, color: theme.primary),
                onPressed: () => _launchURL(lesson.officialLinks.first),
              )
            : null,
        onTap: () => _toggleLessonCompleted(lesson.id.hashCode),
      ),
    );
  }
}