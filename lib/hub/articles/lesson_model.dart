class LessonModule {
  final LessonLevel level;
  final String id;
  final Map<String, String> title;
  final Map<String, List<String>> objectives;
  final Map<String, String> content;
  final List<String> officialLinks;
  final Map<String, List<String>> sampleExercises;

  LessonModule({
    required this.level,
    required this.id,
    required this.title,
    required this.objectives,
    required this.content,
    required this.officialLinks,
    required this.sampleExercises,
  });

  factory LessonModule.fromJson(Map<String, dynamic> json, LessonLevel level) {
    return LessonModule(
      level: level,
      id: json['id'] ?? '',
      title: json['title'] != null
          ? Map<String, String>.from(json['title'])
          : <String, String>{},
      objectives: json['objectives'] != null
          ? Map<String, List<String>>.from(
              json['objectives'].map((k, v) => MapEntry(k, List<String>.from(v ?? []))),
            )
          : <String, List<String>>{},
      content: json['content'] != null
          ? Map<String, String>.from(json['content'])
          : <String, String>{},
      officialLinks: json['official_links'] != null
          ? List<String>.from(json['official_links'])
          : <String>[],
      sampleExercises: json['sample_exercises'] != null
          ? Map<String, List<String>>.from(
              json['sample_exercises'].map((k, v) => MapEntry(k, List<String>.from(v ?? []))),
            )
          : <String, List<String>>{},
    );
  }
}


class LessonCategory {
  final String name;
  final LessonCategory level;
  final List<String> lessons;

  LessonCategory(this.name, this.level, this.lessons);
}


enum LessonLevel { 
  beginner, 
  intermediate, 
  advanced,
}

extension LessonLevelExtension on LessonLevel {
  String get name {
    switch (this) {
      case LessonLevel.beginner:
        return 'Beginner';
      case LessonLevel.intermediate:
        return 'Intermediate';
      case LessonLevel.advanced:
        return 'Advanced';
    }
  }
}