import 'package:flutter/material.dart';
import 'assistant_page.dart';

/// Utility class for managing AI Chat Overlay
class AiOverlayUtils {
  /// Shows the AI Chat Overlay as a modal dialog
  /// Can be called from anywhere in the app
  static Future<void> showAiChat(BuildContext context) async {
    return showAiChatOverlay(context);
  }

  /// Creates a floating action button that opens the AI chat
  static Widget createAiChatFAB(BuildContext context, {
    String? heroTag,
    Color? backgroundColor,
    Color? foregroundColor,
    String tooltip = 'Ask AI Assistant',
  }) {
    final theme = Theme.of(context).colorScheme;
    
    return FloatingActionButton(
      heroTag: heroTag ?? "ai_chat_fab",
      onPressed: () => showAiChat(context),
      tooltip: tooltip,
      backgroundColor: backgroundColor ?? theme.primary,
      foregroundColor: foregroundColor ?? theme.onPrimary,
      child: const Icon(Icons.smart_toy),
    );
  }

  /// Creates an icon button that opens the AI chat
  static Widget createAiChatIconButton(BuildContext context, {
    Color? color,
    double? size,
    String tooltip = 'Ask AI Assistant',
  }) {
    final theme = Theme.of(context).colorScheme;
    
    return IconButton(
      onPressed: () => showAiChat(context),
      tooltip: tooltip,
      icon: Icon(
        Icons.smart_toy,
        color: color ?? theme.primary,
        size: size,
      ),
    );
  }

  /// Creates a text button that opens the AI chat
  static Widget createAiChatTextButton(BuildContext context, {
    String text = 'Ask AI',
    TextStyle? style,
  }) {
    return TextButton.icon(
      onPressed: () => showAiChat(context),
      icon: const Icon(Icons.smart_toy),
      label: Text(text),
      style: TextButton.styleFrom(textStyle: style),
    );
  }

  /// Wraps any widget with AI chat overlay capability
  /// Use this when you want the overlay to appear within a specific widget area
  static Widget wrapWithAiOverlay(
    Widget child, {
    bool showOverlay = false,
    VoidCallback? onOverlayToggle,
  }) {
    return AiChatOverlayWrapper(
      showOverlay: showOverlay,
      onOverlayToggle: onOverlayToggle,
      child: child,
    );
  }
}

/// Example usage widget demonstrating different ways to integrate AI chat
class AiChatExampleUsage extends StatefulWidget {
  const AiChatExampleUsage({super.key});

  @override
  State<AiChatExampleUsage> createState() => _AiChatExampleUsageState();
}

class _AiChatExampleUsageState extends State<AiChatExampleUsage> {
  bool showOverlay = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI Chat Integration Examples'),
        actions: [
          // Example: AI chat icon button in app bar
          AiOverlayUtils.createAiChatIconButton(context),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Different ways to integrate AI Chat:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            
            // Example: Text button
            AiOverlayUtils.createAiChatTextButton(context),
            const SizedBox(height: 16),
            
            // Example: Custom button
            ElevatedButton.icon(
              onPressed: () => AiOverlayUtils.showAiChat(context),
              icon: const Icon(Icons.smart_toy),
              label: const Text('Custom AI Chat Button'),
            ),
            const SizedBox(height: 16),
            
            // Example: Toggle overlay within widget
            ElevatedButton(
              onPressed: () => setState(() => showOverlay = !showOverlay),
              child: Text(showOverlay ? 'Hide AI Overlay' : 'Show AI Overlay'),
            ),
            const SizedBox(height: 20),
            
            // Example: Wrapped content with overlay
            Expanded(
              child: AiOverlayUtils.wrapWithAiOverlay(
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Center(
                    child: Text(
                      'This content can have AI overlay on top of it',
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
                showOverlay: showOverlay,
                onOverlayToggle: () => setState(() => showOverlay = false),
              ),
            ),
          ],
        ),
      ),
      // Example: Floating action button
      floatingActionButton: AiOverlayUtils.createAiChatFAB(context),
    );
  }
}
