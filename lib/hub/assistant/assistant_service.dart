import 'dart:convert';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../../core/constants.dart';
import '../../services/auth_client.dart';
import 'models/chat_models.dart';

/// Enhanced service to interact with the Assistant Service API.
/// Uses both /api/chat and /api/assistant/ask endpoints with proper authentication.
/// Supports real streaming, conversation history, and rate limiting awareness.
///
/// This is a refined version that integrates with the abra-servers Assistant Service
/// running at http://abraapp.undeclab.com with proper JWT authentication.
class AssistantService {
  final String baseUrl;
  final http.Client _httpClient;
  final AuthClient _authClient;

  // Rate limiting tracking
  final Map<String, List<DateTime>> _requestHistory = {};

  AssistantService({
    this.baseUrl = AssistantConfig.baseUrl,
    http.Client? httpClient,
    AuthClient? authClient,
  }) : _httpClient = httpClient ?? http.Client(),
       _authClient = authClient ?? AuthClient();

  /// Test connection to the assistant service
  Future<bool> testConnection() async {
    try {
      final headers = createHeaders();
      final response = await _httpClient
          .get(
            Uri.parse('$baseUrl${AssistantConfig.healthEndpoint}'),
            headers: headers,
          )
          .timeout(AssistantConfig.connectionTimeout);

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('Connection test failed: $e');
      return false;
    }
  }

  /// Create headers with authentication if available
  Map<String, String> createHeaders({bool includeAuth = true}) {
    final headers = <String, String>{
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (includeAuth) {
      final token = _authClient.accessToken;
      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
      }
    }

    return headers;
  }

  /// Check rate limiting for an endpoint
  bool checkRateLimit(String endpoint) {
    final now = DateTime.now();
    final key = endpoint;

    // Clean old requests outside the rate limit window
    _requestHistory[key]?.removeWhere(
      (time) => now.difference(time) > AssistantConfig.rateLimitWindow,
    );

    final requests = _requestHistory[key] ?? [];
    final limit =
        endpoint == AssistantConfig.askEndpoint
            ? AssistantConfig.askRateLimit
            : AssistantConfig.chatRateLimit;

    if (requests.length >= limit) {
      return false; // Rate limit exceeded
    }

    // Add current request
    _requestHistory[key] = [...requests, now];
    return true;
  }

  /// Simple ask method using the /api/assistant/ask endpoint for quick questions
  /// This is ideal for simple questions without conversation history
  Future<String> ask(String prompt) async {
    final sanitizedPrompt = _sanitizeInput(prompt);
    if (sanitizedPrompt.isEmpty) {
      return '⚠️ Please enter a valid question.';
    }

    // Check rate limiting
    if (!checkRateLimit(AssistantConfig.askEndpoint)) {
      return '⏳ Rate limit exceeded. Please wait before asking another question.';
    }

    try {
      final headers = createHeaders();
      final body = jsonEncode({'prompt': sanitizedPrompt});

      final response = await _httpClient
          .post(
            Uri.parse('$baseUrl${AssistantConfig.askEndpoint}'),
            headers: headers,
            body: body,
          )
          .timeout(AssistantConfig.requestTimeout);

      if (response.statusCode != 200) {
        return '❌ ${_getErrorMessage(response.statusCode, response.body)}';
      }

      // The /api/assistant/ask endpoint returns JSON with a "reply" field
      try {
        final data = jsonDecode(response.body);
        final reply = data['reply'] as String? ?? '';
        return _sanitizeOutput(reply);
      } catch (e) {
        // Fallback to plain text if JSON parsing fails
        return _sanitizeOutput(response.body);
      }
    } catch (e) {
      return '🚫 ${_formatError(e)}';
    }
  }

  /// Streams assistant response using the /api/chat endpoint with real streaming.
  /// Supports conversation history and proper streaming from the server.
  Stream<String> chatStream(
    String message, {
    List<ChatMessage>? conversationHistory,
    String model = AssistantConfig.defaultModel,
  }) async* {
    final sanitizedMessage = _sanitizeInput(message);
    if (sanitizedMessage.isEmpty) {
      yield '⚠️ Please enter a valid question.';
      return;
    }

    // Check rate limiting
    if (!checkRateLimit(AssistantConfig.chatEndpoint)) {
      yield '⏳ Rate limit exceeded. Please wait before sending another message.';
      return;
    }

    try {
      // Build messages array with conversation history
      final messages = <ChatMessage>[
        ...?conversationHistory,
        ChatMessage(role: 'user', content: sanitizedMessage),
      ];

      // Limit conversation history to prevent token overflow
      final limitedMessages = _limitConversationHistory(messages);

      final request = ChatRequest(
        model: model,
        messages: limitedMessages,
        stream: true,
      );

      final headers = createHeaders();
      headers['Accept'] = 'text/plain'; // For streaming response

      // Use regular POST but with streaming processing
      final response = await _httpClient
          .post(
            Uri.parse('$baseUrl${AssistantConfig.chatEndpoint}'),
            headers: headers,
            body: request.toJsonString(),
          )
          .timeout(AssistantConfig.streamTimeout);

      if (response.statusCode != 200) {
        yield '❌ ${_getErrorMessage(response.statusCode, response.body)}';
        return;
      }

      // Process the response body as streaming chunks
      yield* _parseStreamingResponse(response.body);
    } catch (e) {
      yield '🚫 ${_formatError(e)}';
    }
  }

  /// Get complete response using the /api/chat endpoint without streaming
  Future<String> chat(
    String message, {
    List<ChatMessage>? conversationHistory,
    String model = AssistantConfig.defaultModel,
  }) async {
    final sanitizedMessage = _sanitizeInput(message);
    if (sanitizedMessage.isEmpty) {
      return '⚠️ Please enter a valid question.';
    }

    // Check rate limiting
    if (!checkRateLimit(AssistantConfig.chatEndpoint)) {
      return '⏳ Rate limit exceeded. Please wait before sending another message.';
    }

    try {
      // Build messages array with conversation history
      final messages = <ChatMessage>[
        ...?conversationHistory,
        ChatMessage(role: 'user', content: sanitizedMessage),
      ];

      // Limit conversation history to prevent token overflow
      final limitedMessages = _limitConversationHistory(messages);

      final request = ChatRequest(
        model: model,
        messages: limitedMessages,
        stream: false,
      );

      final headers = createHeaders();

      final response = await _httpClient
          .post(
            Uri.parse('$baseUrl${AssistantConfig.chatEndpoint}'),
            headers: headers,
            body: request.toJsonString(),
          )
          .timeout(AssistantConfig.requestTimeout);

      if (response.statusCode != 200) {
        return '❌ ${_getErrorMessage(response.statusCode, response.body)}';
      }

      final data = jsonDecode(response.body);
      final chatResponse = ChatResponse.fromJson(data);
      return _sanitizeOutput(chatResponse.message.content);
    } catch (e) {
      return '🚫 ${_formatError(e)}';
    }
  }

  /// Parse streaming response from the server
  /// The C# service returns newline-delimited JSON chunks
  Stream<String> _parseStreamingResponse(String responseBody) async* {
    final lines = responseBody.split('\n');
    for (int i = 0; i < lines.length; i++) {
      final line = lines[i];
      if (line.trim().isEmpty) continue;

      try {
        final data = jsonDecode(line);
        final streamResponse = StreamingChatResponse.fromJson(data);
        if (streamResponse.message.content.isNotEmpty) {
          yield streamResponse.message.content;

          // Add a small delay to simulate streaming (except for the last chunk)
          if (!streamResponse.done && i < lines.length - 1) {
            await Future.delayed(const Duration(milliseconds: 50));
          }
        }
        if (streamResponse.done) break;
      } catch (e) {
        // Skip malformed JSON lines
        debugPrint('Failed to parse streaming line: $line - Error: $e');
        continue;
      }
    }
  }

  /// Limit conversation history to prevent token overflow
  List<ChatMessage> _limitConversationHistory(List<ChatMessage> messages) {
    if (messages.length <= AssistantConfig.maxConversationHistory) {
      return messages;
    }

    // Keep the most recent messages, but always preserve the first system message if present
    final systemMessages =
        messages.where((m) => m.role == 'system').take(1).toList();
    final recentMessages =
        messages
            .where((m) => m.role != 'system')
            .skip(
              messages.length -
                  AssistantConfig.maxConversationHistory +
                  systemMessages.length,
            )
            .toList();

    return [...systemMessages, ...recentMessages];
  }

  /// Get user-friendly error message based on status code
  String _getErrorMessage(int statusCode, String responseBody) {
    // Try to extract specific error message from response body first
    if (statusCode == 400 && responseBody.isNotEmpty) {
      try {
        // If it's a simple string error message, return it
        if (!responseBody.startsWith('{')) {
          return responseBody.length > 100
              ? '${responseBody.substring(0, 100)}...'
              : responseBody;
        }
        // Try to parse as JSON error
        final errorData = jsonDecode(responseBody);
        if (errorData is Map && errorData.containsKey('message')) {
          return errorData['message'].toString();
        }
      } catch (e) {
        // Fall back to generic message
        debugPrint('Failed to parse error response: $e');
      }
    }

    return AssistantConfig.getFriendlyErrorMessage(
      Exception('HTTP $statusCode'),
      statusCode,
    );
  }

  /// Trim, strip unwanted characters, and limit input length.
  String _sanitizeInput(String input) {
    final trimmed = input.trim();

    // Validate message length first
    final validationError = AssistantConfig.validateMessage(trimmed);
    if (validationError != null) {
      throw ArgumentError(validationError);
    }

    // Remove control characters but preserve newlines and tabs
    final sanitized = trimmed.replaceAll(
      RegExp(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]'),
      '',
    );

    // Normalize whitespace
    final normalized = sanitized.replaceAll(RegExp(r'\s+'), ' ').trim();

    // Final length check after sanitization
    return normalized.length > AssistantConfig.maxMessageLength
        ? normalized.substring(0, AssistantConfig.maxMessageLength)
        : normalized;
  }

  /// Clean up output.
  String _sanitizeOutput(String output) {
    final cleaned = output.replaceAll(RegExp(r'<[^>]*>'), '').trim();
    return cleaned.length > 2000 ? '${cleaned.substring(0, 2000)}...' : cleaned;
  }

  /// Format error messages for user display
  String _formatError(Object error) {
    // Use the centralized error message helper
    return AssistantConfig.getFriendlyErrorMessage(error);
  }

  /// Continue conversation with response and updated conversation history
  Future<ConversationResult> continueConversation(
    List<ChatMessage> conversation,
    String message, {
    String model = AssistantConfig.defaultModel,
  }) async {
    final response = await chat(
      message,
      conversationHistory: conversation,
      model: model,
    );

    final updatedConversation = <ChatMessage>[
      ...conversation,
      ChatMessage(role: 'user', content: _sanitizeInput(message)),
      ChatMessage(role: 'assistant', content: response),
    ];

    return ConversationResult(
      response: response,
      conversation: updatedConversation,
    );
  }

  /// Check if user is authenticated
  bool get isAuthenticated => _authClient.isAuthenticated;

  /// Get current user information
  String? get currentUserId => _authClient.currentUser?.id;

  /// Clear rate limiting history (useful for testing or admin reset)
  void clearRateLimitHistory() {
    _requestHistory.clear();
  }

  /// Get rate limit status for an endpoint
  Map<String, dynamic> getRateLimitStatus(String endpoint) {
    final now = DateTime.now();
    final key = endpoint;

    // Clean old requests
    _requestHistory[key]?.removeWhere(
      (time) => now.difference(time) > AssistantConfig.rateLimitWindow,
    );

    final requests = _requestHistory[key] ?? [];
    final limit =
        endpoint == AssistantConfig.askEndpoint
            ? AssistantConfig.askRateLimit
            : AssistantConfig.chatRateLimit;

    return {
      'endpoint': endpoint,
      'currentRequests': requests.length,
      'limit': limit,
      'remaining': limit - requests.length,
      'resetTime':
          requests.isNotEmpty
              ? requests.first
                  .add(AssistantConfig.rateLimitWindow)
                  .toIso8601String()
              : null,
    };
  }

  /// Get current service configuration
  Map<String, dynamic> getConfig() {
    return {
      'baseUrl': baseUrl,
      'defaultModel': AssistantConfig.defaultModel,
      'authentication': {
        'isAuthenticated': isAuthenticated,
        'userId': currentUserId,
      },
      'endpoints': {
        'chat': '$baseUrl${AssistantConfig.chatEndpoint}',
        'ask': '$baseUrl${AssistantConfig.askEndpoint}',
        'health': '$baseUrl${AssistantConfig.healthEndpoint}',
      },
      'rateLimits': {
        'chat': getRateLimitStatus(AssistantConfig.chatEndpoint),
        'ask': getRateLimitStatus(AssistantConfig.askEndpoint),
      },
    };
  }

  /// Dispose of resources
  void dispose() {
    _httpClient.close();
  }
}
