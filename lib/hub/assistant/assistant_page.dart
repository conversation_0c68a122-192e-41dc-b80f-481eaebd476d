import 'package:flutter/material.dart';
import 'assistant_service.dart';
import 'models/chat_models.dart';
import '../../core/constants.dart';

/// Shows the AI Chat Overlay on top of any widget
/// Returns a Future that completes when the overlay is closed
Future<void> showAiChatOverlay(BuildContext context) async {
  return showDialog<void>(
    context: context,
    barrierDismissible: true,
    barrierColor: Colors.black54,
    builder: (BuildContext context) {
      return AiChatOverlay(onClose: () => Navigator.of(context).pop());
    },
  );
}

/// Widget that can be used to overlay AI chat on top of any content
class AiChatOverlayWrapper extends StatefulWidget {
  final Widget child;
  final bool showOverlay;
  final VoidCallback? onOverlayToggle;

  const AiChatOverlayWrapper({
    super.key,
    required this.child,
    this.showOverlay = false,
    this.onOverlayToggle,
  });

  @override
  AiChatOverlayWrapperState createState() => AiChatOverlayWrapperState();
}

class AiChatOverlayWrapperState extends State<AiChatOverlayWrapper> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        if (widget.showOverlay)
          AiChatOverlay(onClose: widget.onOverlayToggle ?? () {}),
      ],
    );
  }
}

class AiChatOverlay extends StatefulWidget {
  final VoidCallback onClose;

  const AiChatOverlay({super.key, required this.onClose});

  @override
  AiChatOverlayState createState() => AiChatOverlayState();
}

class AiChatOverlayState extends State<AiChatOverlay> {
  final TextEditingController _controller = TextEditingController();
  late final AssistantService _aiService;
  bool _isLoading = false;
  final List<ChatMessage> _messages = [];
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _aiService = AssistantService();
    _testConnection();
    _initializeConversation();
  }

  @override
  void dispose() {
    _controller.dispose();
    _scrollController.dispose();
    _aiService.dispose();
    super.dispose();
  }

  void _initializeConversation() {
    _messages.add(
      ChatMessage(
        role: 'assistant',
        content:
            '👋 Hello! I\'m Alim, your trading assistant. How can I help you today?',
      ),
    );
  }

  Future<void> _testConnection() async {
    final isConnected = await _aiService.testConnection();
    if (mounted && !isConnected) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Cannot connect to assistant service'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _sendMessage([String? message]) async {
    final text = message ?? _controller.text.trim();
    if (text.isEmpty || _isLoading) return;

    final userMessage = text;
    if (message == null) {
      _controller.clear();
    }

    if (!mounted) return;
    setState(() {
      _isLoading = true;
      _messages.add(ChatMessage(role: 'user', content: userMessage));
    });

    _scrollToBottom();

    try {
      String assistantResponse = '';

      // Use streaming for real-time response
      await for (final chunk in _aiService.chatStream(
        userMessage,
        conversationHistory: _messages.take(_messages.length - 1).toList(),
      )) {
        if (!mounted) return; // Check if widget is still mounted

        // Check if this chunk is an error message
        if (chunk.startsWith('🚫') || chunk.startsWith('❌')) {
          setState(() {
            _messages.add(
              ChatMessage(
                role: 'assistant',
                content: chunk,
                isError: true,
                originalPrompt: userMessage,
              ),
            );
          });
          _scrollToBottom();
          return; // Stop processing further chunks
        }

        assistantResponse += chunk;
        setState(() {
          // Update the last message if it's from assistant, or add new one
          if (_messages.isNotEmpty && _messages.last.role == 'assistant') {
            _messages.last = ChatMessage(
              role: 'assistant',
              content: assistantResponse,
            );
          } else {
            _messages.add(
              ChatMessage(role: 'assistant', content: assistantResponse),
            );
          }
        });
        _scrollToBottom();
      }
    } catch (e) {
      if (!mounted) return; // Check if widget is still mounted

      setState(() {
        _messages.add(
          ChatMessage(
            role: 'assistant',
            content: 'Error: $e',
            isError: true,
            originalPrompt: userMessage,
          ),
        );
      });
      _scrollToBottom();
    } finally {
      if (mounted) {
        // Only call setState if widget is still mounted
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _retryMessage(String originalPrompt) {
    if (_isLoading) return;

    // Remove the error message
    setState(() {
      _messages.removeWhere(
        (msg) => msg.isError && msg.originalPrompt == originalPrompt,
      );
    });

    // Retry the original message
    _sendMessage(originalPrompt);
  }

  void _clearConversation() {
    if (_isLoading) return;

    setState(() {
      _messages.clear();
      _initializeConversation();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final screenSize = MediaQuery.of(context).size;

    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.all(20),
      child: Container(
        width: screenSize.width * 0.9,
        height: screenSize.height * 0.8,
        decoration: BoxDecoration(
          color: colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: colorScheme.outline),
          boxShadow: [
            BoxShadow(
              color: Colors.black26,
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: Column(
            children: [
              // Header
              Container(
                color: colorScheme.primary,
                child: SafeArea(
                  bottom: false,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    child: Row(
                      children: [
                        Image.asset(
                          'assets/ai/alim_bot.png',
                          width: 32,
                          height: 32,
                          fit: BoxFit.contain,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            'Ask Alim',
                            style: TextStyle(
                              color: colorScheme.onPrimary,
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        IconButton(
                          icon: Icon(
                            Icons.refresh,
                            color: colorScheme.onPrimary,
                          ),
                          onPressed: _clearConversation,
                          tooltip: 'Clear conversation',
                        ),
                        IconButton(
                          icon: Icon(Icons.close, color: colorScheme.onPrimary),
                          onPressed: widget.onClose,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              // Messages area
              Expanded(
                child: ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.all(16),
                  itemCount: _messages.length,
                  itemBuilder: (context, index) {
                    final msg = _messages[index];
                    final isUser = msg.role == 'user';

                    return Align(
                      alignment:
                          isUser ? Alignment.centerRight : Alignment.centerLeft,
                      child: Container(
                        margin: const EdgeInsets.symmetric(vertical: 4),
                        padding: const EdgeInsets.all(12),
                        constraints: BoxConstraints(
                          maxWidth: screenSize.width * 0.7,
                        ),
                        decoration: BoxDecoration(
                          color:
                              isUser
                                  ? colorScheme.primary
                                  : colorScheme.primaryContainer,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black,
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SelectableText(
                              msg.content,
                              style: TextStyle(
                                color:
                                    isUser
                                        ? colorScheme.onPrimary
                                        : colorScheme.onPrimaryContainer,
                                fontSize: 14,
                                height: 1.4,
                              ),
                            ),
                            // Show retry button for error messages
                            if (msg.isError && msg.originalPrompt != null)
                              Padding(
                                padding: const EdgeInsets.only(top: 8),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    IconButton(
                                      onPressed:
                                          _isLoading
                                              ? null
                                              : () => _retryMessage(
                                                msg.originalPrompt!,
                                              ),
                                      icon: Icon(
                                        Icons.refresh,
                                        size: 18,
                                        color:
                                            _isLoading
                                                ? colorScheme.onPrimaryContainer
                                                    .withValues(alpha: 0.5)
                                                : colorScheme
                                                    .onPrimaryContainer,
                                      ),
                                      tooltip: 'Retry',
                                      constraints: const BoxConstraints(
                                        minWidth: 32,
                                        minHeight: 32,
                                      ),
                                      padding: const EdgeInsets.all(4),
                                    ),
                                    Text(
                                      'Retry',
                                      style: TextStyle(
                                        color: colorScheme.onPrimaryContainer
                                            .withValues(alpha: 0.7),
                                        fontSize: 12,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
              // Animated dots loading indicator in message bubble style
              if (_isLoading)
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: colorScheme.primaryContainer,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black,
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: const AnimatedDotsIndicator(),
                      ),
                    ],
                  ),
                ),
              // Input area
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: colorScheme.tertiary,
                  border: Border(
                    top: BorderSide(
                      color: colorScheme.outline.withValues(alpha: 0.2),
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: _controller,
                        enabled: !_isLoading,
                        onSubmitted: (value) {
                          final prompt = value.trim();
                          if (prompt.isNotEmpty && !_isLoading) {
                            final validationError =
                                AssistantConfig.validateMessage(prompt);
                            if (validationError == null) {
                              _sendMessage(prompt);
                              _controller.clear();
                              FocusScope.of(context).unfocus();
                            } else {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(validationError),
                                  backgroundColor: Colors.red,
                                ),
                              );
                            }
                          }
                        },
                        decoration: InputDecoration(
                          hintText: 'Ask a trading question...',
                          filled: true,
                          fillColor: colorScheme.secondary,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(24),
                            borderSide: BorderSide.none,
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      icon: Icon(
                        Icons.send,
                        color:
                            _isLoading
                                ? colorScheme.secondary
                                : colorScheme.onPrimary,
                      ),
                      onPressed:
                          _isLoading
                              ? null
                              : () {
                                final text = _controller.text.trim();
                                if (text.isNotEmpty) {
                                  final validationError =
                                      AssistantConfig.validateMessage(text);
                                  if (validationError == null) {
                                    _sendMessage(text);
                                    _controller.clear();
                                    FocusScope.of(context).unfocus();
                                  } else {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(validationError),
                                        backgroundColor: Colors.red,
                                      ),
                                    );
                                  }
                                }
                              },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Animated dots loading indicator widget
class AnimatedDotsIndicator extends StatefulWidget {
  const AnimatedDotsIndicator({super.key});

  @override
  State<AnimatedDotsIndicator> createState() => _AnimatedDotsIndicatorState();
}

class _AnimatedDotsIndicatorState extends State<AnimatedDotsIndicator>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // Create staggered animations for 3 dots
    _animations = List.generate(3, (index) {
      return Tween<double>(begin: 0.4, end: 1.0).animate(
        CurvedAnimation(
          parent: _animationController,
          curve: Interval(
            index * 0.2,
            0.6 + index * 0.2,
            curve: Curves.easeInOut,
          ),
        ),
      );
    });

    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(3, (index) {
            return Padding(
              padding: EdgeInsets.only(right: index < 2 ? 4.0 : 0),
              child: Transform.scale(
                scale: _animations[index].value,
                child: Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: colorScheme.onPrimaryContainer.withValues(
                      alpha: 0.7,
                    ),
                    shape: BoxShape.circle,
                  ),
                ),
              ),
            );
          }),
        );
      },
    );
  }
}
