import 'dart:convert';

/// Represents a chat message in the conversation
class ChatMessage {
  final String role;
  final String content;
  final bool isError;
  final String? originalPrompt; // Store original prompt for retry functionality

  const ChatMessage({
    required this.role,
    required this.content,
    this.isError = false,
    this.originalPrompt,
  });

  Map<String, dynamic> toJson() {
    return {
      'role': role,
      'content': content,
      'isError': isError,
      'originalPrompt': originalPrompt,
    };
  }

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    // Handle both lowercase (expected) and capitalized (actual server response) field names
    final role = json['role'] ?? json['Role'] ?? '';
    final content = json['content'] ?? json['Content'] ?? '';
    final isError = json['isError'] ?? false;
    final originalPrompt = json['originalPrompt'];

    return ChatMessage(
      role: role,
      content: content,
      isError: isError,
      originalPrompt: originalPrompt,
    );
  }

  @override
  String toString() =>
      'ChatMessage(role: $role, content: $content, isError: $isError)';

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatMessage &&
        other.role == role &&
        other.content == content &&
        other.isError == isError &&
        other.originalPrompt == originalPrompt;
  }

  @override
  int get hashCode => Object.hash(role, content, isError, originalPrompt);
}

/// Request model for the chat API
class ChatRequest {
  final String model;
  final List<ChatMessage> messages;
  final bool stream;

  const ChatRequest({
    required this.model,
    required this.messages,
    this.stream = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'model': model,
      'messages': messages.map((msg) => msg.toJson()).toList(),
      'stream': stream,
    };
  }

  String toJsonString() => jsonEncode(toJson());

  factory ChatRequest.fromJson(Map<String, dynamic> json) {
    return ChatRequest(
      model: json['model'] ?? '',
      messages:
          (json['messages'] as List<dynamic>?)
              ?.map((msg) => ChatMessage.fromJson(msg))
              .toList() ??
          [],
      stream: json['stream'] ?? false,
    );
  }

  @override
  String toString() =>
      'ChatRequest(model: $model, messages: ${messages.length}, stream: $stream)';
}

/// Response model for non-streaming chat API
class ChatResponse {
  final ChatMessage message;
  final bool done;

  const ChatResponse({required this.message, required this.done});

  factory ChatResponse.fromJson(Map<String, dynamic> json) {
    // Handle both lowercase (expected) and capitalized (actual server response) field names
    final messageData = json['message'] ?? json['Message'] ?? {};
    final isDone = json['done'] ?? json['Done'] ?? false;

    return ChatResponse(
      message: ChatMessage.fromJson(messageData),
      done: isDone,
    );
  }

  Map<String, dynamic> toJson() {
    return {'message': message.toJson(), 'done': done};
  }

  @override
  String toString() => 'ChatResponse(message: $message, done: $done)';
}

/// Response model for streaming chat API
class StreamingChatResponse {
  final ChatMessage message;
  final bool done;

  const StreamingChatResponse({required this.message, required this.done});

  factory StreamingChatResponse.fromJson(Map<String, dynamic> json) {
    // Handle both lowercase (expected) and capitalized (actual server response) field names
    final messageData = json['message'] ?? json['Message'] ?? {};
    final isDone = json['done'] ?? json['Done'] ?? false;

    return StreamingChatResponse(
      message: ChatMessage.fromJson(messageData),
      done: isDone,
    );
  }

  Map<String, dynamic> toJson() {
    return {'message': message.toJson(), 'done': done};
  }

  @override
  String toString() => 'StreamingChatResponse(message: $message, done: $done)';
}

/// Result of a conversation continuation
class ConversationResult {
  final String response;
  final List<ChatMessage> conversation;

  const ConversationResult({
    required this.response,
    required this.conversation,
  });

  @override
  String toString() =>
      'ConversationResult(response: $response, conversation: ${conversation.length})';
}

/// Request model for the /api/assistant/ask endpoint
class AskRequest {
  final String prompt;

  const AskRequest({required this.prompt});

  Map<String, dynamic> toJson() {
    return {'prompt': prompt};
  }

  String toJsonString() => jsonEncode(toJson());

  factory AskRequest.fromJson(Map<String, dynamic> json) {
    return AskRequest(prompt: json['prompt'] ?? '');
  }

  @override
  String toString() => 'AskRequest(prompt: $prompt)';
}

/// Response model for the /api/assistant/ask endpoint
class AskResponse {
  final String response;

  const AskResponse({required this.response});

  factory AskResponse.fromJson(Map<String, dynamic> json) {
    return AskResponse(response: json['response'] ?? '');
  }

  /// Create from plain text response
  factory AskResponse.fromText(String text) {
    return AskResponse(response: text);
  }

  Map<String, dynamic> toJson() {
    return {'response': response};
  }

  @override
  String toString() => 'AskResponse(response: $response)';
}

/// Utility class for creating common message types
class ChatMessageHelper {
  /// Creates a user message
  static ChatMessage user(String content) =>
      ChatMessage(role: 'user', content: content);

  /// Creates an assistant message
  static ChatMessage assistant(String content) =>
      ChatMessage(role: 'assistant', content: content);

  /// Creates a system message
  static ChatMessage system(String content) =>
      ChatMessage(role: 'system', content: content);
}
