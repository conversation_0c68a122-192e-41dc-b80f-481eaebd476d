import 'package:flutter/material.dart';
import 'package:abra/core/utilities/navrail.dart';
import 'package:abra/hub/articles/lesson_page.dart';
import 'package:abra/hub/articles/lesson_model.dart';
import 'package:abra/core/constants.dart';
import 'assistant/assistant_page.dart';
import 'feeds/ideas_screen.dart';

class MarketHubScreen extends StatefulWidget {
  const MarketHubScreen({super.key});

  @override
  MarketHubScreenState createState() => MarketHubScreenState();
}

class MarketHubScreenState extends State<MarketHubScreen> {

  int _selectedIndex = 0;
  LessonModule? _selectedModule;
  bool _isExpanded = true;

  final List<LessonCategoryWithModules> _lessonCategories = [
    // These will be populated with actual lesson modules when available
  ];

  void _onSelect(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  void _onShowAI() {
    showAiChatOverlay(context);
  }

  void _onModuleSelect(LessonModule module) {
    setState(() {
      _selectedModule = module;
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.primary,
      body:  SafeArea(
        child: Row(
              children: [
                _isExpanded ?  NavRail(
                  selectedIndex: _selectedIndex,
                  onSelect: _onSelect,
                  onShowAI: _onShowAI,
                  lessonCategories: _lessonCategories,
                  theme: colorScheme,
                  selectedModule: _selectedModule,
                  onModuleSelect: _onModuleSelect,
                  userLocale: Lang.en, // You can make this dynamic based on user preferences
                  isExpanded: _isExpanded,
                  onToggleExpansion: () {
                    setState(() {
                      _isExpanded = !_isExpanded;
                    });
                  },
                ) : const SizedBox.shrink(),
                Expanded(
                  child: Column(
                    children: [
                      _buildTopBar(colorScheme),
                      Divider(color: colorScheme.onPrimary, thickness: 1.5),
                      Expanded(
                        child: _selectedIndex == 0
                            ? ArticlesScreen(selectedLesson: _selectedModule)
                            : const ThreadsPage(),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildTopBar(ColorScheme colorScheme) {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 10),
      color: colorScheme.tertiary,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          IconButton(
            icon: Icon(Icons.chevron_right_rounded, color: colorScheme.onSurface),
            onPressed: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            }, 
          ),
          Expanded(
            child:  Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconButton(
                  icon: Icon(
                    _selectedIndex == 0 ? Icons.article_rounded :
                        Icons.article_outlined,
                    color: colorScheme.onSurface,
                  ),
                  tooltip: 'Article Screen',
                  onPressed: () {
                    setState(() => _selectedIndex = 0);
                  },
                ),
                IconButton(
                  icon: Icon(
                    _selectedIndex == 1 ? Icons.lightbulb_rounded :
                        Icons.lightbulb_outline,
                    color: colorScheme.onSurface,
                  ),
                  tooltip: 'Ideas',
                  onPressed: () {
                    setState(() => _selectedIndex = 1);
                  },
                )
              ]
            )
          )
        ],
      ),
    );
  }
}