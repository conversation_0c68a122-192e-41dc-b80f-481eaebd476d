//======================= THREAD ==========================
class Thread {
  final String id;
  final String userId;
  final String content;
  final DateTime createdAt;
  final DateTime? updatedAt;

  // Profile info joined from `profiles`
  final String? username;
  final String? avatarUrl;

  // Aggregated counts from likes, comments, shares
  final int likesCount;
  final int commentsCount;
  final int sharesCount;

  Thread({
    required this.id,
    required this.userId,
    required this.content,
    required this.createdAt,
    this.updatedAt,
    this.username,
    this.avatarUrl,
    this.likesCount = 0,
    this.commentsCount = 0,
    this.sharesCount = 0,
  });

  // Getter for user initials
  String get initials {
    if (username == null || username!.isEmpty) return '?';
    final parts = username!.trim().split(' ');
    if (parts.length >= 2) {
      return (parts[0][0] + parts[1][0]).toUpperCase();
    }
    return username![0].toUpperCase();
  }

  Thread copyWith({
    String? id,
    String? userId,
    String? content,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? username,
    String? avatarUrl,
    int? likesCount,
    int? commentsCount,
    int? sharesCount,
  }) {
    return Thread(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      content: content ?? this.content,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      username: username ?? this.username,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      likesCount: likesCount ?? this.likesCount,
      commentsCount: commentsCount ?? this.commentsCount,
      sharesCount: sharesCount ?? this.sharesCount,
    );
  }

  factory Thread.fromJson(Map<String, dynamic> json) {
    final profile = json['profiles'] as Map<String, dynamic>?;

    return Thread(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      content: json['content'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'] as String)
              : null,
      username: profile?['username'] as String?,
      avatarUrl: profile?['avatar_url'] as String?,
      likesCount: (json['likes']?['count'] as int?) ?? 0,
      commentsCount: (json['comments']?['count'] as int?) ?? 0,
      sharesCount: (json['shares']?['count'] as int?) ?? 0,
    );
  }

  /// Factory constructor for API response format
  factory Thread.fromApiJson(Map<String, dynamic> json) {
    final user = json['user'] as Map<String, dynamic>?;

    return Thread(
      id: json['id'] as String,
      userId: json['userId'] as String,
      content: json['content'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt:
          json['updatedAt'] != null
              ? DateTime.parse(json['updatedAt'] as String)
              : null,
      username: user?['username'] as String?,
      avatarUrl: user?['avatarUrl'] as String?,
      likesCount: (json['likesCount'] as int?) ?? 0,
      commentsCount: (json['commentsCount'] as int?) ?? 0,
      sharesCount: (json['sharesCount'] as int?) ?? 0,
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'user_id': userId,
    'content': content,
    'created_at': createdAt.toIso8601String(),
    'updated_at': updatedAt?.toIso8601String(),
    'profiles': {
      if (username != null) 'username': username,
      if (avatarUrl != null) 'avatar_url': avatarUrl,
    },
    'likes': {'count': likesCount},
    'comments': {'count': commentsCount},
    'shares': {'count': sharesCount},
  };
}

//===================== COMMENTS =====================
class Comment {
  final String id;
  final String threadId;
  final String userId;
  final String content;
  final DateTime createdAt;
  final DateTime? updatedAt;

  final String? username;
  final String? avatarUrl;

  Comment({
    required this.id,
    required this.threadId,
    required this.userId,
    required this.content,
    required this.createdAt,
    this.updatedAt,
    this.username,
    this.avatarUrl,
  });

  factory Comment.fromJson(Map<String, dynamic> json) {
    final profile = json['profiles'] as Map<String, dynamic>?;

    return Comment(
      id: json['id'] as String,
      threadId: json['thread_id'] as String,
      userId: json['user_id'] as String,
      content: json['content'] as String,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : null,
      username: profile?['username'],
      avatarUrl: profile?['avatar_url'],
    );
  }

  /// Factory constructor for API response format
  factory Comment.fromApiJson(Map<String, dynamic> json) {
    final user = json['user'] as Map<String, dynamic>?;

    return Comment(
      id: json['id'] as String,
      threadId: json['postId'] as String,
      userId: json['userId'] as String,
      content: json['content'] as String,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt:
          json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      username: user?['username'],
      avatarUrl: user?['avatarUrl'],
    );
  }
}
