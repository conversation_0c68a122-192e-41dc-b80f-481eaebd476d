import 'package:flutter/material.dart';
import 'widgets/create_idea_dialog.dart';
import 'models/ideas_model.dart';
import 'ideas_service.dart';

class ThreadsPage extends StatefulWidget {
  const ThreadsPage({super.key});

  @override
  State<ThreadsPage> createState() => _ThreadsPageState();
}

class _ThreadsPageState extends State<ThreadsPage>
    with SingleTickerProviderStateMixin {
  final ThreadsService _threadsService = ThreadsService();
  List<Thread> _threads = [];
  bool _isLoading = true;
  bool _isLoadingMore = false;
  String? _error;

  final ScrollController _scrollController = ScrollController();
  static const int _pageSize = 20;
  int _currentOffset = 0;
  bool _hasMoreData = true;

  late final AnimationController _animController;

  @override
  void initState() {
    super.initState();
    _animController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _loadThreads();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _animController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreThreads();
    }
  }

  Future<void> _loadThreads() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = null;
      _currentOffset = 0;
      _hasMoreData = true;
    });

    try {
      final threads = await _threadsService.fetchThreads(
        limit: _pageSize,
        offset: 0,
      );

      if (mounted) {
        setState(() {
          _threads = threads;
          _isLoading = false;
          _currentOffset = threads.length;
          _hasMoreData = threads.length == _pageSize;
        });
        _animController.forward();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadMoreThreads() async {
    if (_isLoadingMore || !_hasMoreData) return;

    setState(() => _isLoadingMore = true);

    try {
      final moreThreads = await _threadsService.fetchThreads(
        limit: _pageSize,
        offset: _currentOffset,
      );

      if (mounted) {
        setState(() {
          _threads.addAll(moreThreads);
          _currentOffset += moreThreads.length;
          _hasMoreData = moreThreads.length == _pageSize;
          _isLoadingMore = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoadingMore = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load more threads: $e')),
        );
      }
    }
  }

  void _onLikePressed(Thread thread) async {
    try {
      final isLiked = await _threadsService.toggleLike(thread.id);

      if (mounted) {
        setState(() {
          final index = _threads.indexWhere((t) => t.id == thread.id);
          if (index != -1) {
            _threads[index] = _threads[index].copyWith(
              likesCount:
                  isLiked
                      ? _threads[index].likesCount + 1
                      : _threads[index].likesCount - 1,
            );
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to toggle like: $e')));
      }
    }
  }

  void _showCreateThreadDialog() {
    showDialog(
      context: context,
      builder:
          (context) => CreateThreadDialog(
            onThreadCreated: (newThread) {
              setState(() {
                _threads.insert(0, newThread);
              });
            },
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;

    return Scaffold(
      backgroundColor: theme.primary,
      body: _buildContent(theme),
      floatingActionButton: FloatingActionButton(
        backgroundColor: theme.secondary,
        onPressed: _showCreateThreadDialog,
        child: Icon(Icons.add, color: theme.onSecondary),
      ),
    );
  }

  Widget _buildContent(ColorScheme theme) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: theme.error),
            const SizedBox(height: 16),
            Text('$_error', style: TextStyle(color: theme.error)),
            const SizedBox(height: 8),
            ElevatedButton(onPressed: _loadThreads, child: const Text('Retry')),
          ],
        ),
      );
    }
    if (_threads.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.forum, size: 64, color: theme.onPrimary),
            const SizedBox(height: 12),
            const Text('No threads found'),
          ],
        ),
      );
    }
    return RefreshIndicator(
      onRefresh: _loadThreads,
      child: ListView.builder(
        controller: _scrollController,
        itemCount: _threads.length + (_isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= _threads.length) {
            return const Padding(
              padding: EdgeInsets.all(16),
              child: Center(child: CircularProgressIndicator()),
            );
          }
          final thread = _threads[index];
          return FadeTransition(
            opacity: _animController,
            child: ListTile(
              leading: CircleAvatar(
                backgroundImage:
                    thread.avatarUrl != null
                        ? NetworkImage(thread.avatarUrl!)
                        : null,
                backgroundColor:
                    thread.avatarUrl == null ? theme.secondary : null,
                child:
                    thread.avatarUrl == null
                        ? Text(
                          thread.initials,
                          style: TextStyle(color: theme.onSecondary),
                        )
                        : null,
              ),
              title: Text(thread.username ?? 'Anonymous'),
              subtitle: Text(
                thread.content,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              trailing: IconButton(
                icon: const Icon(Icons.thumb_up_alt_outlined),
                onPressed: () => _onLikePressed(thread),
              ),
            ),
          );
        },
      ),
    );
  }
}
