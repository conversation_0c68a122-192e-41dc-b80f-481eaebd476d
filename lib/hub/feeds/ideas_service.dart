import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../../services/auth_client.dart';
import '../../core/constants.dart';
import 'models/ideas_model.dart';

class ThreadsService {
  static final ThreadsService _instance = ThreadsService._();
  factory ThreadsService() => _instance;
  ThreadsService._();

  final _httpClient = http.Client();
  final _authClient = AuthClient();

  // API endpoints
  static const String _postsEndpoint = '/api/posts';
  static const String _commentsEndpoint = '/api/comments';
  static const String _likesEndpoint = '/api/likes';
  static const String _sharesEndpoint = '/api/shares';

  final Map<String, List<Thread>> _cache = {};
  final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheDuration = Duration(minutes: 5);

  /// Get base URL for API
  String get baseUrl => AuthServiceConfig.baseUrl;

  /// Create headers with authentication
  Map<String, String> _createHeaders() {
    final headers = <String, String>{
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    final token = _authClient.accessToken;
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }

    return headers;
  }

  void clearCache() {
    _cache.clear();
    _cacheTimestamps.clear();
  }

  bool _isCacheValid(String key) {
    final ts = _cacheTimestamps[key];
    return ts != null && DateTime.now().difference(ts) < _cacheDuration;
  }

  Future<List<Thread>> fetchThreads({
    int limit = 20,
    int offset = 0,
    bool useCache = true,
  }) async {
    final key = 'threads_${limit}_$offset';

    if (useCache && _isCacheValid(key) && _cache.containsKey(key)) {
      return _cache[key]!;
    }

    try {
      final page = (offset ~/ limit) + 1;
      final uri = Uri.parse('$baseUrl$_postsEndpoint').replace(
        queryParameters: {
          'page': page.toString(),
          'pageSize': limit.toString(),
        },
      );

      final response = await _httpClient
          .get(uri, headers: _createHeaders())
          .timeout(AuthServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final List<dynamic> postsData = data['posts'] ?? data;
        final threads = postsData.map((e) => Thread.fromApiJson(e)).toList();

        _cache[key] = threads;
        _cacheTimestamps[key] = DateTime.now();
        return threads;
      } else {
        throw ThreadException(
          'Server returned ${response.statusCode}: ${response.body}',
        );
      }
    } catch (e) {
      final message = formatApiError(e);
      debugPrint('Error fetching threads: $message');
      throw ThreadException('Failed to fetch threads: $message');
    }
  }

  Future<Thread> createThread(String content) async {
    if (!_authClient.isAuthenticated) {
      throw ThreadException('Authentication required.');
    }

    try {
      final payload = {'content': content.trim()};

      final response = await _httpClient
          .post(
            Uri.parse('$baseUrl$_postsEndpoint'),
            headers: _createHeaders(),
            body: jsonEncode(payload),
          )
          .timeout(AuthServiceConfig.requestTimeout);

      if (response.statusCode == 201 || response.statusCode == 200) {
        final data = jsonDecode(response.body);
        clearCache();
        return Thread.fromApiJson(data);
      } else {
        throw ThreadException(
          'Server returned ${response.statusCode}: ${response.body}',
        );
      }
    } catch (e) {
      final message = formatApiError(e);
      debugPrint('Error creating thread: $message');
      throw ThreadException('Failed to create thread: $message');
    }
  }

  Future<Thread> updateThread({
    required String threadId,
    required String content,
  }) async {
    if (!_authClient.isAuthenticated) {
      throw ThreadException('Authentication required.');
    }

    try {
      final payload = {'content': content.trim()};

      final response = await _httpClient
          .put(
            Uri.parse('$baseUrl$_postsEndpoint/$threadId'),
            headers: _createHeaders(),
            body: jsonEncode(payload),
          )
          .timeout(AuthServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        clearCache();
        return Thread.fromApiJson(data);
      } else {
        throw ThreadException(
          'Server returned ${response.statusCode}: ${response.body}',
        );
      }
    } catch (e) {
      final message = formatApiError(e);
      debugPrint('Error updating thread: $message');
      throw ThreadException('Failed to update thread: $message');
    }
  }

  Future<void> deleteThread(String threadId) async {
    if (!_authClient.isAuthenticated) {
      throw ThreadException('Authentication required.');
    }

    try {
      final response = await _httpClient
          .delete(
            Uri.parse('$baseUrl$_postsEndpoint/$threadId'),
            headers: _createHeaders(),
          )
          .timeout(AuthServiceConfig.requestTimeout);

      if (response.statusCode == 200 || response.statusCode == 204) {
        clearCache();
      } else {
        throw ThreadException(
          'Server returned ${response.statusCode}: ${response.body}',
        );
      }
    } catch (e) {
      final message = formatApiError(e);
      debugPrint('Error deleting thread: $message');
      throw ThreadException('Failed to delete thread: $message');
    }
  }

  Future<bool> toggleLike(String threadId) async {
    if (!_authClient.isAuthenticated) {
      throw ThreadException('Authentication required.');
    }

    try {
      final payload = {'postId': threadId};

      final response = await _httpClient
          .post(
            Uri.parse('$baseUrl$_likesEndpoint/toggle'),
            headers: _createHeaders(),
            body: jsonEncode(payload),
          )
          .timeout(AuthServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return data['liked'] as bool? ?? false;
      } else {
        throw ThreadException(
          'Server returned ${response.statusCode}: ${response.body}',
        );
      }
    } catch (e) {
      final message = formatApiError(e);
      debugPrint('Error toggling like: $message');
      throw ThreadException('Failed to toggle like: $message');
    }
  }

  Future<void> recordShare(String threadId) async {
    if (!_authClient.isAuthenticated) {
      throw ThreadException('Authentication required.');
    }

    try {
      final payload = {'postId': threadId};

      final response = await _httpClient
          .post(
            Uri.parse('$baseUrl$_sharesEndpoint'),
            headers: _createHeaders(),
            body: jsonEncode(payload),
          )
          .timeout(AuthServiceConfig.requestTimeout);

      if (response.statusCode != 201 && response.statusCode != 200) {
        throw ThreadException(
          'Server returned ${response.statusCode}: ${response.body}',
        );
      }
    } catch (e) {
      final message = formatApiError(e);
      debugPrint('Error recording share: $message');
      throw ThreadException('Failed to record share: $message');
    }
  }

  Future<List<Thread>> getUserThreads({String? userId, int limit = 20}) async {
    final uid = userId ?? _authClient.currentUser?.id;
    if (uid == null) throw ThreadException('User ID required.');

    try {
      final uri = Uri.parse(
        '$baseUrl$_postsEndpoint/user/$uid',
      ).replace(queryParameters: {'limit': limit.toString()});

      final response = await _httpClient
          .get(uri, headers: _createHeaders())
          .timeout(AuthServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final List<dynamic> postsData = data['posts'] ?? data;
        return postsData.map((e) => Thread.fromApiJson(e)).toList();
      } else {
        throw ThreadException(
          'Server returned ${response.statusCode}: ${response.body}',
        );
      }
    } catch (e) {
      final message = formatApiError(e);
      debugPrint('Error fetching user threads: $message');
      throw ThreadException('Failed to fetch user threads: $message');
    }
  }

  Future<List<Thread>> searchThreads(String query) async {
    try {
      // For now, we'll use the regular posts endpoint and filter client-side
      //  Implement server-side search when available
      final uri = Uri.parse('$baseUrl$_postsEndpoint').replace(
        queryParameters: {
          'pageSize': '100', // Get more results for search
        },
      );

      final response = await _httpClient
          .get(uri, headers: _createHeaders())
          .timeout(AuthServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final List<dynamic> postsData = data['posts'] ?? data;
        final allThreads = postsData.map((e) => Thread.fromApiJson(e)).toList();

        // Client-side filtering for now
        final filteredThreads =
            allThreads
                .where(
                  (thread) => thread.content.toLowerCase().contains(
                    query.toLowerCase(),
                  ),
                )
                .toList();

        return filteredThreads;
      } else {
        throw ThreadException(
          'Server returned ${response.statusCode}: ${response.body}',
        );
      }
    } catch (e) {
      final message = formatApiError(e);
      debugPrint('Error searching threads: $message');
      throw ThreadException('Failed to search threads: $message');
    }
  }

  Future<void> addComment({
    required String threadId,
    required String content,
  }) async {
    if (!_authClient.isAuthenticated) {
      throw ThreadException('Authentication required.');
    }

    try {
      final payload = {'postId': threadId, 'content': content.trim()};

      final response = await _httpClient
          .post(
            Uri.parse('$baseUrl$_commentsEndpoint'),
            headers: _createHeaders(),
            body: jsonEncode(payload),
          )
          .timeout(AuthServiceConfig.requestTimeout);

      if (response.statusCode != 201 && response.statusCode != 200) {
        throw ThreadException(
          'Server returned ${response.statusCode}: ${response.body}',
        );
      }
    } catch (e) {
      final message = formatApiError(e);
      debugPrint('Error adding comment: $message');
      throw ThreadException('Failed to add comment: $message');
    }
  }

  Future<List<Comment>> fetchComments(String threadId) async {
    try {
      final response = await _httpClient
          .get(
            Uri.parse('$baseUrl$_commentsEndpoint/post/$threadId'),
            headers: _createHeaders(),
          )
          .timeout(AuthServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final List<dynamic> commentsData = data['comments'] ?? data;
        return commentsData.map((e) => Comment.fromApiJson(e)).toList();
      } else {
        throw ThreadException(
          'Server returned ${response.statusCode}: ${response.body}',
        );
      }
    } catch (e) {
      final message = formatApiError(e);
      debugPrint('Error fetching comments: $message');
      throw ThreadException('Failed to fetch comments: $message');
    }
  }

  /// Get posts from followed users (requires authentication)
  Future<List<Thread>> getFollowingFeed({int limit = 20, int page = 1}) async {
    if (!_authClient.isAuthenticated) {
      throw ThreadException('Authentication required.');
    }

    try {
      final uri = Uri.parse('$baseUrl$_postsEndpoint/following').replace(
        queryParameters: {
          'page': page.toString(),
          'pageSize': limit.toString(),
        },
      );

      final response = await _httpClient
          .get(uri, headers: _createHeaders())
          .timeout(AuthServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final List<dynamic> postsData = data['posts'] ?? data;
        return postsData.map((e) => Thread.fromApiJson(e)).toList();
      } else {
        throw ThreadException(
          'Server returned ${response.statusCode}: ${response.body}',
        );
      }
    } catch (e) {
      final message = formatApiError(e);
      debugPrint('Error fetching following feed: $message');
      throw ThreadException('Failed to fetch following feed: $message');
    }
  }

  /// Get trending posts
  Future<List<Thread>> getTrendingPosts({int limit = 20, int page = 1}) async {
    try {
      final uri = Uri.parse('$baseUrl$_postsEndpoint/trending').replace(
        queryParameters: {
          'page': page.toString(),
          'pageSize': limit.toString(),
        },
      );

      final response = await _httpClient
          .get(uri, headers: _createHeaders())
          .timeout(AuthServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final List<dynamic> postsData = data['posts'] ?? data;
        return postsData.map((e) => Thread.fromApiJson(e)).toList();
      } else {
        throw ThreadException(
          'Server returned ${response.statusCode}: ${response.body}',
        );
      }
    } catch (e) {
      final message = formatApiError(e);
      debugPrint('Error fetching trending posts: $message');
      throw ThreadException('Failed to fetch trending posts: $message');
    }
  }

  /// Get posts by stock symbol
  Future<List<Thread>> getPostsBySymbol(
    String symbol, {
    int limit = 20,
    int page = 1,
  }) async {
    try {
      final uri = Uri.parse('$baseUrl$_postsEndpoint/symbol/$symbol').replace(
        queryParameters: {
          'page': page.toString(),
          'pageSize': limit.toString(),
        },
      );

      final response = await _httpClient
          .get(uri, headers: _createHeaders())
          .timeout(AuthServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final List<dynamic> postsData = data['posts'] ?? data;
        return postsData.map((e) => Thread.fromApiJson(e)).toList();
      } else {
        throw ThreadException(
          'Server returned ${response.statusCode}: ${response.body}',
        );
      }
    } catch (e) {
      final message = formatApiError(e);
      debugPrint('Error fetching posts by symbol: $message');
      throw ThreadException('Failed to fetch posts by symbol: $message');
    }
  }

  /// Get trending stock symbols
  Future<List<String>> getTrendingSymbols() async {
    try {
      final response = await _httpClient
          .get(
            Uri.parse('$baseUrl$_postsEndpoint/trending-symbols'),
            headers: _createHeaders(),
          )
          .timeout(AuthServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final List<dynamic> symbolsData = data['symbols'] ?? data;
        return symbolsData.cast<String>();
      } else {
        throw ThreadException(
          'Server returned ${response.statusCode}: ${response.body}',
        );
      }
    } catch (e) {
      final message = formatApiError(e);
      debugPrint('Error fetching trending symbols: $message');
      throw ThreadException('Failed to fetch trending symbols: $message');
    }
  }

  /// Dispose resources
  void dispose() {
    _httpClient.close();
  }
}

/// Custom exception
class ThreadException implements Exception {
  final String message;
  ThreadException(this.message);

  @override
  String toString() => 'ThreadException: $message';
}

/// Central error formatter for API errors
String formatApiError(Object error) {
  if (error is TimeoutException) {
    return 'The request timed out.';
  } else if (error is FormatException) {
    return 'Response format error.';
  } else if (error is http.ClientException) {
    return 'Network error: ${error.message}';
  } else {
    return error.toString();
  }
}
