import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:app_links/app_links.dart';
import 'package:http/http.dart' as http;
import '../services/auth_client.dart';
import 'widgets/phone_sign_in.dart';

class AuthScreen extends StatefulWidget {
  final bool shouldReturn;
  const AuthScreen({super.key, this.shouldReturn = false});

  @override
  State<AuthScreen> createState() => _AuthScreenState();
}

class _AuthScreenState extends State<AuthScreen> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final AppLinks appLinks = AppLinks();
  StreamSubscription<Uri>? _linkSubscription;
  late AuthClient _authClient;

  bool isLoading = false;
  bool isSignUp = true;
  bool showtext = true;

  @override
  void initState() {
    super.initState();
    _authClient = AuthClient();
    _authClient.initialize();
    _handleIncomingLinks();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _linkSubscription?.cancel();
    super.dispose();
  }

  void _handleIncomingLinks() {
    _linkSubscription = appLinks.uriLinkStream.listen(
      (Uri uri) {
        debugPrint('Received deep link: $uri');
        // Handle deep link authentication if needed
        // This is typically used for OAuth callbacks
      },
      onError: (err) {
        debugPrint('Deep link error: $err');
      },
    );
  }

  Future<void> _auth() async {
    if (isLoading) return;

    final email = _emailController.text.trim();
    final password = _passwordController.text.trim();

    if (email.isEmpty || password.isEmpty) {
      _showError('Please fill in all fields');
      return;
    }

    setState(() => isLoading = true);

    try {
      ServerAuthResult result;

      if (isSignUp) {
        debugPrint('Attempting sign up for: $email');
        result = await _authClient.signUpWithEmail(
          email: email,
          password: password,
        );
      } else {
        debugPrint('Attempting sign in for: $email');
        result = await _authClient.signInWithEmail(
          email: email,
          password: password,
        );
      }

      if (result.isSuccess && mounted) {
        debugPrint('Authentication successful for: $email');
        _navigateAfterSuccess();
      } else {
        debugPrint('Authentication failed for: $email');
        // Use the error message from the result
        final errorMessage =
            result.error ??
            'Authentication failed. Please check your credentials and try again.';
        _showError(errorMessage);
      }
    } catch (e) {
      debugPrint('Auth error for $email: $e');
      String errorMessage = 'Authentication failed. Please try again.';

      // Provide more specific error messages based on the error
      final errorString = e.toString().toLowerCase();
      if (errorString.contains('network') ||
          errorString.contains('connection')) {
        errorMessage = 'Network error. Please check your internet connection.';
      } else if (errorString.contains('timeout')) {
        errorMessage = 'Request timed out. Please try again.';
      } else if (errorString.contains('invalid') ||
          errorString.contains('credentials')) {
        errorMessage = 'Invalid email or password.';
      } else if (errorString.contains('email not confirmed')) {
        errorMessage =
            'Please check your email and click the confirmation link.';
      }

      _showError(errorMessage);
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  Future<void> _signInWithGoogle() async {
    if (isLoading) return;

    setState(() => isLoading = true);

    try {
      // Get OAuth URL from abra-server
      final oauthResult = await _authClient.getOAuthUrl(
        provider: 'google',
        redirectTo:
            'http://abraapp.undeclab.com/auth/callback', // Your app's callback URL
      );

      if (oauthResult.isSuccess && oauthResult.url != null) {
        // Open the OAuth URL in browser/webview
        // For now, we'll show the URL to the user
        // In a production app, you'd open this in a webview or browser
        debugPrint('OAuth URL: ${oauthResult.url}');

        // Implement proper OAuth flow with webview or browser
        // This would involve:
        // 1. Opening the OAuth URL in a webview/browser
        // 2. Handling the callback with the authorization code
        // 3. Calling _authClient.handleOAuthCallback with the code

        _showError('Google Sign-In URL generated. Check console for URL.');
      } else {
        _showError(oauthResult.error ?? 'Failed to get Google Sign-In URL');
      }
    } catch (e) {
      debugPrint('Google Sign-In error: $e');
      _showError('Google Sign-In failed. Please try again.');
    } finally {
      if (mounted) setState(() => isLoading = false);
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.red),
    );
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message), backgroundColor: Colors.green),
    );
  }

  Future<void> _testConnection() async {
    try {
      debugPrint('Testing connection to auth server...');

      // Test basic connectivity by making a simple request
      final response = await http
          .post(
            Uri.parse('http://abraapp.undeclab.com/api/auth/login'),
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode({'email': '<EMAIL>', 'password': 'test'}),
          )
          .timeout(const Duration(seconds: 10));

      debugPrint('Connection test response: ${response.statusCode}');
      debugPrint('Connection test body: ${response.body}');

      if (response.statusCode == 400) {
        // 400 is expected for invalid credentials, but means server is reachable
        _showSuccess('✅ Server connection successful!');
      } else {
        _showSuccess('✅ Server reachable (Status: ${response.statusCode})');
      }
    } catch (e) {
      debugPrint('Connection test failed: $e');
      _showError('❌ Connection failed: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return SafeArea(
      child: Scaffold(
        backgroundColor: theme.primary,
        appBar: AppBar(
          title: Text(
            isSignUp ? 'Sign Up' : 'Login',
            style: TextStyle(color: theme.onPrimary),
          ),
        ),
        body: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TextField(
                showCursor: true,
                cursorColor: theme.onPrimary,
                textAlign: TextAlign.center,
                controller: _emailController,
                decoration: InputDecoration(
                  labelText: 'Email',
                  labelStyle: TextStyle(color: theme.onPrimary),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: theme.onPrimary),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(20),
                    borderSide: BorderSide(color: theme.onPrimary),
                  ),
                ),
              ),
              SizedBox(height: 15),
              TextField(
                showCursor: true,
                cursorColor: theme.onPrimary,
                textAlign: TextAlign.center,
                controller: _passwordController,
                obscureText: showtext,
                decoration: InputDecoration(
                  labelText: 'Password',
                  labelStyle: TextStyle(color: theme.onPrimary),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: theme.onPrimary),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(20),
                    borderSide: BorderSide(color: theme.onPrimary),
                  ),
                  suffixIcon: IconButton(
                    icon: Icon(
                      showtext
                          ? Icons.visibility_outlined
                          : Icons.visibility_off_outlined,
                    ),
                    onPressed: () {
                      setState(() {
                        showtext = !showtext;
                      });
                    },
                  ),
                ),
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: isLoading ? null : _auth,
                child:
                    isLoading
                        ? const CircularProgressIndicator()
                        : Text(
                          isSignUp ? 'Create Account' : 'Login',
                          style: TextStyle(color: theme.onPrimary),
                        ),
              ),
              const SizedBox(height: 10),
              Container(
                width: 180,
                height: 45,
                margin: EdgeInsets.zero,
                padding: EdgeInsets.zero,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                ),
                child: ElevatedButton(
                  onPressed: isLoading ? null : _signInWithGoogle,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.primary,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    elevation: 8,
                    padding: EdgeInsets.zero,
                    minimumSize: const Size(double.infinity, 48),
                  ),
                  child: Image.asset(
                    isDark
                        ? 'assets/gsignin/glight.png'
                        : 'assets/gsignin/gdark.png',
                    fit: BoxFit.fill,
                    alignment: Alignment.center,
                  ),
                ),
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                style: ButtonStyle(
                  backgroundColor: WidgetStateProperty.all(theme.secondary),
                  foregroundColor: WidgetStateProperty.all(theme.onPrimary),
                ),
                onPressed: showPhoneSignIn,
                child: const Text('Sign In with Phone'),
              ),
              const SizedBox(height: 20),
              TextButton(
                onPressed: () => setState(() => isSignUp = !isSignUp),
                child: Text(
                  isSignUp ? 'Already member? Login' : 'Create an account',
                  style: TextStyle(
                    color: theme.onPrimary,
                    fontWeight: FontWeight.normal,
                  ),
                ),
              ),
              const SizedBox(height: 10),
              // Debug connection test button
              TextButton(
                onPressed: _testConnection,
                child: Text(
                  'Test Server Connection',
                  style: TextStyle(
                    color: theme.onPrimary.withValues(alpha: 0.7),
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Returns a widget for phone sign-in that can be used in modal bottom sheet
  Widget buildPhoneSignInModal() {
    return FractionallySizedBox(
      heightFactor: 0.7,
      child: Material(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        child: PhoneSignInWidget(
          onSuccess: () {
            // Navigate after success
            if (mounted) {
              _navigateAfterSuccess();
            }
          },
        ),
      ),
    );
  }

  /// Returns a standalone phone sign-in widget that can be embedded directly
  Widget buildPhoneSignInWidget() {
    return PhoneSignInWidget(
      onSuccess: () {
        // Navigate after success
        if (mounted) {
          _navigateAfterSuccess();
        }
      },
    );
  }

  void showPhoneSignIn() async {
    // Close keyboard if open
    FocusScope.of(context).unfocus();

    // Show the phone sign-in bottom sheet using the modal widget
    final result = await showModalBottomSheet<bool>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => buildPhoneSignInModal(),
    );

    // Handle result if needed (optional since onSuccess handles navigation)
    if (result == true && mounted) {
      _navigateAfterSuccess();
    }
  }

  void _navigateAfterSuccess() {
    if (widget.shouldReturn) {
      Navigator.pop(context); // Go back
    } else {
      Navigator.pushReplacementNamed(
        context,
        '/home',
      ); // Or wherever your default landing page is
    }
  }
}
