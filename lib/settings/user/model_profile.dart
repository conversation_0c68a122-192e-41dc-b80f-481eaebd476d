import 'package:abra/core/constants.dart';
//=================  PROFILE MODEL =================

class Profile {
  final String id;
  final String username;
  final String fullName;
  final Gender? gender;
  final String? email;
  final String? phone;
  final String? avatarUrl;
  final String? brokerId;
  final String? countryCode;
  final String? region;
  final String? city;
  final String? timezone;
  final String? locale;
  final Lang preferredLanguage;
  final Currency preferredCurrency;
  final String themeMode;
  final Plan plan;
  final bool verified;
  final DateTime createdAt;
  final DateTime updatedAt;

  Profile({
    required this.id,
    required this.username,
    required this.fullName,
    this.gender,
    this.email,
    this.phone,
    this.avatarUrl,
    this.brokerId,
    this.countryCode,
    this.region,
    this.city,
    this.timezone,
    this.locale,
    this.preferredLanguage = Lang.es,
    this.preferredCurrency = Currency.tzs,
    this.themeMode = 'system',
    this.plan = Plan.free,
    this.verified = false,
    required this.createdAt,
    required this.updatedAt,
  });

  bool get completeProfile =>
      username.isNotEmpty || fullName.isNotEmpty || verified;
  factory Profile.fromMap(Map<String, dynamic> map) {
    return Profile(
      id: map['id'] ?? map['user_id'] ?? '',
      username: map['username'] ?? '',
      fullName: map['full_name'] ?? '',
      gender: map['gender'] == 'male' ? Gender.male : Gender.female,
      email: map['email'],
      phone: map['phone'],
      avatarUrl: map['avatar_url'],
      brokerId: map['broker_id'],
      countryCode: map['country_code'],
      region: map['region'],
      city: map['city'],
      timezone: map['timezone'],
      locale: map['locale'],
      preferredLanguage: _parseLang(map['preferred_language']) ?? Lang.sw,
      preferredCurrency:
          _parseCurrency(map['preferred_currency']) ?? Currency.tzs,
      themeMode: map['theme_mode'] ?? 'system',
      plan: _parsePlan(map['plan']) ?? Plan.free,
      verified: map['verified'] ?? false,
      createdAt: DateTime.tryParse(map['created_at'] ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(map['updated_at'] ?? '') ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'username': username,
      'full_name': fullName,
      'gender': gender,
      'email': email,
      'phone': phone,
      'avatar_url': avatarUrl,
      'broker_id': brokerId,
      'country_code': countryCode,
      'region': region,
      'city': city,
      'timezone': timezone,
      'locale': locale,
      'preferred_language': preferredLanguage,
      'preferred_currency': preferredCurrency,
      'theme_mode': themeMode,
      'plan': plan,
      'verified': verified,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Parse string to Lang enum
  static Lang? _parseLang(dynamic value) {
    if (value == null) return null;
    final stringValue = value.toString().toLowerCase();
    for (Lang lang in Lang.values) {
      if (lang.name.toLowerCase() == stringValue) {
        return lang;
      }
    }
    return null;
  }

  /// Parse string to Currency enum
  static Currency? _parseCurrency(dynamic value) {
    if (value == null) return null;
    final stringValue = value.toString().toLowerCase();
    for (Currency currency in Currency.values) {
      if (currency.name.toLowerCase() == stringValue) {
        return currency;
      }
    }
    return null;
  }

  /// Parse string to Plan enum
  static Plan? _parsePlan(dynamic value) {
    if (value == null) return null;
    final stringValue = value.toString().toLowerCase();
    for (Plan plan in Plan.values) {
      if (plan.name.toLowerCase() == stringValue) {
        return plan;
      }
    }
    return null;
  }
}

// Enums
enum Gender { male, female }

enum Plan { free, basic, pro }
