# Profile Service Refinement - Server Endpoints Integration

This document describes the refinement of the profile management system to use abra-servers endpoints instead of local storage and placeholder methods.

## Key Refinements Made

### 🔧 **New ProfileService Implementation**

#### 1. **Server-Side Profile Management**
- **`GET /api/auth/user`** - Fetch current user profile from server
- **`PUT /api/auth/user`** - Update user profile on server
- **`POST /api/auth/profile/link-broker`** - Link broker to user profile
- Proper JWT authentication for all requests
- Comprehensive error handling with user-friendly messages

#### 2. **Enhanced Profile Operations**
```dart
// Get user profile from server
Future<Profile?> getUserProfile()

// Update profile with server validation
Future<Profile?> updateUserProfile({
  String? fullName,
  String? username,
  String? avatarUrl,
  // ... other optional fields
})

// Link broker to profile
Future<bool> linkBroker(String brokerId)
```

#### 3. **Authentication Integration**
- Uses `AuthClient` for JWT token management
- Automatic Authorization headers for all requests
- Proper handling of 401 (Unauthorized) responses
- Token refresh integration

### 🔄 **Migration from AuthService to ProfileService**

#### Before (AuthService - Local/Placeholder)
```dart
// Old implementation using local storage
final (profile, avatarUrl) = await AuthService.getProfileWithAvatar();
final preferences = await AuthService.loadPreferences();
await AuthService.updatePreference('gender', 'Male');
final updatedProfile = await AuthService.upsertProfile(
  username: username,
  fullName: fullName,
  avatarUrl: avatarUrl,
);
```

#### After (ProfileService - Server Endpoints)
```dart
// New implementation using server endpoints
final (profile, avatarUrl) = await _profileService.getProfileWithAvatar();
final preferences = await _profileService.getUserPreferences();
await _profileService.updatePreference('gender', 'Male');
final updatedProfile = await _profileService.updateUserProfile(
  username: username,
  fullName: fullName,
  avatarUrl: avatarUrl,
);
```

### 📊 **Server Endpoint Mapping**

| Operation | Old Method | New Endpoint | Status |
|-----------|------------|--------------|--------|
| Get Profile | Local storage | `GET /api/auth/user` | ✅ Implemented |
| Update Profile | Local placeholder | `PUT /api/auth/user` | ✅ Implemented |
| Link Broker | Server endpoint | `POST /api/auth/profile/link-broker` | ✅ Implemented |
| Upload Avatar | Placeholder | File upload endpoint | 🚧 TODO |
| Delete Avatar | Placeholder | File deletion endpoint | 🚧 TODO |
| Username Check | Placeholder | Validation endpoint | 🚧 TODO |
| Preferences | Local storage | Server preferences | 🚧 TODO |

### 🔒 **Enhanced Security & Error Handling**

#### Authentication
```dart
Map<String, String> _createHeaders() {
  final headers = <String, String>{
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  final token = _authClient.accessToken;
  if (token != null) {
    headers['Authorization'] = 'Bearer $token';
  }

  return headers;
}
```

#### Error Handling
```dart
// Comprehensive error handling with specific status codes
if (response.statusCode == 200) {
  // Success
} else if (response.statusCode == 401) {
  throw Exception('Unauthorized: Please sign in again');
} else if (response.statusCode == 409) {
  throw Exception('Username already exists');
} else {
  throw Exception('Server error: ${response.statusCode}');
}
```

### 🎯 **UI Integration Updates**

#### Profile Screen Refinements
- Uses `ProfileService` instead of `AuthService`
- Better error messages for server operations
- Proper loading states for server requests
- Graceful handling of network errors

#### Key Changes in `edit_profile.dart`
```dart
class _ProfileScreenState extends State<ProfileScreen> {
  final _profileService = ProfileService();

  Future<void> _loadProfile() async {
    // Load from server instead of local storage
    final (profile, avatarUrl) = await _profileService.getProfileWithAvatar();
    final preferences = await _profileService.getUserPreferences();
    // ...
  }

  Future<void> _saveProfile() async {
    // Update on server with proper validation
    final updatedProfile = await _profileService.updateUserProfile(
      fullName: fullName.isNotEmpty ? fullName : null,
      username: username.isNotEmpty ? username : null,
      avatarUrl: _avatarUrl,
    );
    // ...
  }
}
```

### 🚧 **TODO: Pending Server Implementations**

#### 1. **File Upload Endpoints**
```dart
// TODO: Implement in abra-servers
POST /api/auth/avatar/upload
DELETE /api/auth/avatar/{id}
```

#### 2. **Username Validation**
```dart
// TODO: Implement in abra-servers
GET /api/auth/username/check?username={username}
```

#### 3. **Server-Side Preferences**
```dart
// TODO: Implement in abra-servers
GET /api/auth/preferences
PUT /api/auth/preferences
```

### 📱 **User Experience Improvements**

#### 1. **Better Error Messages**
- "Username already exists" for 409 conflicts
- "Unauthorized: Please sign in again" for 401 errors
- "Avatar upload not yet implemented on server" for missing features

#### 2. **Loading States**
- Proper loading indicators during server requests
- Disabled UI elements during save operations
- Graceful handling of network timeouts

#### 3. **Validation**
- Server-side validation for profile updates
- Client-side validation for required fields
- Username availability checking (placeholder)

### 🔧 **Configuration**

#### Server Configuration
```dart
class AuthServiceConfig {
  static const String baseUrl = 'http://abraapp.undeclab.com';
  static const String authEndpoint = '/api/auth';
  static const String profileEndpoint = '/api/auth/profile';
  static const String linkBrokerEndpoint = '/api/auth/profile/link-broker';
  static const Duration requestTimeout = Duration(seconds: 30);
}
```

### 🧪 **Testing Considerations**

#### Unit Tests Needed
```dart
// Test ProfileService methods
test('should fetch user profile from server', () async {
  // Mock server response
  // Test ProfileService.getUserProfile()
});

test('should update user profile on server', () async {
  // Mock server response
  // Test ProfileService.updateUserProfile()
});

test('should handle authentication errors', () async {
  // Mock 401 response
  // Test error handling
});
```

#### Integration Tests
- Test complete profile update flow
- Test error handling with various server responses
- Test authentication token management

### 🚀 **Benefits of Refinement**

1. **Server-Centric Architecture**: All profile data managed on server
2. **Proper Authentication**: JWT tokens for all requests
3. **Better Error Handling**: Specific error messages for different scenarios
4. **Scalability**: Ready for multi-device synchronization
5. **Security**: Server-side validation and authorization
6. **Maintainability**: Clear separation of concerns

### 📋 **Migration Checklist**

- ✅ Created ProfileService with server endpoints
- ✅ Updated edit_profile.dart to use ProfileService
- ✅ Implemented proper authentication headers
- ✅ Added comprehensive error handling
- ✅ Updated UI for better user experience
- 🚧 TODO: Implement file upload endpoints on server
- 🚧 TODO: Implement username validation endpoint
- 🚧 TODO: Move preferences to server-side storage
- 🚧 TODO: Add comprehensive unit tests
- 🚧 TODO: Add integration tests

### 🔗 **Related Files**

- `lib/services/profile_service.dart` - New ProfileService implementation
- `lib/settings/user/edit_profile.dart` - Updated profile editing UI
- `lib/services/auth_client.dart` - Authentication client integration
- `lib/core/constants.dart` - Configuration constants

The refined profile service provides a solid foundation for server-side profile management while maintaining backward compatibility and providing clear paths for future enhancements.
