import 'package:flutter/material.dart';
import '../services/auth_client.dart';

enum StartupDestination { auth, main }

class SplashService {
  static final SplashService _instance = SplashService._internal();
  factory SplashService() => _instance;
  SplashService._internal();

  Future<StartupDestination> getStartupDestination() async {
    debugPrint('SplashService: Starting destination check...');

    // Check AuthClient first (server-side auth)
    final authClient = AuthClient();
    await authClient.initialize();

    debugPrint(
      'SplashService: AuthClient authenticated: ${authClient.isAuthenticated}',
    );

    try {
      if (!authClient.isAuthenticated) {
        debugPrint('SplashService: Not authenticated, returning auth');
        return StartupDestination.auth;
      } else {
        debugPrint('SplashService: Authenticated, going to main screen');
        return StartupDestination.main;
      }
    } catch (e) {
      debugPrint('SplashService: Error checking auth: $e');
      return StartupDestination.main;
    }
  }
}
