import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'connectivity_service.dart';

/// Provides the singleton ConnectivityService instance
final connectivityServiceProvider = Provider<ConnectivityService>((ref) {
  final service = ConnectivityService();
  service.initialize();
  ref.onDispose(service.dispose);
  return service;
});

/// Listens to changes in **real internet availability**
final connectivityStreamProvider = StreamProvider<bool>((ref) {
  final service = ref.watch(connectivityServiceProvider);
  return service.connectionChange;
});

/// Optional overrideable state for UI logic (e.g. retry button state)
final hasConnectionProvider = StateProvider<bool>((ref) {
  final asyncStatus = ref.watch(connectivityStreamProvider);
  return asyncStatus.maybeWhen(
    data: (value) => value,
    orElse: () => true, // fallback while loading
  );
});