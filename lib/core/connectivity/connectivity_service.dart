import 'dart:async';
import 'dart:io';
import 'package:connectivity_plus/connectivity_plus.dart';

class ConnectivityService {
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();

  final _connectivity = Connectivity();
  final _controller = StreamController<bool>.broadcast();

  bool _hasInternet = true;
  bool get hasInternet => _hasInternet;

  Stream<bool> get connectionChange => _controller.stream;

  void initialize() {
    _connectivity.onConnectivityChanged.listen((_) {
      checkInternetAccess();
    });
    checkInternetAccess(); // initial check
  }

  Future<bool> checkInternetAccess() async {
    final previousStatus = _hasInternet;
    _hasInternet = await hasRealInternet();

    if (previousStatus != _hasInternet) {
      _controller.add(_hasInternet);
    }

    return _hasInternet;
  }

  Future<bool> hasRealInternet() async {
    try {
      final result = await InternetAddress.lookup('example.com')
          .timeout(const Duration(seconds: 5));
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (_) {
      return false;
    }
  }

  void dispose() {
    _controller.close();
  }
}