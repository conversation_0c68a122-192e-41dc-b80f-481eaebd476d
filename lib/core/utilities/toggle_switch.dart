import 'package:flutter/material.dart';



class TapToggle extends StatefulWidget {
  final List<Toggle> toggles;
  final double totalWidth;
  final double toggleWidth;
  final double totalHeight;
  final double toggleHeight;
  final int initialIndex;
  final Function(int) onToggleTap;


  const TapToggle({
    super.key,
    required this.toggles,
    required this.totalWidth,
    required this.toggleWidth,
    required this.totalHeight,
    required this.toggleHeight,
    this.initialIndex = 1,
    required this.onToggleTap
  });

  @override
  TapToggleState createState() => TapToggleState();
}

class TapToggleState extends State<TapToggle> {
  late List<Toggle> _toggleState;

  @override
  void initState() {
    super.initState();
    _toggleState = widget.toggles.map((toggle) => toggle.copyWith(isOn: false)).toList();
    if (_toggleState.isNotEmpty && widget.initialIndex >= 0 && widget.initialIndex < _toggleState.length) {
      _toggleState[widget.initialIndex] = _toggleState[widget.initialIndex].copyWith(isOn: true);
    }
  }

  
  @override
  void didUpdateWidget(TapToggle oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.initialIndex != widget.initialIndex) {
      setState(() {
        _toggleState = _toggleState.map((toggle) => toggle.copyWith(isOn: false)).toList();
        if (widget.initialIndex >= 0 && widget.initialIndex < _toggleState.length) {
          _toggleState[widget.initialIndex] = _toggleState[widget.initialIndex].copyWith(isOn: true);
        }
      });
    }
  }

  void _handleToggle(int index) {
    setState(() {
      _toggleState = _toggleState.map((toggle) => toggle.copyWith(isOn: false)).toList();
      _toggleState[index] = _toggleState[index].copyWith(isOn: true);
    });
    widget.onToggleTap(index);
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      width: widget.totalWidth,
      height: widget.totalHeight,
      decoration: BoxDecoration(
        color: colorScheme.primary,
        borderRadius: BorderRadius.circular(widget.totalHeight / 2),
        border: Border.all(color: colorScheme.secondary),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: List.generate(_toggleState.length, (index) {
          return ToggleButton(
            toggle: _toggleState[index],
            width: widget.toggleWidth,
            height: widget.toggleHeight,
            primaryOnColor: colorScheme.onPrimary,  
            borderColor: colorScheme.primary,
            onTap: () => _handleToggle(index),
          );
        }),
      ),
    );
  }
}

class ToggleButton extends StatelessWidget {
  // Use const for animation duration to prevent recreation
  static const Duration _animationDuration = Duration(milliseconds: 300);
  static const Curve _animationCurve = Curves.easeOut;

  final Toggle toggle;
  final double width;
  final double height;
  final VoidCallback onTap;
  final Color primaryOnColor;
  final Color borderColor;

  const ToggleButton({
    super.key,
    required this.toggle,
    required this.width,
    required this.height,
    required this.onTap,
    required this.primaryOnColor,
    required this.borderColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;
    
    // Fix: Replace margin with padding to avoid ParentDataWidget error
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: _animationDuration,
        curve: _animationCurve,
        width: width,
        height: height,
        padding: const EdgeInsets.all(0.5), // Changed to padding
        decoration: BoxDecoration(
          color: toggle.isOn ? primaryOnColor : Colors.transparent,
          borderRadius: BorderRadius.circular(height / 2),
          border: Border.all(color: borderColor),
        ),
        child: Center(
          child: Icon(
            toggle.icon,
            color: toggle.isOn 
              ? theme.primary 
              : theme.onPrimary,
            size: 18,
          ),
        ),
      ),
    );
  }
}

// Toggle Model
class Toggle {
  final String label;
  final IconData icon;
  bool isOn;

  Toggle({
    required this.label,
    required this.icon,
    this.isOn = false,
  });

  Toggle copyWith({bool? isOn}) {
    return Toggle(
      label: label,
      icon: icon,
      isOn: isOn ?? this.isOn,
    );
  }
}