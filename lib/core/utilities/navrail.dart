import 'dart:convert';
import 'package:flutter/services.dart' show rootBundle;
import 'package:flutter/material.dart';
import '../../hub/articles/lesson_model.dart';
import '../../core/constants.dart';
import '../../hub/articles/lesson_page.dart';

class LessonCategoryWithModules {
  final LessonLevel level;
  final List<LessonModule> modules;

  LessonCategoryWithModules({required this.level, required this.modules});
}

class NavRail extends StatefulWidget {
  final int selectedIndex;
  final Function(int) onSelect;
  final VoidCallback onShowAI;
  final List<LessonCategoryWithModules> lessonCategories;
  final ColorScheme theme;
  final LessonModule? selectedModule;
  final Function(LessonModule)? onModuleSelect;
  final Lang userLocale;
  final bool isExpanded;
  final VoidCallback? onToggleExpansion;

  const NavRail({
    super.key,
    required this.selectedIndex,
    required this.onSelect,
    required this.onShowAI,
    required this.lessonCategories,
    required this.theme,
    this.selectedModule,
    this.onModuleSelect,
    this.userLocale = Lang.en,
    required this.isExpanded,
    this.onToggleExpansion,
  });

  @override
  NavRailState createState() => NavRailState();
}

// Map lesson levels to asset paths
final Map<LessonLevel, List<String>> lessonAssets = {
  LessonLevel.beginner: [
    'assets/courses/beginner_module1-3.json',
    'assets/courses/beginner_module4-10.json',
  ],
  LessonLevel.intermediate: [
    'assets/courses/intermediate_module1-3.json',
    'assets/courses/intermediate_module4-7.json',
  ],
  LessonLevel.advanced: [
    'assets/courses/advanced_module1-4.json',
  ],
};

class NavRailState extends State<NavRail> {
  int _expandedLessonIndex = -1;
  final Map<LessonLevel, List<LessonModule>> _lessonHeaders = {};

  Future<void> _loadLessonHeaders(LessonLevel level) async {
    if (_lessonHeaders.containsKey(level)) return; // Already loaded

    List<LessonModule> modules = [];
    for (final asset in lessonAssets[level]!) {
      final jsonString = await rootBundle.loadString(asset);
      final dynamic jsonData = json.decode(jsonString);

      List<dynamic> jsonList;
      if (jsonData is List) {
        // Direct array format
        jsonList = jsonData;
      } else if (jsonData is Map<String, dynamic> && jsonData.containsKey('modules')) {
        // Object with modules key format
        jsonList = jsonData['modules'];
      } else {
        // Unknown format, skip this file
        continue;
      }

      modules.addAll(jsonList.map((e) => LessonModule.fromJson(e, level)));
    }
    setState(() {
      _lessonHeaders[level] = modules;
    });
  }



  void _toggleLessonCategory(int index, LessonLevel level) async {
    if (_expandedLessonIndex == index) {
      setState(() => _expandedLessonIndex = -1);
    } else {
      await _loadLessonHeaders(level);
      setState(() => _expandedLessonIndex = index);
    }
  }

  List<LessonCategoryWithModules> categorizeModules(List<LessonModule> modules) {
    final levels = [LessonLevel.beginner, LessonLevel.intermediate, LessonLevel.advanced];

    return levels.map((level) {
      final group = modules.where((m) => m.level == level).toList();
      return LessonCategoryWithModules(level: level, modules: group);
    }).where((cat) => cat.modules.isNotEmpty).toList();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      width: widget.isExpanded ? (isSmallScreen ? screenWidth * 0.35 : 200) : 60,
      height: MediaQuery.of(context).size.height,
      decoration: BoxDecoration(
        color: widget.theme.tertiary,
        borderRadius: const BorderRadius.only(
          topRight: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
        border: Border(
          right: BorderSide(color: widget.theme.onPrimary),
        ),
        boxShadow: [
          BoxShadow(
            color: widget.theme.onPrimary,
            blurRadius: 6.0,
            blurStyle: BlurStyle.inner,
            offset: const Offset(2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildHeader(),
          if (widget.isExpanded) const Divider(),
          _buildNavItems(),
          if (widget.isExpanded && widget.selectedIndex == 0)
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 10),
                    Text(
                      'Lessons',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: widget.theme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 10),
                    ...LessonLevel.values.asMap().entries.map((entry) {
                      final idx = entry.key;
                      final level = entry.value;
                      final isExpanded = _expandedLessonIndex == idx;
                      final modules = _lessonHeaders[level] ?? [];
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          GestureDetector(
                            onTap: () => _toggleLessonCategory(idx, level),
                            child: Container(
                              padding: const EdgeInsets.all(12),
                              margin: const EdgeInsets.only(bottom: 6),
                              decoration: BoxDecoration(
                                color: isExpanded ? widget.theme.primaryContainer : widget.theme.surface,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: Text(
                                      level.name,
                                      style: TextStyle(
                                        fontWeight: FontWeight.w500,
                                        color: isExpanded
                                            ? widget.theme.onPrimaryContainer
                                            : widget.theme.onSurfaceVariant,
                                      ),
                                    ),
                                  ),
                                  Icon(
                                    isExpanded ? Icons.expand_less : Icons.expand_more,
                                    color: isExpanded
                                        ? widget.theme.onPrimaryContainer
                                        : widget.theme.onSurfaceVariant,
                                  ),
                                ],
                              ),
                            ),
                          ),
                          if (isExpanded)
                            ...modules.map((module) {
                              final title = module.title[widget.userLocale.name] ?? module.title['en']!;
                              return GestureDetector(
                                onTap: () {
                                  // Pass the asset path and module id to ArticleScreen
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (_) => ArticlesScreen(selectedLesson: module),
                                    ),
                                  );
                                },
                                child: Container(
                                  margin: const EdgeInsets.only(left: 16, bottom: 6),
                                  padding: const EdgeInsets.all(10),
                                  decoration: BoxDecoration(
                                    border: Border.all(color: widget.theme.primary),
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  child: Row(
                                    children: [
                                      Icon(Icons.arrow_right, size: 16, color: widget.theme.primary),
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: Text(
                                          title,
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: widget.theme.onSurface,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            }),
                        ],
                      );
                    }),
                  ],
                ),
              ),
            ),
          if (widget.isExpanded && widget.selectedIndex == 1)
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 10),
                    Text(
                      'Ideas',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: widget.theme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 10),
                    _buildIdeasMenu(),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: widget.isExpanded ? const EdgeInsets.all(8) : const EdgeInsets.all(4),
      child: widget.isExpanded
        ? Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              IconButton(
                onPressed: widget.onToggleExpansion,
                icon: Icon(
                  Icons.menu_open_rounded,
                  color: widget.theme.onPrimary,
                ),
                tooltip: "Collapse Sidebar"
              ),
            ],
          )
        : Center(
            child: IconButton(
              onPressed: widget.onToggleExpansion,
              icon: Icon(
                Icons.menu,
                color: widget.theme.onPrimary,
                size: 20,
              ),
              tooltip: "Expand Sidebar",
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(
                minWidth: 40,
                minHeight: 40,
              ),
            ),
          ),
    );
  }

  Widget _buildNavItems() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: Column(
        children: [
          _buildRailItem(
            item: ItemIcon.asset('assets/ai/alim_bot.png'),
            label: widget.isExpanded ? 'Ask Alim!' : '',
            isSelected: false, // This is a separate action, not a screen selection
            onTap: () {
              widget.onShowAI();
            },
          ),
          const SizedBox(height: 8),
          _buildRailItem(
            item: ItemIcon.icon(Icons.settings),
            label: widget.isExpanded ? 'Settings' : '',
            isSelected: widget.selectedIndex == 2,
            onTap: () {
              widget.onSelect(2);
            },
          )
        ],
      ),
    );
  }

  Widget _buildRailItem({
    required ItemIcon item,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected ? widget.theme.primaryContainer : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: widget.isExpanded
          ? Row(
              children: [
                if (item.isAsset)
                  Image.asset(
                    item.assetPath!,
                    height: 32,
                    width: 32,
                    fit: BoxFit.contain,
                  )
                else
                  Icon(
                    item.icon!,
                    color: isSelected ? widget.theme.onPrimary : widget.theme.onSurface,
                  ),
                if (label.isNotEmpty) ...[
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      label,
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: isSelected ? widget.theme.onPrimary : widget.theme.onSurface,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ],
              ],
            )
          : Center(
              child: item.isAsset
                ? Image.asset(
                    item.assetPath!,
                    height: 24,
                    width: 24,
                    fit: BoxFit.contain,
                  )
                : Icon(
                    item.icon!,
                    color: isSelected ? widget.theme.onPrimary : widget.theme.onSurface,
                  ),
            ),
      ),
    );
  }

  Widget _buildIdeasMenu() {
    final ideaCategories = [
      {'title': 'Trading Ideas', 'icon': Icons.trending_up, 'count': 12},
      {'title': 'Market Analysis', 'icon': Icons.analytics, 'count': 8},
      {'title': 'Investment Tips', 'icon': Icons.lightbulb, 'count': 15},
      {'title': 'Risk Management', 'icon': Icons.security, 'count': 6},
      {'title': 'Portfolio Ideas', 'icon': Icons.pie_chart, 'count': 10},
    ];

    return Column(
      children: ideaCategories.map((category) {
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: widget.theme.surface,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: widget.theme.outline.withValues(alpha: 0.3)),
          ),
          child: Row(
            children: [
              Icon(
                category['icon'] as IconData,
                color: widget.theme.primary,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  category['title'] as String,
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: widget.theme.onSurface,
                    fontSize: 14,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: widget.theme.primaryContainer,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${category['count']}',
                  style: TextStyle(
                    color: widget.theme.onPrimaryContainer,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }
}

class ItemIcon {
  final String? assetPath;
  final IconData? icon;

  const ItemIcon.asset(this.assetPath) : icon = null;
  const ItemIcon.icon(this.icon) : assetPath = null;

  bool get isAsset => assetPath != null;
}