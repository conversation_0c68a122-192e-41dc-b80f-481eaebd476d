import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class NotchBottomNavBar extends StatefulWidget {
  final int currentIndex;
  final Function(int) onTap;

  const NotchBottomNavBar({
    required this.currentIndex,
    required this.onTap,
    super.key,
  });

  @override
  State<NotchBottomNavBar> createState() => NotchBottomNavBarState();
}

class NotchBottomNavBarState extends State<NotchBottomNavBar>
    with SingleTickerProviderStateMixin {
  final List<GlobalKey> _iconKeys = List.generate(5, (_) => GlobalKey());
  // GlobalKey for the navigation bar container
  final GlobalKey _navBarKey = GlobalKey();

  final List<NavItemIcon> navIcons = [
    NavItemIcon.asset('assets/icons/watchlist_outlined.png'),
    NavItemIcon.asset('assets/icons/candles_outlined.png'),
    NavItemIcon.asset('assets/icons/portfolio_outlined.png'),
    NavItemIcon.icon(CupertinoIcons.person_2),
    NavItemIcon.icon(CupertinoIcons.settings),
  ];

  final List<NavItemIcon> activeIcons = [
    NavItemIcon.asset('assets/icons/watchlist_rounded.png'),
    NavItemIcon.asset('assets/icons/candles_rounded.png'),
    NavItemIcon.asset('assets/icons/portfolio_rounded.png'),
    NavItemIcon.icon(CupertinoIcons.person_2_fill),
    NavItemIcon.icon(CupertinoIcons.settings_solid), // Example of IconData
  ];

  @override
  void initState() {
    super.initState();
    // Remove the artificial delay and use LayoutBuilder to ensure layout is ready
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateNotchPosition(widget.currentIndex);
    });
  }

  @override
  void didUpdateWidget(covariant NotchBottomNavBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentIndex != widget.currentIndex) {
      _updateNotchPosition(widget.currentIndex);
    }
  }

  void _updateNotchPosition(int index) {
    // Get the RenderBox of the selected icon.
    final RenderBox? iconBox =
        _iconKeys[index].currentContext?.findRenderObject() as RenderBox?;
    // Get the RenderBox of the nav bar container to use as a reference.
    final RenderBox? navBarBox =
        _navBarKey.currentContext?.findRenderObject() as RenderBox?;
    if (iconBox != null && navBarBox != null) {
      // Compute the global positions.
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // This ensures the layout is complete before measuring
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            _updateNotchPosition(widget.currentIndex);
          }
        });
        
        return SizedBox(
          height: 70,
          child: Stack(
            // Attach the key to the Stack so we get the nav bar coordinates.
            key: _navBarKey,
            alignment: Alignment.bottomCenter,
            children: [
              Container(
                height: 70,
                decoration: BoxDecoration(
                  border: Border(
                    top: BorderSide(
                      color: Theme.of(context).colorScheme.onPrimary,
                      width: 0.5,
                    ),
                  ),
                  color: Theme.of(context).colorScheme.primary,
                  borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: buildNavItems(),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  List<Widget> buildNavItems() {
    return List.generate(navIcons.length, (index) {
      final isSelected = widget.currentIndex == index;
      final icon = isSelected ? activeIcons[index] : navIcons[index];
      final double iconSize = isSelected? 35 : 30;
      final color = isSelected
          ? Theme.of(context).colorScheme.onPrimary
          : Theme.of(context).colorScheme.onSecondary;

      return SizedBox(
        key: _iconKeys[index],
        width: 35,
        height: 35,
        child: Center(
          child: IconButton(
            iconSize: iconSize,
            padding: EdgeInsets.zero,
            alignment: Alignment.center,
            icon: icon.isAsset
                ? Image.asset(
                    icon.assetPath!,
                    width: iconSize,
                    height: iconSize,
                    color: color,
                  )
                : Icon(
                    icon.icon!,
                    color: color,
                  ),
            onPressed: () => widget.onTap(index),
          ),
        ),
      );
    });
  }

  Widget getActiveIcon(NavItemIcon navIcon, int index){
    final isSelected = widget.currentIndex == index;
    final double iconSize = isSelected? 35 : 30;
    final color = Theme.of(context).colorScheme.onPrimary;
    if (navIcon.isAsset) {
      return AnimatedContainer(
        duration: Duration(milliseconds: 200),
        width: iconSize,
        height: iconSize,
        child: Image.asset(
          navIcon.assetPath!,
          width: iconSize,
          height: iconSize,
          color: color,
        ),
      );
    } else {
      return Icon(
        navIcon.icon!,
        color: color,
        size: iconSize,
      );
    }
  }
}
class NavItemIcon {
  final String? assetPath;
  final IconData? icon;

  const NavItemIcon.asset(this.assetPath) : icon = null;
  const NavItemIcon.icon(this.icon) : assetPath = null;

  bool get isAsset => assetPath != null;
}