import 'package:flutter/material.dart';

class CustomDropdown extends StatefulWidget {
  final double width;
  final double height;
  final List<String> items;
  final String? initialValue;
  final ValueChanged<String> onItemSelected;

  const CustomDropdown({
    super.key,
    required this.width,
    required this.height,
    required this.items,
    required this.initialValue,
    required this.onItemSelected,
  });

  @override
  CustomDropdownState createState() => CustomDropdownState();
}

class CustomDropdownState extends State<CustomDropdown> {
  late String selectedValue;

  @override
  void initState() {
    super.initState();
    selectedValue = widget.initialValue!;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;
    return GestureDetector(
      onTap: () async {
        if (widget.items.isNotEmpty) {
          final RenderBox renderBox = context.findRenderObject() as RenderBox;
          final Offset offset = renderBox.localToGlobal(Offset.zero);
          final String? selected = await showMenu<String>(
            context: context,
            position: RelativeRect.fromLTRB(
              offset.dx,
              offset.dy + renderBox.size.height,
              offset.dx + renderBox.size.width,
              offset.dy,
            ),
            items: widget.items.map((final item) {
              return PopupMenuItem<String>(
                value: item,
                child: Text(item),
              );
            }).toList(),
          );
          if (selected != null) {
            setState(() {
              selectedValue = selected;
            });
            widget.onItemSelected(selected);
          }
        } else {
          debugPrint('No items available in the dropdown menu.');
        }
      },
      child: Container(
        width: widget.width,
        height: widget.height,
        padding: EdgeInsets.zero,
        decoration: BoxDecoration(
          color: theme.secondary,
          borderRadius: BorderRadius.circular(4),
          border: Border.all(
            color: theme.onPrimary,
            width: 0.3,
            strokeAlign: BorderSide.strokeAlignOutside
          ),
          boxShadow: [
            BoxShadow(
              color: theme.primary,
              offset: Offset(2, 3),
              spreadRadius: 8,
              blurRadius: 8,
              blurStyle: BlurStyle.inner
            )
          ]
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            SizedBox( width: 3),
            Text(
              selectedValue,
              style: TextStyle(
                fontSize: 14,
                color: theme.onPrimary,
                fontWeight: FontWeight.w700,
              ),
            ),
            Icon(
              Icons.arrow_drop_down,
              color: theme.onPrimary,
            ),
          ],
        ),
      ),
    );
  }
}