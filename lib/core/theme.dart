import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Riverpod provider for theme mode
final themeProvider = StateProvider<ThemeMode>((ref) => ThemeMode.system);

// AppTheme class (unchanged)
class AppTheme {
  static ThemeData lightTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: const ColorScheme(
        brightness: Brightness.light,
        primary: Color(0xFFFFFFFF),
        onPrimary: Color(0xFF212121),
        tertiary: Color.fromARGB(255, 231, 231, 231),
        secondary: Color.fromARGB(255, 196, 196, 196),
        onSecondary: Color(0xFF212121),
        surface: Color(0xFFFFFFFF),
        onSurface: Color(0xFF212121),
        error: Color(0xFFB00020),
        onError: Color(0xFFFFFFFF),
      )
    );
  }

  static ThemeData darkTheme() {
    return ThemeData(
      useMaterial3: true,
      colorScheme: const ColorScheme(
        brightness: Brightness.dark,
        primary: Color(0xFF000000),
        onPrimary: Color(0xFFFFFFFF),
        tertiary: Color.fromARGB(255, 37, 37, 37),
        secondary: Color(0xFF454545),
        onSecondary: Color(0xFFFFFFFF),
        surface: Color(0xFF000000),
        onSurface: Color(0xFFE0E0E0),
        error: Color(0xFF000000),
        onError: Color(0xFFCF6679),
      ),
    );
  }
}