// lib/ffi/chart_engine_bindings.dart
import 'dart:ffi';
import 'dart:io';
import 'package:ffi/ffi.dart';

/// Loads the native library.
/// For Android, it should load 'libchart_engine.so'
/// For iOS, you might use DynamicLibrary.process()
DynamicLibrary? _nativeLib;

DynamicLibrary get nativeLib {
  if (_nativeLib != null) return _nativeLib!;

  try {
    _nativeLib =
        Platform.isAndroid
            ? DynamicLibrary.open("native-lib.so")
            : Platform.isIOS
            ? DynamicLibrary.process()
            : DynamicLibrary.open("native-lib.dylib");
    return _nativeLib!;
  } catch (e) {
    throw Exception(
      'Failed to load native library: $e. Please build the native library first using scripts/build_android.sh',
    );
  }
}

// Opaque pointer representing an instance of ChartEngine.
base class ChartEngine extends Opaque {}

/// Binding for: ChartEngine* create_chart_engine()
typedef CreateChartEngineNative = Pointer<ChartEngine> Function();
typedef CreateChartEngine = Pointer<ChartEngine> Function();

final CreateChartEngine createChartEngine =
    nativeLib
        .lookup<NativeFunction<CreateChartEngineNative>>('create_chart_engine')
        .asFunction();

/// Binding for: void destroy_chart_engine(ChartEngine*)
typedef DestroyChartEngineNative = Void Function(Pointer<ChartEngine>);
typedef DestroyChartEngine = void Function(Pointer<ChartEngine>);

final DestroyChartEngine destroyChartEngine =
    nativeLib
        .lookup<NativeFunction<DestroyChartEngineNative>>(
          'destroy_chart_engine',
        )
        .asFunction();

/// Binding for: void chart_engine_feed_tick(ChartEngine*, double, int64_t)
typedef ChartEngineFeedTickNative =
    Void Function(Pointer<ChartEngine>, Double, Int64);
typedef ChartEngineFeedTick = void Function(Pointer<ChartEngine>, double, int);

final ChartEngineFeedTick chartEngineFeedTick =
    nativeLib
        .lookup<NativeFunction<ChartEngineFeedTickNative>>(
          'chart_engine_feed_tick',
        )
        .asFunction();

/// Binding for: int chart_engine_get_candlestick_count(ChartEngine*)
typedef ChartEngineGetCandlestickCountNative =
    Int32 Function(Pointer<ChartEngine>);
typedef ChartEngineGetCandlestickCount = int Function(Pointer<ChartEngine>);

final ChartEngineGetCandlestickCount chartEngineGetCandlestickCount =
    nativeLib
        .lookup<NativeFunction<ChartEngineGetCandlestickCountNative>>(
          'chart_engine_get_candlestick_count',
        )
        .asFunction();

/// Define the Candlestick struct to mirror the C++ struct.
base class Candlestick extends Struct {
  @Double()
  external double open;
  @Double()
  external double high;
  @Double()
  external double low;
  @Double()
  external double close;
  @Int64()
  external int startTime;
  @Int64()
  external int endTime;
}

/// Binding for:
/// void chart_engine_get_candlestick(ChartEngine*, int, Candlestick*)
typedef ChartEngineGetCandlestickNative =
    Void Function(Pointer<ChartEngine>, Int32, Pointer<Candlestick>);
typedef ChartEngineGetCandlestick =
    void Function(Pointer<ChartEngine>, int, Pointer<Candlestick>);

final ChartEngineGetCandlestick chartEngineGetCandlestick =
    nativeLib
        .lookup<NativeFunction<ChartEngineGetCandlestickNative>>(
          'chart_engine_get_candlestick',
        )
        .asFunction();

// Add these bindings after your existing bindings

/// Binding for: void chart_engine_set_high_price(ChartEngine*, double)
typedef ChartEngineSetHighPriceNative =
    Void Function(Pointer<ChartEngine>, Double);
typedef ChartEngineSetHighPrice = void Function(Pointer<ChartEngine>, double);

final ChartEngineSetHighPrice chartEngineSetHighPrice =
    nativeLib
        .lookup<NativeFunction<ChartEngineSetHighPriceNative>>(
          'chart_engine_set_high_price',
        )
        .asFunction();

/// Binding for: void chart_engine_set_start_time(ChartEngine*, int64_t)
typedef ChartEngineSetStartTimeNative =
    Void Function(Pointer<ChartEngine>, Int64);
typedef ChartEngineSetStartTime = void Function(Pointer<ChartEngine>, int);

final ChartEngineSetStartTime chartEngineSetStartTime =
    nativeLib
        .lookup<NativeFunction<ChartEngineSetStartTimeNative>>(
          'chart_engine_set_start_time',
        )
        .asFunction();

/// Binding for: void chart_engine_set_zoom_scales(ChartEngine*, double, double)
typedef ChartEngineSetZoomScalesNative =
    Void Function(Pointer<ChartEngine>, Double, Double);
typedef ChartEngineSetZoomScales =
    void Function(Pointer<ChartEngine>, double, double);

final ChartEngineSetZoomScales chartEngineSetZoomScales =
    nativeLib
        .lookup<NativeFunction<ChartEngineSetZoomScalesNative>>(
          'chart_engine_set_zoom_scales',
        )
        .asFunction();

/// Binding for: void chart_engine_set_pan_offsets(ChartEngine*, double, double)
typedef ChartEngineSetPanOffsetsNative =
    Void Function(Pointer<ChartEngine>, Double, Double);
typedef ChartEngineSetPanOffsets =
    void Function(Pointer<ChartEngine>, double, double);

final ChartEngineSetPanOffsets chartEngineSetPanOffsets =
    nativeLib
        .lookup<NativeFunction<ChartEngineSetPanOffsetsNative>>(
          'chart_engine_set_pan_offsets',
        )
        .asFunction();

/// Binding for: void chart_engine_set_time_interval(ChartEngine*, int)
typedef ChartEngineSetTimeIntervalNative =
    Void Function(Pointer<ChartEngine>, Int32);
typedef ChartEngineSetTimeInterval = void Function(Pointer<ChartEngine>, int);

final ChartEngineSetTimeInterval chartEngineSetTimeInterval =
    nativeLib
        .lookup<NativeFunction<ChartEngineSetTimeIntervalNative>>(
          'chart_engine_set_time_interval',
        )
        .asFunction();

// ============================================================================
// INDICATOR FFI BINDINGS
// ============================================================================

/// Binding for: size_t chart_engine_add_sma(ChartEngine*, int)
typedef ChartEngineAddSMANative = Uint64 Function(Pointer<ChartEngine>, Int32);
typedef ChartEngineAddSMA = int Function(Pointer<ChartEngine>, int);

final ChartEngineAddSMA chartEngineAddSMA =
    nativeLib
        .lookup<NativeFunction<ChartEngineAddSMANative>>('chart_engine_add_sma')
        .asFunction();

/// Binding for: size_t chart_engine_add_ema(ChartEngine*, int)
typedef ChartEngineAddEMANative = Uint64 Function(Pointer<ChartEngine>, Int32);
typedef ChartEngineAddEMA = int Function(Pointer<ChartEngine>, int);

final ChartEngineAddEMA chartEngineAddEMA =
    nativeLib
        .lookup<NativeFunction<ChartEngineAddEMANative>>('chart_engine_add_ema')
        .asFunction();

/// Binding for: size_t chart_engine_add_rsi(ChartEngine*, int)
typedef ChartEngineAddRSINative = Uint64 Function(Pointer<ChartEngine>, Int32);
typedef ChartEngineAddRSI = int Function(Pointer<ChartEngine>, int);

final ChartEngineAddRSI chartEngineAddRSI =
    nativeLib
        .lookup<NativeFunction<ChartEngineAddRSINative>>('chart_engine_add_rsi')
        .asFunction();

/// Binding for: size_t chart_engine_add_macd(ChartEngine*, int, int, int)
typedef ChartEngineAddMACDNative =
    Uint64 Function(Pointer<ChartEngine>, Int32, Int32, Int32);
typedef ChartEngineAddMACD = int Function(Pointer<ChartEngine>, int, int, int);

final ChartEngineAddMACD chartEngineAddMACD =
    nativeLib
        .lookup<NativeFunction<ChartEngineAddMACDNative>>(
          'chart_engine_add_macd',
        )
        .asFunction();

/// Binding for: size_t chart_engine_add_bollinger_bands(ChartEngine*, int, double)
typedef ChartEngineAddBollingerBandsNative =
    Uint64 Function(Pointer<ChartEngine>, Int32, Double);
typedef ChartEngineAddBollingerBands =
    int Function(Pointer<ChartEngine>, int, double);

final ChartEngineAddBollingerBands chartEngineAddBollingerBands =
    nativeLib
        .lookup<NativeFunction<ChartEngineAddBollingerBandsNative>>(
          'chart_engine_add_bollinger_bands',
        )
        .asFunction();

/// Binding for: size_t chart_engine_add_atr(ChartEngine*, int, bool)
typedef ChartEngineAddATRNative =
    Uint64 Function(Pointer<ChartEngine>, Int32, Bool);
typedef ChartEngineAddATR = int Function(Pointer<ChartEngine>, int, bool);

final ChartEngineAddATR chartEngineAddATR =
    nativeLib
        .lookup<NativeFunction<ChartEngineAddATRNative>>('chart_engine_add_atr')
        .asFunction();

/// Binding for: bool chart_engine_remove_indicator(ChartEngine*, size_t)
typedef ChartEngineRemoveIndicatorNative =
    Bool Function(Pointer<ChartEngine>, Uint64);
typedef ChartEngineRemoveIndicator = bool Function(Pointer<ChartEngine>, int);

final ChartEngineRemoveIndicator chartEngineRemoveIndicator =
    nativeLib
        .lookup<NativeFunction<ChartEngineRemoveIndicatorNative>>(
          'chart_engine_remove_indicator',
        )
        .asFunction();

/// Binding for: void chart_engine_clear_all_indicators(ChartEngine*)
typedef ChartEngineClearAllIndicatorsNative =
    Void Function(Pointer<ChartEngine>);
typedef ChartEngineClearAllIndicators = void Function(Pointer<ChartEngine>);

final ChartEngineClearAllIndicators chartEngineClearAllIndicators =
    nativeLib
        .lookup<NativeFunction<ChartEngineClearAllIndicatorsNative>>(
          'chart_engine_clear_all_indicators',
        )
        .asFunction();

/// Binding for: int chart_engine_get_indicator_count(ChartEngine*)
typedef ChartEngineGetIndicatorCountNative =
    Int32 Function(Pointer<ChartEngine>);
typedef ChartEngineGetIndicatorCount = int Function(Pointer<ChartEngine>);

final ChartEngineGetIndicatorCount chartEngineGetIndicatorCount =
    nativeLib
        .lookup<NativeFunction<ChartEngineGetIndicatorCountNative>>(
          'chart_engine_get_indicator_count',
        )
        .asFunction();

/// Binding for: const char* chart_engine_get_indicator_name(ChartEngine*, int)
typedef ChartEngineGetIndicatorNameNative =
    Pointer<Utf8> Function(Pointer<ChartEngine>, Int32);
typedef ChartEngineGetIndicatorName =
    Pointer<Utf8> Function(Pointer<ChartEngine>, int);

final ChartEngineGetIndicatorName chartEngineGetIndicatorName =
    nativeLib
        .lookup<NativeFunction<ChartEngineGetIndicatorNameNative>>(
          'chart_engine_get_indicator_name',
        )
        .asFunction();

/// Binding for: int chart_engine_get_indicator_data_count(ChartEngine*, int)
typedef ChartEngineGetIndicatorDataCountNative =
    Int32 Function(Pointer<ChartEngine>, Int32);
typedef ChartEngineGetIndicatorDataCount =
    int Function(Pointer<ChartEngine>, int);

final ChartEngineGetIndicatorDataCount chartEngineGetIndicatorDataCount =
    nativeLib
        .lookup<NativeFunction<ChartEngineGetIndicatorDataCountNative>>(
          'chart_engine_get_indicator_data_count',
        )
        .asFunction();

/// Binding for: bool chart_engine_get_indicator_data_point(ChartEngine*, int, int, double*, int64_t*, bool*)
typedef ChartEngineGetIndicatorDataPointNative =
    Bool Function(
      Pointer<ChartEngine>,
      Int32,
      Int32,
      Pointer<Double>,
      Pointer<Int64>,
      Pointer<Bool>,
    );
typedef ChartEngineGetIndicatorDataPoint =
    bool Function(
      Pointer<ChartEngine>,
      int,
      int,
      Pointer<Double>,
      Pointer<Int64>,
      Pointer<Bool>,
    );

final ChartEngineGetIndicatorDataPoint chartEngineGetIndicatorDataPoint =
    nativeLib
        .lookup<NativeFunction<ChartEngineGetIndicatorDataPointNative>>(
          'chart_engine_get_indicator_data_point',
        )
        .asFunction();
