// lib/ffi/chart_engine_wrapper.dart
import 'dart:ffi';
import 'package:ffi/ffi.dart';
import 'chart_engine_bindings.dart';

class ChartEngineWrapper {
  Pointer<ChartEngine>? _engine;
  bool _isInitialized = false;
  String? _initError;

  ChartEngineWrapper() {
    try {
      _engine = createChartEngine();
      _isInitialized = true;
    } catch (e) {
      _initError = e.toString();
      _isInitialized = false;
    }
  }

  bool get isInitialized => _isInitialized;
  String? get initError => _initError;

  void feedTick(double price, int timestamp) {
    if (!_isInitialized || _engine == null) return;
    chartEngineFeedTick(_engine!, price, timestamp);
  }

  int getCandlestickCount() {
    if (!_isInitialized || _engine == null) return 0;
    return chartEngineGetCandlestickCount(_engine!);
  }

  Candlestick getCandlestick(int index) {
    if (!_isInitialized || _engine == null) {
      // Return a default/empty candlestick
      final pointer = calloc<Candlestick>();
      final candle = pointer.ref;
      calloc.free(pointer);
      return candle;
    }
    final pointer = calloc<Candlestick>();
    chartEngineGetCandlestick(_engine!, index, pointer);
    final candle = pointer.ref;
    calloc.free(pointer);
    return candle;
  }

  void dispose() {
    if (_engine != null && _engine != nullptr) {
      destroyChartEngine(_engine!);
      _engine = null;
    }
  }

  // Add these methods to your existing ChartEngineWrapper class

  // Set the high price for the price axis
  void setHighPrice(double high) {
    if (!_isInitialized || _engine == null) return;
    chartEngineSetHighPrice(_engine!, high);
  }

  // Set the start time for the time axis
  void setStartTime(int timestampMs) {
    if (!_isInitialized || _engine == null) return;
    chartEngineSetStartTime(_engine!, timestampMs);
  }

  // Set zoom scales
  void setZoomScales(double horizontalZoom, double verticalZoom) {
    if (!_isInitialized || _engine == null) return;
    chartEngineSetZoomScales(_engine!, horizontalZoom, verticalZoom);
  }

  // Set pan offsets
  void setPanOffsets(double horizontalOffset, double verticalOffset) {
    if (!_isInitialized || _engine == null) return;
    chartEngineSetPanOffsets(_engine!, horizontalOffset, verticalOffset);
  }

  // Set time interval
  void setTimeInterval(int seconds) {
    if (!_isInitialized || _engine == null) return;
    chartEngineSetTimeInterval(_engine!, seconds);
  }

  // ============================================================================
  // INDICATOR METHODS
  // ============================================================================

  /// Add Simple Moving Average indicator
  int addSMA(int period) {
    if (!_isInitialized || _engine == null) return 0;
    return chartEngineAddSMA(_engine!, period);
  }

  /// Add Exponential Moving Average indicator
  int addEMA(int period) {
    if (!_isInitialized || _engine == null) return 0;
    return chartEngineAddEMA(_engine!, period);
  }

  /// Add Relative Strength Index indicator
  int addRSI(int period) {
    if (!_isInitialized || _engine == null) return 0;
    return chartEngineAddRSI(_engine!, period);
  }

  /// Add MACD indicator
  int addMACD(int fastPeriod, int slowPeriod, int signalPeriod) {
    if (!_isInitialized || _engine == null) return 0;
    return chartEngineAddMACD(_engine!, fastPeriod, slowPeriod, signalPeriod);
  }

  /// Add Bollinger Bands indicator
  int addBollingerBands(int period, double stdDevMultiplier) {
    if (!_isInitialized || _engine == null) return 0;
    return chartEngineAddBollingerBands(_engine!, period, stdDevMultiplier);
  }

  /// Add Average True Range indicator
  int addATR(int period, bool useWildersSmoothing) {
    if (!_isInitialized || _engine == null) return 0;
    return chartEngineAddATR(_engine!, period, useWildersSmoothing);
  }

  /// Remove indicator by ID
  bool removeIndicator(int indicatorId) {
    if (!_isInitialized || _engine == null) return false;
    return chartEngineRemoveIndicator(_engine!, indicatorId);
  }

  /// Clear all indicators
  void clearAllIndicators() {
    if (!_isInitialized || _engine == null) return;
    chartEngineClearAllIndicators(_engine!);
  }

  /// Get number of indicators
  int getIndicatorCount() {
    if (!_isInitialized || _engine == null) return 0;
    return chartEngineGetIndicatorCount(_engine!);
  }

  /// Get indicator name by index
  String? getIndicatorName(int index) {
    if (!_isInitialized || _engine == null) return null;
    final namePtr = chartEngineGetIndicatorName(_engine!, index);
    if (namePtr == nullptr) return null;
    return namePtr.toDartString();
  }

  /// Get indicator data count by indicator index
  int getIndicatorDataCount(int indicatorIndex) {
    if (!_isInitialized || _engine == null) return 0;
    return chartEngineGetIndicatorDataCount(_engine!, indicatorIndex);
  }

  /// Get indicator data point
  IndicatorDataPoint? getIndicatorDataPoint(int indicatorIndex, int dataIndex) {
    if (!_isInitialized || _engine == null) return null;

    final valuePtr = calloc<Double>();
    final timestampPtr = calloc<Int64>();
    final isValidPtr = calloc<Bool>();

    try {
      final success = chartEngineGetIndicatorDataPoint(
        _engine!,
        indicatorIndex,
        dataIndex,
        valuePtr,
        timestampPtr,
        isValidPtr,
      );

      if (!success) return null;

      return IndicatorDataPoint(
        value: valuePtr.value,
        timestamp: timestampPtr.value,
        isValid: isValidPtr.value,
      );
    } finally {
      calloc.free(valuePtr);
      calloc.free(timestampPtr);
      calloc.free(isValidPtr);
    }
  }
}

/// Data class for indicator data points
class IndicatorDataPoint {
  final double value;
  final int timestamp;
  final bool isValid;

  const IndicatorDataPoint({
    required this.value,
    required this.timestamp,
    required this.isValid,
  });

  @override
  String toString() {
    return 'IndicatorDataPoint(value: $value, timestamp: $timestamp, isValid: $isValid)';
  }
}
