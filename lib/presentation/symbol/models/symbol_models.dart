class SymbolMeta {
  final String symbol;
  final String? description;
  DateTime? timestamp;
  double? bid;
  double? ask;
  double? price;
  double? volume;
  double? change;
  final String? broker;

  /// A const constructor for canonical instances
  SymbolMeta({
    required this.symbol,
    this.description,
    this.timestamp,
    this.bid,
    this.ask,
    this.price,
    this.volume,
    this.change,
    this.broker,
  });

  /// Named constructor for JSON → model
  factory SymbolMeta.fromJson(String symbol, Map<String, dynamic> json) {
    return SymbolMeta(
      symbol: symbol,
      description: json['description'] as String?,
      bid: double.tryParse(json['bp']?.toString() ?? '') ?? 0,
      ask: double.tryParse(json['ap']?.toString() ?? '') ?? 0,
      price: double.tryParse(json['p']?.toString() ?? '') ?? 0,
      volume:
          json.containsKey('v')
              ? double.tryParse(json['v']?.toString() ?? '')
              : null,
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['t'] ?? 0),
      broker: json['broker'] as String?,
    );
  }

  /// Create from server response data
  factory SymbolMeta.fromServerResponse(Map<String, dynamic> json) {
    return SymbolMeta(
      symbol: json['symbol'] as String,
      description: json['description'] as String?,
      price: (json['currentPrice'] as num?)?.toDouble(),
      change: (json['change'] as num?)?.toDouble(),
      volume: (json['volume'] as num?)?.toDouble(),
      timestamp:
          json['timestamp'] != null
              ? DateTime.parse(json['timestamp'] as String)
              : DateTime.now(),
      broker: json['broker'] as String?,
    );
  }

  /// Model → JSON
  Map<String, dynamic> toJson({required double price}) {
    return {
      'symbol': symbol,
      'description': description,
      'bid': bid,
      'ask': ask,
      'price': price,
      'volume': volume,
      'change': change,
      'broker': broker,
      'timestamp': timestamp?.millisecondsSinceEpoch ?? 0,
    };
  }

  /// Create a copy with updated values
  SymbolMeta copyWith({
    String? symbol,
    String? description,
    DateTime? timestamp,
    double? bid,
    double? ask,
    double? price,
    double? volume,
    double? change,
    String? broker,
  }) {
    return SymbolMeta(
      symbol: symbol ?? this.symbol,
      description: description ?? this.description,
      timestamp: timestamp ?? this.timestamp,
      bid: bid ?? this.bid,
      ask: ask ?? this.ask,
      price: price ?? this.price,
      volume: volume ?? this.volume,
      change: change ?? this.change,
      broker: broker ?? this.broker,
    );
  }

  /// Makes debugPrint(this) more meaningful
  @override
  String toString() {
    return 'SymbolMeta(symbol: "$symbol", description: "$description", bid: $bid, ask: $ask, price: $price, '
        'volume: $volume, change: $change, broker: $broker, timestamp: $timestamp)';
  }
}

/// Broker information from server
class BrokerInfo {
  final String id;
  final String name;
  final String? description;
  final bool isActive;

  BrokerInfo({
    required this.id,
    required this.name,
    this.description,
    required this.isActive,
  });

  factory BrokerInfo.fromJson(Map<String, dynamic> json) {
    return BrokerInfo(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      isActive: json['isActive'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'isActive': isActive,
    };
  }

  @override
  String toString() {
    return 'BrokerInfo(id: $id, name: $name, description: $description, isActive: $isActive)';
  }
}

/// Symbol price data from server
class SymbolPriceData {
  final String symbol;
  final String? description;
  final double? currentPrice;
  final double? change;
  final double? changePercent;
  final double? volume;
  final double? high;
  final double? low;
  final double? open;
  final DateTime timestamp;

  SymbolPriceData({
    required this.symbol,
    this.description,
    this.currentPrice,
    this.change,
    this.changePercent,
    this.volume,
    this.high,
    this.low,
    this.open,
    required this.timestamp,
  });

  factory SymbolPriceData.fromJson(Map<String, dynamic> json) {
    return SymbolPriceData(
      symbol: json['symbol'] as String,
      description: json['description'] as String?,
      currentPrice: (json['currentPrice'] as num?)?.toDouble(),
      change: (json['change'] as num?)?.toDouble(),
      changePercent: (json['changePercent'] as num?)?.toDouble(),
      volume: (json['volume'] as num?)?.toDouble(),
      high: (json['high'] as num?)?.toDouble(),
      low: (json['low'] as num?)?.toDouble(),
      open: (json['open'] as num?)?.toDouble(),
      timestamp:
          json['timestamp'] != null
              ? DateTime.parse(json['timestamp'] as String)
              : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'symbol': symbol,
      'description': description,
      'currentPrice': currentPrice,
      'change': change,
      'changePercent': changePercent,
      'volume': volume,
      'high': high,
      'low': low,
      'open': open,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'SymbolPriceData(symbol: $symbol, currentPrice: $currentPrice, change: $change, timestamp: $timestamp)';
  }
}

/// Historical price data from server
class HistoricalPriceData {
  final String symbol;
  final DateTime timestamp;
  final double open;
  final double high;
  final double low;
  final double close;
  final double volume;

  HistoricalPriceData({
    required this.symbol,
    required this.timestamp,
    required this.open,
    required this.high,
    required this.low,
    required this.close,
    required this.volume,
  });

  factory HistoricalPriceData.fromJson(Map<String, dynamic> json) {
    return HistoricalPriceData(
      symbol: json['symbol'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      open: (json['open'] as num).toDouble(),
      high: (json['high'] as num).toDouble(),
      low: (json['low'] as num).toDouble(),
      close: (json['close'] as num).toDouble(),
      volume: (json['volume'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'symbol': symbol,
      'timestamp': timestamp.toIso8601String(),
      'open': open,
      'high': high,
      'low': low,
      'close': close,
      'volume': volume,
    };
  }

  @override
  String toString() {
    return 'HistoricalPriceData(symbol: $symbol, timestamp: $timestamp, close: $close)';
  }
}

/// Bulk price response from server
class BulkPriceResponse {
  final List<SymbolPriceData> prices;
  final int successCount;
  final int failureCount;
  final DateTime timestamp;

  BulkPriceResponse({
    required this.prices,
    required this.successCount,
    required this.failureCount,
    required this.timestamp,
  });

  factory BulkPriceResponse.fromJson(Map<String, dynamic> json) {
    final List<dynamic> pricesJson = json['prices'] as List<dynamic>;
    final prices =
        pricesJson
            .map((p) => SymbolPriceData.fromJson(p as Map<String, dynamic>))
            .toList();

    return BulkPriceResponse(
      prices: prices,
      successCount: json['successCount'] as int? ?? prices.length,
      failureCount: json['failureCount'] as int? ?? 0,
      timestamp:
          json['timestamp'] != null
              ? DateTime.parse(json['timestamp'] as String)
              : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'prices': prices.map((p) => p.toJson()).toList(),
      'successCount': successCount,
      'failureCount': failureCount,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'BulkPriceResponse(prices: ${prices.length}, successCount: $successCount, failureCount: $failureCount)';
  }
}

/// Price analytics data from server
class PriceAnalyticsData {
  final String symbol;
  final double? volatility;
  final double? beta;
  final double? rsi;
  final double? movingAverage20;
  final double? movingAverage50;
  final double? movingAverage200;
  final DateTime timestamp;

  PriceAnalyticsData({
    required this.symbol,
    this.volatility,
    this.beta,
    this.rsi,
    this.movingAverage20,
    this.movingAverage50,
    this.movingAverage200,
    required this.timestamp,
  });

  factory PriceAnalyticsData.fromJson(Map<String, dynamic> json) {
    return PriceAnalyticsData(
      symbol: json['symbol'] as String,
      volatility: (json['volatility'] as num?)?.toDouble(),
      beta: (json['beta'] as num?)?.toDouble(),
      rsi: (json['rsi'] as num?)?.toDouble(),
      movingAverage20: (json['movingAverage20'] as num?)?.toDouble(),
      movingAverage50: (json['movingAverage50'] as num?)?.toDouble(),
      movingAverage200: (json['movingAverage200'] as num?)?.toDouble(),
      timestamp:
          json['timestamp'] != null
              ? DateTime.parse(json['timestamp'] as String)
              : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'symbol': symbol,
      'volatility': volatility,
      'beta': beta,
      'rsi': rsi,
      'movingAverage20': movingAverage20,
      'movingAverage50': movingAverage50,
      'movingAverage200': movingAverage200,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'PriceAnalyticsData(symbol: $symbol, volatility: $volatility, rsi: $rsi)';
  }
}

/// Price analytics response from server
class PriceAnalyticsResponse {
  final List<PriceAnalyticsData> analytics;
  final DateTime timestamp;

  PriceAnalyticsResponse({required this.analytics, required this.timestamp});

  factory PriceAnalyticsResponse.fromJson(Map<String, dynamic> json) {
    final List<dynamic> analyticsJson = json['analytics'] as List<dynamic>;
    final analytics =
        analyticsJson
            .map((a) => PriceAnalyticsData.fromJson(a as Map<String, dynamic>))
            .toList();

    return PriceAnalyticsResponse(
      analytics: analytics,
      timestamp:
          json['timestamp'] != null
              ? DateTime.parse(json['timestamp'] as String)
              : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'analytics': analytics.map((a) => a.toJson()).toList(),
      'timestamp': timestamp.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'PriceAnalyticsResponse(analytics: ${analytics.length}, timestamp: $timestamp)';
  }
}

/// Internal model for symbol search results
class SymbolSearchResult {
  final String symbol;
  final String name;
  final String? exchange;
  final String? type;
  final String? currency;

  SymbolSearchResult({
    required this.symbol,
    required this.name,
    this.exchange,
    this.type,
    this.currency,
  });

  factory SymbolSearchResult.fromJson(Map<String, dynamic> json) {
    return SymbolSearchResult(
      symbol: json['symbol'] as String? ?? '',
      name: json['name'] as String? ?? json['description'] as String? ?? '',
      exchange: json['exchange'] as String?,
      type: json['type'] as String?,
      currency: json['currency'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'symbol': symbol,
      'name': name,
      'exchange': exchange,
      'type': type,
      'currency': currency,
    };
  }

  @override
  String toString() {
    return 'SymbolSearchResult(symbol: $symbol, name: $name, exchange: $exchange, type: $type, currency: $currency)';
  }
}
