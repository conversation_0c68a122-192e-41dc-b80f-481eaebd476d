import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'models/symbol_models.dart';
import 'providers/symbol_provider.dart';

class SymbolListScreen extends ConsumerWidget {
  final String watchlistName;
  final Function(String) onSymbolAdded;

  const SymbolListScreen({
    super.key,
    required this.watchlistName,
    required this.onSymbolAdded,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return _SymbolListContent(
      watchlistName: watchlistName,
      onSymbolAdded: onSymbolAdded,
    );
  }
}

class _SymbolListContent extends ConsumerStatefulWidget {
  final String watchlistName;
  final Function(String) onSymbolAdded;

  const _SymbolListContent({
    required this.watchlistName,
    required this.onSymbolAdded,
  });

  @override
  ConsumerState<_SymbolListContent> createState() => _SymbolListContentState();
}

class _SymbolListContentState extends ConsumerState<_SymbolListContent> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 70,
        title: _SearchBar(
          controller: _searchController,
          focusNode: _searchFocusNode,
          onChanged: (query) {
            setState(() {
              _searchQuery = query;
            });
          },
        ),
        shape: Border(bottom: BorderSide(color: theme.secondary)),
      ),
      body: Column(children: [Expanded(child: _buildSymbolContent())]),
    );
  }

  Widget _buildSymbolContent() {
    // If there's a search query, use search provider
    if (_searchQuery.isNotEmpty) {
      final searchResults = ref.watch(symbolSearchProvider(_searchQuery));
      return searchResults.when(
        data:
            (symbols) => _SymbolListView(
              symbols: symbols,
              onSymbolAdded: widget.onSymbolAdded,
            ),
        loading: () => const _LoadingIndicator(),
        error: (error, stack) => _ErrorState(message: error.toString()),
      );
    }

    // Otherwise, show tabbed content with different symbol categories
    return DefaultTabController(
      length: 4,
      child: Column(
        children: [
          TabBar(
            tabs: const [
              Tab(text: 'Popular'),
              Tab(text: 'Stocks'),
              Tab(text: 'Forex'),
              Tab(text: 'Crypto'),
            ],
            labelColor: Theme.of(context).colorScheme.primary,
            unselectedLabelColor: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.6),
            indicatorColor: Theme.of(context).colorScheme.primary,
          ),
          Expanded(
            child: TabBarView(
              children: [
                _buildPopularSymbols(),
                _buildMarketTypeSymbols('stocks'),
                _buildMarketTypeSymbols('forex'),
                _buildMarketTypeSymbols('crypto'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPopularSymbols() {
    final popularSymbols = ref.watch(popularSymbolsProvider);
    return popularSymbols.when(
      data:
          (symbols) => RefreshIndicator(
            onRefresh: () async {
              ref.invalidate(popularSymbolsProvider);
            },
            child: _SymbolListView(
              symbols: symbols,
              onSymbolAdded: widget.onSymbolAdded,
            ),
          ),
      loading: () => const _LoadingIndicator(),
      error:
          (error, stack) => _ErrorState(
            message: error.toString(),
            onRetry: () => ref.invalidate(popularSymbolsProvider),
          ),
    );
  }

  Widget _buildMarketTypeSymbols(String marketType) {
    final symbols = ref.watch(activeSymbolsByMarketProvider(marketType));
    return symbols.when(
      data:
          (symbols) => RefreshIndicator(
            onRefresh: () async {
              ref.invalidate(activeSymbolsByMarketProvider(marketType));
            },
            child: _SymbolListView(
              symbols: symbols,
              onSymbolAdded: widget.onSymbolAdded,
            ),
          ),
      loading: () => const _LoadingIndicator(),
      error:
          (error, stack) => _ErrorState(
            message: error.toString(),
            onRetry:
                () => ref.invalidate(activeSymbolsByMarketProvider(marketType)),
          ),
    );
  }
}

class _SearchBar extends StatelessWidget {
  final TextEditingController controller;
  final FocusNode focusNode;
  final ValueChanged<String> onChanged;

  const _SearchBar({
    required this.controller,
    required this.focusNode,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: controller,
      focusNode: focusNode,
      decoration: InputDecoration(
        hintText: 'Search symbols...',
        prefixIcon: const Icon(Icons.search),
        border: InputBorder.none,
        contentPadding: const EdgeInsets.symmetric(horizontal: 16),
      ),
      onChanged: onChanged,
    );
  }
}

class _SymbolListView extends StatelessWidget {
  final List<SymbolMeta> symbols;
  final Function(String) onSymbolAdded;

  const _SymbolListView({required this.symbols, required this.onSymbolAdded});

  @override
  Widget build(BuildContext context) {
    if (symbols.isEmpty) {
      return const _EmptyState();
    }

    return ListView.separated(
      itemCount: symbols.length,
      keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
      separatorBuilder: (context, index) => _SymbolDivider(),
      itemBuilder: (context, index) {
        return _SymbolListItem(
          symbol: symbols[index],
          onTap: () => onSymbolAdded(symbols[index].symbol),
        );
      },
    );
  }
}

class _SymbolListItem extends StatelessWidget {
  final SymbolMeta symbol;
  final VoidCallback onTap;

  const _SymbolListItem({required this.symbol, required this.onTap});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;
    final change = symbol.change ?? 0.0;
    final changeColor =
        change > 0
            ? Colors.green
            : change < 0
            ? Colors.red
            : theme.onSurface;

    return ListTile(
      minTileHeight: 60,
      minVerticalPadding: 8,
      title: Row(
        children: [
          Text(
            symbol.symbol,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
          const SizedBox(width: 8),
          if (symbol.broker != null)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: theme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                symbol.broker!,
                style: TextStyle(
                  fontSize: 10,
                  color: theme.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            symbol.description ?? '--',
            style: TextStyle(
              color: theme.onSurface.withValues(alpha: 0.7),
              fontSize: 13,
            ),
          ),
          if (symbol.volume != null)
            Text(
              'Vol: ${_formatVolume(symbol.volume!)}',
              style: TextStyle(
                color: theme.onSurface.withValues(alpha: 0.5),
                fontSize: 11,
              ),
            ),
        ],
      ),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            symbol.price?.toStringAsFixed(2) ?? '---',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: theme.onSurface,
            ),
          ),
          if (change != 0.0)
            Text(
              '${change > 0 ? '+' : ''}${change.toStringAsFixed(2)}',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 12,
                color: changeColor,
              ),
            ),
        ],
      ),
      onTap: onTap,
    );
  }

  String _formatVolume(double volume) {
    if (volume >= 1000000) {
      return '${(volume / 1000000).toStringAsFixed(1)}M';
    } else if (volume >= 1000) {
      return '${(volume / 1000).toStringAsFixed(1)}K';
    }
    return volume.toStringAsFixed(0);
  }
}

class _SymbolDivider extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;
    return Divider(height: 0.5, thickness: 0.2, color: theme.onPrimary);
  }
}

class _LoadingIndicator extends StatelessWidget {
  const _LoadingIndicator();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;
    return Center(child: CircularProgressIndicator(color: theme.onPrimary));
  }
}

class _EmptyState extends StatelessWidget {
  const _EmptyState();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: theme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'No symbols found',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: theme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try searching for a different symbol or check your connection.',
            style: TextStyle(color: theme.onSurface.withValues(alpha: 0.7)),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class _ErrorState extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;

  const _ErrorState({required this.message, this.onRetry});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 48, color: theme.error),
          const SizedBox(height: 16),
          Text(
            'Something went wrong',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: theme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: TextStyle(color: theme.onSurface.withValues(alpha: 0.7)),
            textAlign: TextAlign.center,
          ),
          if (onRetry != null) ...[
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          ],
        ],
      ),
    );
  }
}
