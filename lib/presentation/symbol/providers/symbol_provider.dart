import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../symbol_service.dart';
import '../models/symbol_models.dart';

// Enhanced symbol service provider
final symbolServiceProvider = Provider<SymbolService>((ref) {
  final service = SymbolService();
  ref.onDispose(() => service.dispose());
  return service;
});

/// Provider that manages the currently selected trading symbol
final selectedSymbolProvider =
    StateNotifierProvider<SelectedSymbolNotifier, String>((ref) {
      return SelectedSymbolNotifier();
    });

class SelectedSymbolNotifier extends StateNotifier<String> {
  SelectedSymbolNotifier() : super('AAPL');

  void updateSymbol(String newSymbol) {
    state = newSymbol;
  }
}

/// Enhanced provider for available symbols with server-side loading
final availableSymbolsProvider = StateNotifierProvider<
  AvailableSymbolsNotifier,
  AsyncValue<List<SymbolMeta>>
>((ref) {
  return AvailableSymbolsNotifier(ref);
});

class AvailableSymbolsNotifier
    extends StateNotifier<AsyncValue<List<SymbolMeta>>> {
  final Ref ref;
  late final SymbolService _symbolService;

  AvailableSymbolsNotifier(this.ref) : super(const AsyncLoading()) {
    _symbolService = ref.read(symbolServiceProvider);
    loadSymbols();
  }

  /// Load symbols from the server
  Future<void> loadSymbols({bool forceRefresh = false}) async {
    if (!forceRefresh && state.hasValue) {
      return; // Don't reload if we already have data
    }

    state = const AsyncLoading();

    try {
      final symbols = await _symbolService.getAllSymbols();
      state = AsyncData(symbols);
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
    }
  }

  /// Refresh symbols from server
  Future<void> refreshSymbols() async {
    await loadSymbols(forceRefresh: true);
  }
}

/// Provider for popular/active symbols
final popularSymbolsProvider =
    StateNotifierProvider<PopularSymbolsNotifier, AsyncValue<List<SymbolMeta>>>(
      (ref) {
        return PopularSymbolsNotifier(ref);
      },
    );

class PopularSymbolsNotifier
    extends StateNotifier<AsyncValue<List<SymbolMeta>>> {
  final Ref ref;
  late final SymbolService _symbolService;

  PopularSymbolsNotifier(this.ref) : super(const AsyncLoading()) {
    _symbolService = ref.read(symbolServiceProvider);
    loadPopularSymbols();
  }

  /// Load popular symbols from the server
  Future<void> loadPopularSymbols({bool forceRefresh = false}) async {
    if (!forceRefresh && state.hasValue) {
      return; // Don't reload if we already have data
    }

    state = const AsyncLoading();

    try {
      final symbols = await _symbolService.getPopularSymbols();
      state = AsyncData(symbols);
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
    }
  }

  /// Refresh popular symbols from server
  Future<void> refreshPopularSymbols() async {
    await loadPopularSymbols(forceRefresh: true);
  }
}

/// Provider for symbol search functionality
final symbolSearchProvider = StateNotifierProvider.family<
  SymbolSearchNotifier,
  AsyncValue<List<SymbolMeta>>,
  String
>((ref, query) {
  return SymbolSearchNotifier(ref, query);
});

class SymbolSearchNotifier extends StateNotifier<AsyncValue<List<SymbolMeta>>> {
  final Ref ref;
  final String query;
  late final SymbolService _symbolService;
  Timer? _debounceTimer;

  SymbolSearchNotifier(this.ref, this.query) : super(const AsyncLoading()) {
    _symbolService = ref.read(symbolServiceProvider);
    _performSearch();
  }

  /// Perform symbol search with debouncing
  void _performSearch() {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () async {
      if (query.isEmpty) {
        state = const AsyncData([]);
        return;
      }

      try {
        final results = await _symbolService.searchSymbols(query);
        state = AsyncData(results);
      } catch (e) {
        state = AsyncError(e, StackTrace.current);
      }
    });
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }
}

/// Provider for active symbols by market type
final activeSymbolsByMarketProvider =
    FutureProvider.family<List<SymbolMeta>, String>((ref, marketType) async {
      final symbolService = ref.read(symbolServiceProvider);
      return await symbolService.getSymbolsByMarketType(marketType);
    });

/// Provider for active symbols with limit
final activeSymbolsProvider = FutureProvider.family<List<SymbolMeta>, int>((
  ref,
  limit,
) async {
  final symbolService = ref.read(symbolServiceProvider);
  return await symbolService.getActiveSymbols(limit: limit);
});

/// Provider for live symbol price
final liveSymbolPriceProvider = StateNotifierProvider.family<
  LiveSymbolPriceNotifier,
  AsyncValue<SymbolPriceData?>,
  String
>((ref, symbol) {
  return LiveSymbolPriceNotifier(ref, symbol);
});

class LiveSymbolPriceNotifier
    extends StateNotifier<AsyncValue<SymbolPriceData?>> {
  final Ref ref;
  final String symbol;
  late final SymbolService _symbolService;
  Timer? _refreshTimer;

  LiveSymbolPriceNotifier(this.ref, this.symbol) : super(const AsyncLoading()) {
    _symbolService = ref.read(symbolServiceProvider);
    _initializePriceStream();
  }

  /// Initialize price fetching
  Future<void> _initializePriceStream() async {
    try {
      // Get initial price
      final initialPrice = await _symbolService.fetchLivePrice(symbol);
      state = AsyncData(initialPrice);

      // Set up periodic refresh (every 30 seconds)
      _refreshTimer = Timer.periodic(const Duration(seconds: 30), (
        timer,
      ) async {
        await refreshPrice();
      });
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
    }
  }

  /// Manually refresh price
  Future<void> refreshPrice() async {
    try {
      final price = await _symbolService.fetchLivePrice(symbol, noCache: true);
      state = AsyncData(price);
    } catch (e) {
      state = AsyncError(e, StackTrace.current);
    }
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }
}

/// Provider for bulk symbol prices
final bulkSymbolPricesProvider =
    FutureProvider.family<BulkPriceResponse, List<String>>((
      ref,
      symbols,
    ) async {
      final symbolService = ref.read(symbolServiceProvider);
      return await symbolService.getBulkPrices(symbols);
    });

/// Provider for historical prices
final historicalPricesProvider =
    FutureProvider.family<List<HistoricalPriceData>, HistoricalPriceParams>((
      ref,
      params,
    ) async {
      final symbolService = ref.read(symbolServiceProvider);
      return await symbolService.getHistoricalPrices(
        params.symbol,
        from: params.from,
        to: params.to,
        interval: params.interval,
      );
    });

/// Parameters for historical price requests
class HistoricalPriceParams {
  final String symbol;
  final DateTime from;
  final DateTime to;
  final String interval;

  HistoricalPriceParams({
    required this.symbol,
    required this.from,
    required this.to,
    this.interval = '1d',
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is HistoricalPriceParams &&
        other.symbol == symbol &&
        other.from == from &&
        other.to == to &&
        other.interval == interval;
  }

  @override
  int get hashCode {
    return symbol.hashCode ^ from.hashCode ^ to.hashCode ^ interval.hashCode;
  }
}

/// Provider for available brokers
final availableBrokersProvider = FutureProvider<List<BrokerInfo>>((ref) async {
  final symbolService = ref.read(symbolServiceProvider);
  return await symbolService.getAvailableBrokers();
});

/// Provider for symbol brokers (broker names)
final symbolBrokersProvider = FutureProvider<List<String>>((ref) async {
  final symbolService = ref.read(symbolServiceProvider);
  return await symbolService.getSymbolBrokers();
});

/// Provider for bulk symbol search
final bulkSymbolSearchProvider =
    FutureProvider.family<List<SymbolMeta>, List<String>>((ref, queries) async {
      final symbolService = ref.read(symbolServiceProvider);
      return await symbolService.bulkSymbolSearch(queries);
    });

/// Provider for bulk active symbols
final bulkActiveSymbolsProvider =
    FutureProvider.family<List<SymbolMeta>, BulkActiveSymbolsParams>((
      ref,
      params,
    ) async {
      final symbolService = ref.read(symbolServiceProvider);
      return await symbolService.bulkGetActiveSymbols(
        params.brokers,
        limit: params.limit,
      );
    });

/// Parameters for bulk active symbols requests
class BulkActiveSymbolsParams {
  final List<String> brokers;
  final int limit;

  BulkActiveSymbolsParams({required this.brokers, this.limit = 5});

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is BulkActiveSymbolsParams &&
        other.brokers.length == brokers.length &&
        other.brokers.every((broker) => brokers.contains(broker)) &&
        other.limit == limit;
  }

  @override
  int get hashCode {
    return brokers.fold(0, (prev, broker) => prev ^ broker.hashCode) ^
        limit.hashCode;
  }
}
