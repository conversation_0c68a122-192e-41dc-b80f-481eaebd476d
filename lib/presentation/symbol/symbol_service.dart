import 'dart:convert';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../../core/constants.dart';
import '../../services/auth_client.dart';
import 'models/symbol_models.dart';

/// Enhanced SymbolService that uses abra-servers endpoints
/// Handles symbol discovery, live prices, and market data operations
class SymbolService {
  static final SymbolService _instance = SymbolService._();
  factory SymbolService() => _instance;
  SymbolService._();

  final AuthClient _authClient = AuthClient();
  final http.Client _httpClient = http.Client();

  /// Get base URL for abra-servers
  String get baseUrl => MarketServiceConfig.baseUrl;

  /// Create headers with authentication
  Map<String, String> _createHeaders() {
    final headers = <String, String>{
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    final token = _authClient.accessToken;
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }

    return headers;
  }

  /// Check if user is authenticated
  bool get isAuthenticated => _authClient.isAuthenticated;

  /// Get available brokers from server
  Future<List<BrokerInfo>> getAvailableBrokers() async {
    try {
      debugPrint('Fetching available brokers from server');

      final response = await _httpClient
          .get(Uri.parse('$baseUrl/api/brokers'), headers: _createHeaders())
          .timeout(MarketServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => BrokerInfo.fromJson(json)).toList();
      } else if (response.statusCode == 401) {
        throw Exception('Unauthorized: Please sign in again');
      } else {
        throw Exception('Server error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching brokers: $e');
      throw Exception('Failed to load brokers: $e');
    }
  }

  /// Get available brokers for symbols (returns broker names)
  Future<List<String>> getSymbolBrokers() async {
    try {
      debugPrint('Fetching available symbol brokers');

      final response = await _httpClient
          .get(
            Uri.parse('$baseUrl/api/symbols/brokers'),
            headers: _createHeaders(),
          )
          .timeout(MarketServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((broker) => broker.toString()).toList();
      } else if (response.statusCode == 401) {
        throw Exception('Unauthorized: Please sign in again');
      } else {
        throw Exception('Server error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching symbol brokers: $e');
      throw Exception('Failed to load symbol brokers: $e');
    }
  }

  /// Get live price data for a single symbol
  Future<SymbolPriceData?> fetchLivePrice(
    String symbol, {
    bool noCache = false,
  }) async {
    if (!isAuthenticated) {
      throw Exception('User not authenticated');
    }

    try {
      debugPrint('Fetching live price for symbol: $symbol (noCache: $noCache)');

      final queryParams = noCache ? {'nocache': 'true'} : <String, String>{};
      final uri = Uri.parse(
        '$baseUrl/api/marketdata/$symbol/fetch-live',
      ).replace(queryParameters: queryParams.isNotEmpty ? queryParams : null);

      final response = await _httpClient
          .get(uri, headers: _createHeaders())
          .timeout(MarketServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return SymbolPriceData.fromJson(data);
      } else if (response.statusCode == 401) {
        throw Exception('Unauthorized: Please sign in again');
      } else if (response.statusCode == 404) {
        debugPrint('Symbol not found: $symbol');
        return null;
      } else {
        throw Exception('Server error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching live price for $symbol: $e');
      throw Exception('Failed to fetch live price: $e');
    }
  }

  /// Get historical price data for a symbol
  Future<List<HistoricalPriceData>> getHistoricalPrices(
    String symbol, {
    required DateTime from,
    required DateTime to,
    String interval = '1d',
  }) async {
    if (!isAuthenticated) {
      throw Exception('User not authenticated');
    }

    try {
      debugPrint('Fetching historical prices for $symbol from $from to $to');

      final queryParams = {
        'from': from.toIso8601String(),
        'to': to.toIso8601String(),
        'interval': interval,
      };

      final uri = Uri.parse(
        '$baseUrl/api/marketdata/$symbol/history',
      ).replace(queryParameters: queryParams);

      final response = await _httpClient
          .get(uri, headers: _createHeaders())
          .timeout(MarketServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => HistoricalPriceData.fromJson(json)).toList();
      } else if (response.statusCode == 401) {
        throw Exception('Unauthorized: Please sign in again');
      } else if (response.statusCode == 404) {
        debugPrint('Historical data not found for symbol: $symbol');
        return [];
      } else {
        throw Exception('Server error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching historical prices for $symbol: $e');
      throw Exception('Failed to fetch historical prices: $e');
    }
  }

  /// Get most active symbols with optional limit
  Future<List<SymbolMeta>> getActiveSymbols({int limit = 5}) async {
    try {
      debugPrint('Fetching $limit most active symbols');

      final queryParams = {'limit': limit.toString()};
      final uri = Uri.parse(
        '$baseUrl/api/symbols/active',
      ).replace(queryParameters: queryParams);

      final response = await _httpClient
          .get(uri, headers: _createHeaders())
          .timeout(MarketServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => SymbolMeta.fromServerResponse(json)).toList();
      } else if (response.statusCode == 401) {
        throw Exception('Unauthorized: Please sign in again');
      } else {
        throw Exception('Server error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching active symbols: $e');
      throw Exception('Failed to fetch active symbols: $e');
    }
  }

  /// Get symbols by market type (stocks, forex, crypto)
  Future<List<SymbolMeta>> getSymbolsByMarketType(String marketType) async {
    try {
      debugPrint('Fetching symbols for market type: $marketType');

      final response = await _httpClient
          .get(
            Uri.parse('$baseUrl/api/symbols/market/$marketType'),
            headers: _createHeaders(),
          )
          .timeout(MarketServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => SymbolMeta.fromServerResponse(json)).toList();
      } else if (response.statusCode == 401) {
        throw Exception('Unauthorized: Please sign in again');
      } else if (response.statusCode == 404) {
        debugPrint('Market type not found: $marketType');
        return [];
      } else {
        throw Exception('Server error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching symbols for market type $marketType: $e');
      throw Exception('Failed to fetch symbols for market type: $e');
    }
  }

  /// Get all symbols from all brokers
  Future<List<SymbolMeta>> getAllSymbols() async {
    try {
      debugPrint('Fetching all symbols from all brokers');

      final response = await _httpClient
          .get(Uri.parse('$baseUrl/api/symbols'), headers: _createHeaders())
          .timeout(MarketServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => SymbolMeta.fromServerResponse(json)).toList();
      } else if (response.statusCode == 401) {
        throw Exception('Unauthorized: Please sign in again');
      } else {
        throw Exception('Server error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching all symbols: $e');
      throw Exception('Failed to fetch all symbols: $e');
    }
  }

  /// Bulk symbol search using POST endpoint
  Future<List<SymbolMeta>> bulkSymbolSearch(List<String> queries) async {
    if (queries.isEmpty) {
      return [];
    }

    try {
      debugPrint('Performing bulk symbol search for ${queries.length} queries');

      final body = jsonEncode({'queries': queries});

      final response = await _httpClient
          .post(
            Uri.parse('$baseUrl/api/symbols/search'),
            headers: _createHeaders(),
            body: body,
          )
          .timeout(MarketServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => SymbolMeta.fromServerResponse(json)).toList();
      } else if (response.statusCode == 401) {
        throw Exception('Unauthorized: Please sign in again');
      } else if (response.statusCode == 400) {
        final error = jsonDecode(response.body);
        throw Exception(error['message'] ?? 'Invalid request');
      } else {
        throw Exception('Server error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error in bulk symbol search: $e');
      throw Exception('Failed to perform bulk symbol search: $e');
    }
  }

  /// Bulk get most active symbols using POST endpoint
  Future<List<SymbolMeta>> bulkGetActiveSymbols(
    List<String> brokers, {
    int limit = 5,
  }) async {
    try {
      debugPrint('Fetching active symbols from ${brokers.length} brokers');

      final body = jsonEncode({'brokers': brokers, 'limit': limit});

      final response = await _httpClient
          .post(
            Uri.parse('$baseUrl/api/symbols/active'),
            headers: _createHeaders(),
            body: body,
          )
          .timeout(MarketServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => SymbolMeta.fromServerResponse(json)).toList();
      } else if (response.statusCode == 401) {
        throw Exception('Unauthorized: Please sign in again');
      } else if (response.statusCode == 400) {
        final error = jsonDecode(response.body);
        throw Exception(error['message'] ?? 'Invalid request');
      } else {
        throw Exception('Server error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching bulk active symbols: $e');
      throw Exception('Failed to fetch bulk active symbols: $e');
    }
  }

  /// Get bulk prices for multiple symbols (max 100 symbols)
  /// Note: This method is kept for backward compatibility but may use a different endpoint
  Future<BulkPriceResponse> getBulkPrices(List<String> symbols) async {
    if (symbols.isEmpty) {
      return BulkPriceResponse(
        prices: [],
        successCount: 0,
        failureCount: 0,
        timestamp: DateTime.now(),
      );
    }

    if (!isAuthenticated) {
      throw Exception('User not authenticated');
    }

    if (symbols.length > 100) {
      throw Exception('Maximum 100 symbols allowed for bulk prices');
    }

    try {
      debugPrint('Fetching bulk prices for ${symbols.length} symbols');

      // Fetch individual prices and combine them
      final List<SymbolPriceData> prices = [];
      int successCount = 0;
      int failureCount = 0;

      for (final symbol in symbols) {
        try {
          final priceData = await fetchLivePrice(symbol);
          if (priceData != null) {
            prices.add(priceData);
            successCount++;
          } else {
            failureCount++;
          }
        } catch (e) {
          debugPrint('Failed to fetch price for $symbol: $e');
          failureCount++;
        }
      }

      return BulkPriceResponse(
        prices: prices,
        successCount: successCount,
        failureCount: failureCount,
        timestamp: DateTime.now(),
      );
    } catch (e) {
      debugPrint('Error fetching bulk prices: $e');
      throw Exception('Failed to fetch bulk prices: $e');
    }
  }

  /// Get top 10 active symbols from broker with their current prices
  Future<List<SymbolMeta>> getPopularSymbols() async {
    try {
      debugPrint('Fetching top 10 active symbols from broker');

      // First, get available symbols from broker
      final availableSymbols = await _getAvailableSymbolsFromBroker();

      if (availableSymbols.isEmpty) {
        debugPrint('No symbols available from broker, using fallback list');
        return await _getFallbackPopularSymbols();
      }

      // Get a reasonable sample size to find the most active ones
      // Take first 50 symbols or all if less than 50
      final sampleSize =
          availableSymbols.length > 50 ? 50 : availableSymbols.length;
      final sampleSymbols = availableSymbols.take(sampleSize).toList();

      debugPrint(
        'Fetching prices for ${sampleSymbols.length} symbols to find most active',
      );

      final bulkResponse = await getBulkPrices(sampleSymbols);

      // Sort by volume (descending) to get most active symbols
      final sortedPrices =
          bulkResponse.prices.toList()..sort((a, b) {
            final volumeA = a.volume ?? 0;
            final volumeB = b.volume ?? 0;
            return volumeB.compareTo(volumeA);
          });

      // Take top 10 most active symbols
      final top10Prices = sortedPrices.take(10).toList();

      // Convert to SymbolMeta objects
      final symbols =
          top10Prices
              .map(
                (priceData) => SymbolMeta(
                  symbol: priceData.symbol,
                  description: priceData.description ?? priceData.symbol,
                  price: priceData.currentPrice,
                  change: priceData.change,
                  volume: priceData.volume,
                  timestamp: priceData.timestamp,
                  broker: 'Market Service',
                ),
              )
              .toList();

      debugPrint('Successfully loaded ${symbols.length} top active symbols');
      return symbols;
    } catch (e) {
      debugPrint('Error fetching popular symbols: $e');
      // Fallback to hardcoded list if broker fetch fails
      return await _getFallbackPopularSymbols();
    }
  }

  /// Get available symbols from all brokers
  Future<List<String>> _getAvailableSymbolsFromBroker() async {
    try {
      final response = await _httpClient
          .get(Uri.parse('$baseUrl/api/symbols'), headers: _createHeaders())
          .timeout(MarketServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        // Extract symbol names from the response
        return data
            .map((item) {
              if (item is Map<String, dynamic>) {
                return item['symbol']?.toString() ?? '';
              }
              return item.toString();
            })
            .where((symbol) => symbol.isNotEmpty)
            .toList();
      } else {
        debugPrint('Failed to fetch symbols: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      debugPrint('Error fetching symbols: $e');
      return [];
    }
  }

  /// Fallback method with hardcoded popular symbols
  Future<List<SymbolMeta>> _getFallbackPopularSymbols() async {
    debugPrint('Using fallback popular symbols list');

    final fallbackSymbols = [
      'AAPL',
      'GOOGL',
      'MSFT',
      'AMZN',
      'TSLA',
      'META',
      'NVDA',
      'NFLX',
      'BABA',
      'V',
    ];

    final bulkResponse = await getBulkPrices(fallbackSymbols);

    return bulkResponse.prices
        .map(
          (priceData) => SymbolMeta(
            symbol: priceData.symbol,
            description: priceData.description ?? priceData.symbol,
            price: priceData.currentPrice,
            change: priceData.change,
            volume: priceData.volume,
            timestamp: priceData.timestamp,
            broker: 'Market Service (Fallback)',
          ),
        )
        .toList();
  }

  /// Search symbols by query using dedicated search endpoint
  Future<List<SymbolMeta>> searchSymbols(String query) async {
    if (query.trim().isEmpty) {
      return [];
    }

    try {
      debugPrint('Searching symbols with query: "$query"');

      // Use dedicated search endpoint
      final searchResults = await _searchSymbolsFromBroker(query);

      if (searchResults.isEmpty) {
        debugPrint('No symbols found from broker search, trying fallback');
        return await _searchInPopularSymbols(query);
      }

      // Convert search results to SymbolMeta and optionally fetch prices
      final symbols = await _convertSearchResultsToSymbolMeta(searchResults);

      debugPrint('Found ${symbols.length} symbols matching "$query"');
      return symbols;
    } catch (e) {
      debugPrint('Error searching symbols: $e');
      // Fallback to searching in popular symbols
      return await _searchInPopularSymbols(query);
    }
  }

  /// Search symbols using the symbols search endpoint
  Future<List<SymbolSearchResult>> _searchSymbolsFromBroker(
    String query,
  ) async {
    try {
      final response = await _httpClient
          .get(
            Uri.parse(
              '$baseUrl/api/symbols/search?q=${Uri.encodeComponent(query)}',
            ),
            headers: _createHeaders(),
          )
          .timeout(MarketServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data
            .map(
              (json) =>
                  SymbolSearchResult.fromJson(json as Map<String, dynamic>),
            )
            .toList();
      } else {
        debugPrint('Search endpoint returned: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      debugPrint('Error calling search endpoint: $e');
      return [];
    }
  }

  /// Convert search results to SymbolMeta with optional price data
  Future<List<SymbolMeta>> _convertSearchResultsToSymbolMeta(
    List<SymbolSearchResult> searchResults,
  ) async {
    // Extract symbols for price lookup
    final symbols = searchResults.map((result) => result.symbol).toList();

    // Try to get prices for search results (limit to reasonable number)
    final symbolsToPrice = symbols.take(20).toList();
    Map<String, SymbolPriceData> priceMap = {};

    try {
      if (symbolsToPrice.isNotEmpty) {
        final bulkResponse = await getBulkPrices(symbolsToPrice);
        priceMap = {for (var price in bulkResponse.prices) price.symbol: price};
      }
    } catch (e) {
      debugPrint('Could not fetch prices for search results: $e');
      // Continue without prices
    }

    // Convert to SymbolMeta
    return searchResults.map((result) {
      final priceData = priceMap[result.symbol];
      return SymbolMeta(
        symbol: result.symbol,
        description: result.name,
        price: priceData?.currentPrice,
        change: priceData?.change,
        volume: priceData?.volume,
        timestamp: priceData?.timestamp ?? DateTime.now(),
        broker: result.exchange ?? 'Search Result',
      );
    }).toList();
  }

  /// Fallback search in popular symbols
  Future<List<SymbolMeta>> _searchInPopularSymbols(String query) async {
    try {
      debugPrint('Searching in popular symbols as fallback');
      final popularSymbols = await getPopularSymbols();

      final filteredSymbols =
          popularSymbols
              .where(
                (symbol) =>
                    symbol.symbol.toLowerCase().contains(query.toLowerCase()) ||
                    (symbol.description?.toLowerCase().contains(
                          query.toLowerCase(),
                        ) ??
                        false),
              )
              .toList();

      debugPrint('Found ${filteredSymbols.length} symbols in popular symbols');
      return filteredSymbols;
    } catch (e) {
      debugPrint('Error in fallback search: $e');
      return [];
    }
  }

  /// Dispose resources
  void dispose() {
    _httpClient.close();
  }
}
