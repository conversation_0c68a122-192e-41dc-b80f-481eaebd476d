import 'package:flutter/material.dart';
import '../../core/utilities/notch_navbar.dart';
import '../../hub/hub_screen.dart';
import '../../settings/app_settings.dart';
import '../chart/chart_screen.dart';
import '../portifolio/portfolio_screen.dart';
import '../watchlist/watchlist_screen.dart';

class MainScreen extends StatefulWidget {
  final int initialIndex;

  const MainScreen({
    this.initialIndex = 0,
    super.key,
  }); // default to 0 = Watchlist

  @override
  State<MainScreen> createState() => MainScreenState();
}

class MainScreenState extends State<MainScreen> {
  late int _selectedIndex;

  final List<Widget> screens = const [
    WatchlistScreen(),
    ChartScreen(),
    PortfolioScreen(),
    MarketHubScreen(),
    SettingsScreen(),
  ];

  @override
  void initState() {
    super.initState();
    _selectedIndex = widget.initialIndex;
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      body: screens[_selectedIndex],
      bottomNavigationBar: NotchBottomNavBar(
        currentIndex: _selectedIndex,
        onTap: _onItemTapped,
      ),
    );
  }
}
