class Watchlist {
  final int? id;
  final String? userId;
  final String name;
  final DateTime? createdAt;
  final bool isGlobal;

  /// A const constructor lets <PERSON><PERSON> canonicalize identical instances
  const Watchlist({
    this.id,
    this.userId,
    required this.name,
    this.createdAt,
    this.isGlobal = false,
  });

  /// Named constructor for JSON → model
  factory Watchlist.fromJson(Map<String, dynamic> json) {
    return Watchlist(
      id: json['id'] as int?,
      userId: json['user_id'] as String?,
      name: json['name'] as String,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'] as String)
          : null,
      isGlobal: json['is_global'] as bool? ?? false,
    );
  }

  /// Model → JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'name': name,                     // 🔧 fixed key from 'names' → 'name'
      'created_at': createdAt
          ?.toIso8601String(),          // emits null if createdAt is null
      'is_global': isGlobal,
    };
  }

  /// Makes debugPrint(this) more meaningful
  @override
  String toString() {
    return 'Watchlist(id: $id, userId: $userId, name: "$name", '
           'createdAt: $createdAt, isGlobal: $isGlobal)';
  }
}
class WatchlistException implements Exception {
  final String message;
  WatchlistException(this.message);
}