import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../symbol/models/symbol_models.dart';
import '../providers/watchlist_provider.dart';
// items_provider.dart removed - using server-side operations

class PriceChange extends ConsumerWidget {
  final double? width;
  final double? height;
  final SymbolMeta? symbol;

  const PriceChange({super.key, this.width, this.height, this.symbol});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (symbol == null) return const SizedBox.shrink();
    final theme = Theme.of(context).colorScheme;

    // Watch real-time updates for this symbol
    final symbolDataAsync = ref.watch(symbolDataProvider(symbol!.symbol));

    return symbolDataAsync.when(
      data: (realtimeSymbol) {
        // Use server-calculated change if available, otherwise calculate from original price
        final originalPrice = symbol!.price ?? 0.0;
        final currentPrice = realtimeSymbol?.currentPrice ?? originalPrice;
        final change =
            realtimeSymbol?.priceChange ?? (currentPrice - originalPrice);
        final changePercent =
            realtimeSymbol?.priceChangePercent ??
            (originalPrice > 0 ? (change / originalPrice) * 100 : 0.0);

        final isPositive = change >= 0;
        final color = isPositive ? Colors.green : Colors.red;

        // If no significant change, show minimal indicator
        if (change.abs() < 0.01) {
          return Container(
            width: width ?? 60,
            height: height ?? 24,
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Center(
              child: Text(
                '0.00',
                style: TextStyle(
                  color: theme.onSurface,
                  fontWeight: FontWeight.w500,
                  fontSize: 12,
                ),
              ),
            ),
          );
        }

        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          width: width ?? 70,
          height: height ?? 24,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(6),
            boxShadow: [
              BoxShadow(
                color: color.withValues(alpha: 0.3),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Center(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Direction arrow
                Icon(
                  isPositive ? Icons.arrow_upward : Icons.arrow_downward,
                  color: Colors.white,
                  size: 12,
                ),
                const SizedBox(width: 2),
                // Display absolute change and percentage
                Flexible(
                  child: Text(
                    '${change.abs().toStringAsFixed(2)} (${changePercent.abs().toStringAsFixed(1)}%)',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                      fontSize: 10,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        );
      },
      loading:
          () => Container(
            width: width ?? 60,
            height: height ?? 24,
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(6),
            ),
            child: const Center(
              child: SizedBox(
                width: 12,
                height: 12,
                child: CircularProgressIndicator(
                  strokeWidth: 1,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
                ),
              ),
            ),
          ),
      error:
          (error, stack) => Container(
            width: width ?? 60,
            height: height ?? 24,
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(6),
            ),
            child: const Center(
              child: Icon(Icons.error_outline, color: Colors.orange, size: 12),
            ),
          ),
    );
  }
}
