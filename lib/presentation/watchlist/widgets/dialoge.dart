import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../providers/watchlist_provider.dart';
import '../../../services/auth_client.dart';

/// Provider for current user ID
/// This provider checks both AuthClient and Supabase for user ID
final userIdProvider = Provider<String?>((ref) {
  // First try AuthClient (server-side auth)
  final authClient = AuthClient();
  final authClientUserId = authClient.currentUser?.id;
  if (authClientUserId != null) {
    return authClientUserId;
  }

  // Fallback to Supabase auth for backward compatibility
  final supabaseUser = Supabase.instance.client.auth.currentUser;
  return supabaseUser?.id;
});

Future<String?> showCreateWatchlistDialog(
  BuildContext context,
  WidgetRef ref,
) async {
  final theme = Theme.of(context).colorScheme;
  final userId = ref.read(userIdProvider);

  // Validate user session
  if (userId == null) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Log in to create a watchlist')),
    );
    return null;
  }

  return await showDialog<String>(
    context: context,
    builder: (dialogContext) => _CreateWatchlistDialog(theme: theme, ref: ref),
  );
}

class _CreateWatchlistDialog extends ConsumerStatefulWidget {
  final ColorScheme theme;
  final WidgetRef ref;

  const _CreateWatchlistDialog({required this.theme, required this.ref});

  @override
  ConsumerState<_CreateWatchlistDialog> createState() =>
      _CreateWatchlistDialogState();
}

class _CreateWatchlistDialogState
    extends ConsumerState<_CreateWatchlistDialog> {
  late final TextEditingController _inputController;
  bool _isSubmitting = false;
  String? _errorText;

  @override
  void initState() {
    super.initState();
    _inputController = TextEditingController();
  }

  @override
  void dispose() {
    _inputController.dispose();
    super.dispose();
  }

  Future<void> _handleSubmit() async {
    final newName = _inputController.text.trim();

    if (newName.isEmpty) {
      setState(() => _errorText = 'Name cannot be empty');
      return;
    }

    setState(() {
      _isSubmitting = true;
      _errorText = null;
    });

    try {
      await ref
          .read(watchlistNotifierProvider.notifier)
          .createWatchlist(newName);
      if (mounted) {
        Navigator.of(context).pop(newName);
      }
    } on PostgrestException catch (e) {
      setState(() => _errorText = e.message);
    } catch (e) {
      setState(() => _errorText = 'Failed to create watchlist');
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: widget.theme.secondary,
      title: Center(
        child: Text(
          'New Watchlist',
          style: TextStyle(color: widget.theme.onPrimary),
        ),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _inputController,
            autofocus: true,
            enabled: !_isSubmitting,
            decoration: InputDecoration(
              hintText: 'Type watchlist name...',
              errorText: _errorText,
              border: const OutlineInputBorder(),
              filled: true,
              fillColor: widget.theme.primary,
            ),
            textInputAction: TextInputAction.done,
            onSubmitted: (_) => _handleSubmit(),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: _isSubmitting ? null : () => Navigator.pop(context),
          child: Text(
            'Cancel',
            style: TextStyle(color: widget.theme.onPrimary),
          ),
        ),
        ElevatedButton(
          onPressed: _isSubmitting ? null : _handleSubmit,
          style: ElevatedButton.styleFrom(
            backgroundColor: widget.theme.secondary,
            foregroundColor: widget.theme.onPrimary,
          ),
          child:
              _isSubmitting
                  ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                  : Text('Create'),
        ),
      ],
    );
  }
}
