import 'package:abra/presentation/watchlist/widgets/mini_chart.dart';
import 'package:abra/presentation/watchlist/widgets/price_change.dart';
import 'package:flutter/material.dart';
import '../../symbol/models/symbol_models.dart';
import 'price_display.dart';

class SymbolListItem extends StatelessWidget {
  final SymbolMeta symbol;

  const SymbolListItem({super.key, required this.symbol});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: theme.primary,
        child: Text(
          // Added error handling for empty or null symbol.symbol
          symbol.symbol.isNotEmpty ? symbol.symbol[0].toUpperCase() : '?',
          style: TextStyle(color: theme.onPrimary, fontWeight: FontWeight.bold),
        ),
      ),
      title: _SymbolTitle(symbol: symbol.symbol, theme: theme),
      subtitle: _SymbolSubtitle(theme: theme, symbol: symbol),
      // Changed to adaptive trailing content
      trailing: _TrailingContent(symbol: symbol, theme: theme),
    );
  }
}

class _SymbolTitle extends StatelessWidget {
  final String symbol;
  final ColorScheme theme;

  const _SymbolTitle({required this.symbol, required this.theme});

  @override
  Widget build(BuildContext context) {
    return Text(
      symbol,
      style: TextStyle(fontWeight: FontWeight.bold, color: theme.onPrimary),
    );
  }
}

class _SymbolSubtitle extends StatelessWidget {
  final SymbolMeta symbol;
  final ColorScheme theme;

  const _SymbolSubtitle({required this.symbol, required this.theme});

  @override
  Widget build(BuildContext context) {
    return Text(
      symbol.description ?? '...',
      style: TextStyle(color: theme.onPrimary, fontSize: 12),
    );
  }
}

class _TrailingContent extends StatelessWidget {
  final SymbolMeta symbol;
  final ColorScheme theme;

  const _TrailingContent({required this.symbol, required this.theme});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Use new simplified chart with 4H market data
        MiniChart(symbol: symbol.symbol, width: 60, height: 30),
        const SizedBox(width: 10),
        PriceChange(symbol: symbol),
        const SizedBox(width: 10),
        PriceDisplay(symbol: symbol),
      ],
    );
  }
}
