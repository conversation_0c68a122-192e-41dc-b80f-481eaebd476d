import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../symbol/models/symbol_models.dart';
import '../../symbol/providers/symbol_provider.dart';

/// Simplified mini chart widget that displays historical price data
class Mini<PERSON>hart extends ConsumerWidget {
  final String symbol;
  final double width;
  final double height;
  final String interval;
  final int days;

  const MiniChart({
    super.key,
    required this.symbol,
    this.width = 80,
    this.height = 40,
    this.interval = '4h', // 4-hour interval by default
    this.days = 7, // Last 7 days by default
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get historical data for the specified period
    final now = DateTime.now();
    final from = now.subtract(Duration(days: days));

    final historicalParams = HistoricalPriceParams(
      symbol: symbol,
      from: from,
      to: now,
      interval: interval,
    );

    final historicalDataAsync = ref.watch(
      historicalPricesProvider(historicalParams),
    );

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(4)),
      child: historicalDataAsync.when(
        data: (historicalData) {
          if (historicalData.isEmpty) {
            return _buildPlaceholder(context, isEmpty: true);
          }

          return _buildChart(context, historicalData);
        },
        loading: () => _buildPlaceholder(context, isLoading: true),
        error: (error, stack) => _buildPlaceholder(context, hasError: true),
      ),
    );
  }

  Widget _buildChart(BuildContext context, List<HistoricalPriceData> data) {
    return SizedBox(
      width: width,
      height: height,
      child: CustomPaint(painter: MiniChartPainter(data: data)),
    );
  }

  Widget _buildPlaceholder(
    BuildContext context, {
    bool isLoading = false,
    bool hasError = false,
    bool isEmpty = false,
  }) {
    final theme = Theme.of(context).colorScheme;

    IconData icon;
    Color iconColor;

    if (isLoading) {
      return Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: theme.surface.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Center(
          child: SizedBox(
            width: height * 0.4,
            height: height * 0.4,
            child: CircularProgressIndicator(
              strokeWidth: 1.5,
              color: theme.primary.withValues(alpha: 0.7),
            ),
          ),
        ),
      );
    }

    if (hasError) {
      icon = Icons.error_outline;
      iconColor = theme.error.withValues(alpha: 0.6);
    } else if (isEmpty) {
      icon = Icons.show_chart_outlined;
      iconColor = theme.onSurface.withValues(alpha: 0.3);
    } else {
      icon = Icons.show_chart;
      iconColor = theme.onSurface.withValues(alpha: 0.5);
    }

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: theme.surface.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Icon(icon, size: height * 0.5, color: iconColor),
    );
  }
}

/// Custom painter for the mini chart
class MiniChartPainter extends CustomPainter {
  final List<HistoricalPriceData> data;

  MiniChartPainter({required this.data});

  @override
  void paint(Canvas canvas, Size size) {
    if (data.length < 2) return;

    // Extract close prices for the chart
    final prices = data.map((d) => d.close).toList();

    final minPrice = prices.reduce((a, b) => a < b ? a : b);
    final maxPrice = prices.reduce((a, b) => a > b ? a : b);
    final priceRange = maxPrice - minPrice;

    // Handle case where all prices are the same
    if (priceRange == 0) {
      _drawFlatLine(canvas, size, prices.first);
      return;
    }

    // Determine color based on trend (first vs last price)
    final isPositive = prices.last >= prices.first;
    final lineColor = isPositive ? Colors.green : Colors.red;
    final fillColor = lineColor.withValues(alpha: 0.15);

    final path = Path();
    final fillPath = Path();

    // Add some padding to the chart
    final padding = size.height * 0.1;
    final chartHeight = size.height - (padding * 2);

    // Draw the price line
    for (int i = 0; i < prices.length; i++) {
      final x = (i / (prices.length - 1)) * size.width;
      final normalizedPrice = (prices[i] - minPrice) / priceRange;
      final y = padding + (chartHeight - (normalizedPrice * chartHeight));

      if (i == 0) {
        path.moveTo(x, y);
        fillPath.moveTo(x, size.height);
        fillPath.lineTo(x, y);
      } else {
        path.lineTo(x, y);
        fillPath.lineTo(x, y);
      }
    }

    // Complete the fill path
    fillPath.lineTo(size.width, size.height);
    fillPath.close();

    // Draw fill area
    final fillPaint =
        Paint()
          ..color = fillColor
          ..style = PaintingStyle.fill;
    canvas.drawPath(fillPath, fillPaint);

    // Draw price line with smooth curves
    final linePaint =
        Paint()
          ..color = lineColor
          ..strokeWidth = 1.8
          ..style = PaintingStyle.stroke
          ..strokeCap = StrokeCap.round
          ..strokeJoin = StrokeJoin.round;
    canvas.drawPath(path, linePaint);

    // Add a subtle glow effect
    final glowPaint =
        Paint()
          ..color = lineColor.withValues(alpha: 0.3)
          ..strokeWidth = 3.0
          ..style = PaintingStyle.stroke
          ..strokeCap = StrokeCap.round
          ..strokeJoin = StrokeJoin.round
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 1.0);
    canvas.drawPath(path, glowPaint);
  }

  void _drawFlatLine(Canvas canvas, Size size, double price) {
    // Draw a flat line when all prices are the same
    final y = size.height / 2;
    final path =
        Path()
          ..moveTo(0, y)
          ..lineTo(size.width, y);

    final paint =
        Paint()
          ..color = Colors.grey.withValues(alpha: 0.6)
          ..strokeWidth = 1.5
          ..style = PaintingStyle.stroke
          ..strokeCap = StrokeCap.round;

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant MiniChartPainter oldDelegate) {
    return data != oldDelegate.data;
  }
}
