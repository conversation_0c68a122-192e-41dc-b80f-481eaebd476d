import 'package:flutter/material.dart';

class SkeletonSymbolListItem extends StatelessWidget {
  final ColorScheme theme;

  const SkeletonSymbolListItem({super.key, required this.theme});

  @override
  Widget build(BuildContext context) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      title: Container(
        width: 100,
        height: 16,
        color: theme.secondary,
      ),
      subtitle: Container(
        width: 150,
        height: 12,
        color: theme.secondary,
      ),
      trailing: SizedBox(
        width: 250,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Placeholder for mini chart
            Container(
              width: 80,
              height: 50,
              color: theme.secondary,
            ),
            const SizedBox(width: 8),
            // Placeholder for price change indicator
            Container(
              width: 40,
              height: 20,
              decoration: BoxDecoration(
                color: theme.secondary,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            const SizedBox(width: 8),
            // Placeholder for price and percentage
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 50,
                  height: 16,
                  color: theme.secondary,
                ),
                const Sized<PERSON><PERSON>(height: 4),
                Container(
                  width: 70,
                  height: 12,
                  color: theme.secondary,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}