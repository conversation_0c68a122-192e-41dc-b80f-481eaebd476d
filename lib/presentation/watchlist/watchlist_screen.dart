import 'package:abra/core/utilities/dropdown_box.dart';
import 'package:abra/core/connectivity/lost_connection.dart';
import 'package:abra/presentation/symbol/symbol_screen.dart';
import 'package:abra/presentation/watchlist/widgets/skeleton_list.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/connectivity/connectivity_provider.dart';
import 'providers/watchlist_provider.dart';
import '../../services/realtime_service.dart';
import '../../services/market_service.dart';
import 'widgets/dialoge.dart';
import 'widgets/watchlist_items.dart';

class WatchlistScreen extends ConsumerStatefulWidget {
  const WatchlistScreen({super.key});

  @override
  ConsumerState<WatchlistScreen> createState() => _WatchlistScreenState();
}

class _WatchlistScreenState extends ConsumerState<WatchlistScreen> {
  // ignore: unused_field
  bool _isRetrying = false;

  @override
  void initState() {
    super.initState();
    // Only initialize the service here
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(connectivityServiceProvider).initialize();
    });
  }

  void _onConnectionRestored() {
    // Refresh watchlist data when connection is restored using new providers
    ref.invalidate(watchlistsProvider); // New simplified provider
    ref.invalidate(watchlistWithPricesProvider); // New bulk price provider

    // Force refresh real-time prices
    final subscriptionNotifier = ref.read(symbolSubscriptionProvider.notifier);
    subscriptionNotifier.forceRefresh();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Connection restored'),
          duration: Duration(seconds: 2),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  // ignore: unused_element
  Future<void> _onRetry() async {
    setState(() {
      _isRetrying = true;
    });

    final connectivityService = ref.read(connectivityServiceProvider);
    bool hasConnection = await connectivityService.checkInternetAccess();

    if (mounted) {
      setState(() {
        _isRetrying = false;
      });

      ref.read(hasConnectionProvider.notifier).state = hasConnection;

      if (hasConnection) {
        _onConnectionRestored();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // 👇 Listen here, at build-time
    ref.listen<AsyncValue<bool>>(connectivityStreamProvider, (previous, next) {
      next.whenData((hasConnection) {
        ref.read(hasConnectionProvider.notifier).state = hasConnection;
        if (hasConnection && previous?.value == false) {
          _onConnectionRestored();
        }
      });
    });

    final theme = Theme.of(context).colorScheme;
    final connectivity = ref.watch(connectivityStreamProvider);

    return SafeArea(
      child: Scaffold(
        backgroundColor: theme.primary,
        body: Stack(
          children: [
            Column(
              children: [
                _WatchlistHeader(theme: theme),
                Expanded(
                  child:
                      connectivity.value == false
                          ? const NoInternetOverlay()
                          : _SymbolListContent(theme: theme),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _WatchlistHeader extends ConsumerWidget {
  final ColorScheme theme;

  const _WatchlistHeader({required this.theme});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final hasConnection = ref.watch(hasConnectionProvider);
    // Use the first watchlist name as default since we don't have a selected watchlist provider anymore
    final watchlistsAsync = ref.watch(watchlistsProvider);
    final selectedWatchlist = watchlistsAsync.when(
      data:
          (watchlists) =>
              watchlists.isNotEmpty ? watchlists.first.name : 'watchlists',
      loading: () => 'watchlists',
      error: (_, __) => 'watchlists',
    );

    return Container(
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(
        color: theme.primary,
        border: Border(bottom: BorderSide(color: theme.onPrimary, width: 2)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.bookmark_rounded,
            color: hasConnection ? Colors.green : Colors.red,
            size: 35,
          ),
          const SizedBox(width: 10),
          _WatchlistDropdown(theme: theme),
          const SizedBox(width: 10),
          _HeaderIconButton(
            icon: Icons.edit,
            onPressed: () => renameWatchlist(context, ref),
          ),
          const SizedBox(width: 10),
          _HeaderIconButton(
            icon: Icons.add,
            onPressed:
                hasConnection
                    ? () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder:
                            (context) => SymbolListScreen(
                              watchlistName: selectedWatchlist,
                              onSymbolAdded: (symbol) {
                                Navigator.pop(context);
                              },
                            ),
                      ),
                    )
                    : null,
          ),
          _HeaderIconButton(
            icon: Icons.add_circle_outline,
            onPressed:
                hasConnection ? () => _createWatchlist(context, ref) : null,
          ),
        ],
      ),
    );
  }

  Future<void> _createWatchlist(BuildContext context, WidgetRef ref) async {
    final result = await showCreateWatchlistDialog(context, ref);
    if (result != null && context.mounted) {
      ref.invalidate(watchlistsProvider);
      // Set the selected watchlist ID to the newly created one
      final watchlists = await ref.read(watchlistsProvider.future);
      final newWatchlist = watchlists.firstWhere((w) => w.name == result);
      ref.read(selectedWatchlistIdProvider.notifier).state = newWatchlist.id;
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Watchlist "$result" added'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  Future<void> renameWatchlist(BuildContext context, WidgetRef ref) async {
    // Get the currently selected watchlist ID and find the corresponding watchlist
    final selectedId = ref.read(selectedWatchlistIdProvider);
    if (selectedId == null) return;

    final watchlistsAsync = ref.read(watchlistsProvider);
    final selectedWatchlist = await watchlistsAsync.when(
      data: (watchlists) async {
        return watchlists.where((w) => w.id == selectedId).firstOrNull;
      },
      loading: () async => null,
      error: (_, __) async => null,
    );

    if (selectedWatchlist == null) return;

    final TextEditingController controller = TextEditingController(
      text: selectedWatchlist.name,
    );

    if (!context.mounted) return;

    final newName = await showDialog<String>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Rename Watchlist'),
            content: TextField(
              controller: controller,
              decoration: const InputDecoration(
                labelText: 'Watchlist Name',
                border: OutlineInputBorder(),
              ),
              autofocus: true,
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed:
                    () => Navigator.of(context).pop(controller.text.trim()),
                child: const Text('Rename'),
              ),
            ],
          ),
    );

    if (newName != null &&
        newName.isNotEmpty &&
        newName != selectedWatchlist.name) {
      try {
        await ref
            .read(watchlistNotifierProvider.notifier)
            .renameWatchlist(selectedId, newName);
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Watchlist renamed to "$newName"')),
          );
        }
      } catch (error) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to rename watchlist: $error')),
          );
        }
      }
    }
  }
}

class _WatchlistDropdown extends ConsumerWidget {
  final ColorScheme theme;

  const _WatchlistDropdown({required this.theme});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final watchlistsAsync = ref.watch(watchlistsProvider);
    final selectedId = ref.watch(selectedWatchlistIdProvider);
    final hasConnection = ref.watch(hasConnectionProvider);

    return watchlistsAsync.when(
      data: (watchlists) {
        final watchlistNames = watchlists.map((w) => w.name).toList();
        // Find the selected watchlist name by ID, or use the first one
        String selectedName = 'watchlists';
        if (selectedId != null) {
          final selectedWatchlist =
              watchlists.where((w) => w.id == selectedId).firstOrNull;
          selectedName =
              selectedWatchlist?.name ??
              (watchlistNames.isNotEmpty ? watchlistNames[0] : 'watchlists');
        } else if (watchlistNames.isNotEmpty) {
          selectedName = watchlistNames[0];
        }

        return CustomDropdown(
          width: 130,
          height: 30,
          initialValue: selectedName,
          items: watchlistNames,
          onItemSelected: (value) {
            if (hasConnection) {
              // Find the watchlist DTO by name and use its ID
              final selectedWatchlist = watchlists.firstWhere(
                (w) => w.name == value,
              );
              ref.read(selectedWatchlistIdProvider.notifier).state =
                  selectedWatchlist.id;
            }
          },
        );
      },
      loading:
          () => SizedBox(
            width: 130,
            height: 30,
            child: Text(
              hasConnection ? 'loading..' : 'offline',
              style: TextStyle(color: hasConnection ? null : Colors.grey),
            ),
          ),
      error:
          (error, stack) => SizedBox(
            width: 130,
            height: 30,
            child: Text(
              hasConnection ? 'Error loading' : 'offline',
              style: TextStyle(color: hasConnection ? Colors.red : Colors.grey),
            ),
          ),
    );
  }
}

class _HeaderIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onPressed; // Made nullable to support disabled state

  const _HeaderIconButton({required this.icon, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;

    return IconButton(
      icon: Icon(
        icon,
        color: onPressed != null ? theme.onPrimary : Colors.grey,
      ),
      onPressed: onPressed,
    );
  }
}

class _SymbolListContent extends ConsumerWidget {
  final ColorScheme theme;

  const _SymbolListContent({required this.theme});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Use the watchlist with prices provider
    final watchlistWithPricesAsync = ref.watch(watchlistWithPricesProvider);
    final hasConnection = ref.watch(hasConnectionProvider);

    return watchlistWithPricesAsync.when(
      data: (watchlistWithPrices) {
        if (watchlistWithPrices == null || watchlistWithPrices.items.isEmpty) {
          return Center(
            child: Text(
              'No symbols in this watchlist',
              style: TextStyle(color: theme.onPrimary),
            ),
          );
        }

        // Initialize the watchlist subscription manager
        // This will automatically handle symbol subscriptions
        ref.watch(watchlistSubscriptionProvider);

        return _WatchlistItemsView(
          items: watchlistWithPrices.items,
          theme: theme,
        );
      },
      loading: () {
        return SkeletonSymbolListItem(theme: theme);
      },
      error: (error, stack) {
        return NoInternetOverlay(
          onRetry:
              hasConnection
                  ? () => ref.invalidate(watchlistWithPricesProvider)
                  : null,
          isRetrying: true,
        );
      },
    );
  }
}

/// New view component that uses server-side watchlist items with prices
class _WatchlistItemsView extends StatelessWidget {
  final List<WatchlistItemDto> items;
  final ColorScheme theme;

  const _WatchlistItemsView({required this.items, required this.theme});

  @override
  Widget build(BuildContext context) {
    debugPrint(
      '_WatchlistItemsView building with ${items.length} items with server-side prices',
    );

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 2),
      itemCount: items.length,
      itemExtent: 70,
      itemBuilder: (context, index) {
        final item = items[index];
        return WatchlistItem(item: item, theme: theme);
      },
    );
  }
}
