import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../../services/market_service.dart';
import '../../services/auth_client.dart';
import '../../core/constants.dart';

/// Server-centric WatchlistService using abra-servers API endpoints
/// Replaces Supabase direct access with proper server-side operations
class WatchlistService {
  final http.Client _httpClient = http.Client();
  final AuthClient _authClient = AuthClient();

  /// Get the base URL for abra-servers
  String get _baseUrl => MarketServiceConfig.baseUrl;

  /// Load watchlists for a user
  Future<List<WatchlistDto>> loadWatchlists({required String userId}) async {
    try {
      await _authClient.initialize();
      final accessToken = _authClient.accessToken;

      if (accessToken == null) {
        throw Exception('User not authenticated');
      }

      final url = Uri.parse(
        '$_baseUrl${MarketServiceConfig.watchlistEndpoint}',
      );
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $accessToken',
      };

      final response = await _httpClient
          .get(url, headers: headers)
          .timeout(MarketServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.map((json) => WatchlistDto.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load watchlists: ${response.statusCode}');
      }
    } on SocketException {
      debugPrint('Network error loading watchlists');
      return [];
    } catch (e) {
      debugPrint('Error loading watchlists: $e');
      return [];
    }
  }

  /// Create a new watchlist
  Future<WatchlistDto?> createWatchlist({
    required String userId,
    required String name,
  }) async {
    try {
      await _authClient.initialize();
      final accessToken = _authClient.accessToken;

      if (accessToken == null) {
        throw Exception('User not authenticated');
      }

      final url = Uri.parse(
        '$_baseUrl${MarketServiceConfig.watchlistEndpoint}',
      );
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $accessToken',
      };

      final body = jsonEncode({'name': name});

      final response = await _httpClient
          .post(url, headers: headers, body: body)
          .timeout(MarketServiceConfig.requestTimeout);

      if (response.statusCode == 200 || response.statusCode == 201) {
        final data = jsonDecode(response.body);
        return WatchlistDto.fromJson(data);
      } else {
        throw Exception('Failed to create watchlist: ${response.statusCode}');
      }
    } on SocketException {
      debugPrint('Network error creating watchlist');
      return null;
    } catch (e) {
      debugPrint('Error creating watchlist: $e');
      return null;
    }
  }

  /// Delete a watchlist
  Future<bool> deleteWatchlist({
    required String userId,
    required int watchlistId,
  }) async {
    try {
      await _authClient.initialize();
      final accessToken = _authClient.accessToken;

      if (accessToken == null) {
        throw Exception('User not authenticated');
      }

      final url = Uri.parse(
        '$_baseUrl${MarketServiceConfig.watchlistEndpoint}/$watchlistId',
      );
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $accessToken',
      };

      final response = await _httpClient
          .delete(url, headers: headers)
          .timeout(MarketServiceConfig.requestTimeout);

      return response.statusCode == 200 || response.statusCode == 204;
    } on SocketException {
      debugPrint('Network error deleting watchlist');
      return false;
    } catch (e) {
      debugPrint('Error deleting watchlist: $e');
      return false;
    }
  }

  /// Rename a watchlist
  Future<bool> renameWatchlist({
    required String userId,
    required int watchlistId,
    required String newName,
  }) async {
    try {
      await _authClient.initialize();
      final accessToken = _authClient.accessToken;

      if (accessToken == null) {
        throw Exception('User not authenticated');
      }

      final url = Uri.parse(
        '$_baseUrl${MarketServiceConfig.watchlistEndpoint}/$watchlistId',
      );
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $accessToken',
      };

      final body = jsonEncode({'name': newName});

      final response = await _httpClient
          .put(url, headers: headers, body: body)
          .timeout(MarketServiceConfig.requestTimeout);

      return response.statusCode == 200 || response.statusCode == 204;
    } on SocketException {
      debugPrint('Network error renaming watchlist');
      return false;
    } catch (e) {
      debugPrint('Error renaming watchlist: $e');
      return false;
    }
  }

  /// Add symbol to watchlist
  Future<bool> addSymbolToWatchlist({
    required String userId,
    required int watchlistId,
    required String symbol,
  }) async {
    try {
      await _authClient.initialize();
      final accessToken = _authClient.accessToken;

      if (accessToken == null) {
        throw Exception('User not authenticated');
      }

      final url = Uri.parse(
        '$_baseUrl${MarketServiceConfig.watchlistEndpoint}/$watchlistId/symbols',
      );
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $accessToken',
      };

      final body = jsonEncode({
        'symbols': [symbol],
      });

      final response = await _httpClient
          .post(url, headers: headers, body: body)
          .timeout(MarketServiceConfig.requestTimeout);

      return response.statusCode == 200 || response.statusCode == 201;
    } on SocketException {
      debugPrint('Network error adding symbol to watchlist');
      return false;
    } catch (e) {
      debugPrint('Error adding symbol to watchlist: $e');
      return false;
    }
  }

  /// Remove symbol from watchlist
  Future<bool> removeSymbolFromWatchlist({
    required String userId,
    required int watchlistId,
    required String symbol,
  }) async {
    try {
      await _authClient.initialize();
      final accessToken = _authClient.accessToken;

      if (accessToken == null) {
        throw Exception('User not authenticated');
      }

      final url = Uri.parse(
        '$_baseUrl${MarketServiceConfig.watchlistEndpoint}/$watchlistId/symbols/$symbol',
      );
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $accessToken',
      };

      final response = await _httpClient
          .delete(url, headers: headers)
          .timeout(MarketServiceConfig.requestTimeout);

      return response.statusCode == 200 || response.statusCode == 204;
    } on SocketException {
      debugPrint('Network error removing symbol from watchlist');
      return false;
    } catch (e) {
      debugPrint('Error removing symbol from watchlist: $e');
      return false;
    }
  }

  /// Get symbols in a watchlist
  Future<List<String>> getWatchlistSymbols({
    required String userId,
    required int watchlistId,
  }) async {
    try {
      await _authClient.initialize();
      final accessToken = _authClient.accessToken;

      if (accessToken == null) {
        throw Exception('User not authenticated');
      }

      final url = Uri.parse(
        '$_baseUrl${MarketServiceConfig.watchlistEndpoint}/$watchlistId',
      );
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $accessToken',
      };

      final response = await _httpClient
          .get(url, headers: headers)
          .timeout(MarketServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        // Assuming the response contains a 'symbols' field with the list of symbols
        if (data['symbols'] != null) {
          return List<String>.from(data['symbols']);
        }
        return [];
      } else {
        throw Exception(
          'Failed to get watchlist symbols: ${response.statusCode}',
        );
      }
    } on SocketException {
      debugPrint('Network error getting watchlist symbols');
      return [];
    } catch (e) {
      debugPrint('Error getting watchlist symbols: $e');
      return [];
    }
  }

  /// Get watchlist with live prices
  Future<WatchlistWithPricesDto?> getWatchlistWithPrices({
    required String userId,
    required int watchlistId,
  }) async {
    try {
      await _authClient.initialize();
      final accessToken = _authClient.accessToken;

      if (accessToken == null) {
        throw Exception('User not authenticated');
      }

      final url = Uri.parse(
        '$_baseUrl${MarketServiceConfig.watchlistEndpoint}/$watchlistId/with-prices',
      );
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $accessToken',
      };

      final response = await _httpClient
          .get(url, headers: headers)
          .timeout(MarketServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return WatchlistWithPricesDto.fromJson(data);
      } else {
        throw Exception(
          'Failed to get watchlist with prices: ${response.statusCode}',
        );
      }
    } on SocketException {
      debugPrint('Network error getting watchlist with prices');
      return null;
    } catch (e) {
      debugPrint('Error getting watchlist with prices: $e');
      return null;
    }
  }

  /// Reorder watchlist items
  Future<bool> reorderWatchlist({
    required String userId,
    required int watchlistId,
    required List<String> orderedSymbols,
  }) async {
    try {
      await _authClient.initialize();
      final accessToken = _authClient.accessToken;

      if (accessToken == null) {
        throw Exception('User not authenticated');
      }

      final url = Uri.parse(
        '$_baseUrl${MarketServiceConfig.watchlistEndpoint}/$watchlistId/reorder',
      );
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $accessToken',
      };

      final body = jsonEncode({'symbols': orderedSymbols});

      final response = await _httpClient
          .put(url, headers: headers, body: body)
          .timeout(MarketServiceConfig.requestTimeout);

      return response.statusCode == 200 || response.statusCode == 204;
    } on SocketException {
      debugPrint('Network error reordering watchlist');
      return false;
    } catch (e) {
      debugPrint('Error reordering watchlist: $e');
      return false;
    }
  }

  /// Get bulk prices for multiple symbols (max 100 symbols)
  /// This method is deprecated - use MarketService.getBulkPrices() instead
  @Deprecated(
    'Use MarketService.getBulkPrices() for consistent bulk price operations',
  )
  Future<Map<String, dynamic>?> getBulkPrices({
    required List<String> symbols,
  }) async {
    try {
      if (symbols.length > 100) {
        throw Exception('Maximum 100 symbols allowed for bulk prices');
      }

      await _authClient.initialize();
      final accessToken = _authClient.accessToken;

      if (accessToken == null) {
        throw Exception('User not authenticated');
      }

      final url = Uri.parse(
        '$_baseUrl${MarketServiceConfig.watchlistEndpoint}/bulk-prices',
      );
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $accessToken',
      };

      final body = jsonEncode({'symbols': symbols});

      final response = await _httpClient
          .post(url, headers: headers, body: body)
          .timeout(MarketServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to get bulk prices: ${response.statusCode}');
      }
    } on SocketException {
      debugPrint('Network error getting bulk prices');
      return null;
    } catch (e) {
      debugPrint('Error getting bulk prices: $e');
      return null;
    }
  }

  /// Get price analytics for multiple symbols (max 50 symbols)
  Future<Map<String, dynamic>?> getPriceAnalytics({
    required List<String> symbols,
  }) async {
    try {
      if (symbols.length > 50) {
        throw Exception('Maximum 50 symbols allowed for price analytics');
      }

      await _authClient.initialize();
      final accessToken = _authClient.accessToken;

      if (accessToken == null) {
        throw Exception('User not authenticated');
      }

      final url = Uri.parse(
        '$_baseUrl${MarketServiceConfig.watchlistEndpoint}/price-analytics',
      );
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer $accessToken',
      };

      final body = jsonEncode({'symbols': symbols});

      final response = await _httpClient
          .post(url, headers: headers, body: body)
          .timeout(MarketServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception(
          'Failed to get price analytics: ${response.statusCode}',
        );
      }
    } on SocketException {
      debugPrint('Network error getting price analytics');
      return null;
    } catch (e) {
      debugPrint('Error getting price analytics: $e');
      return null;
    }
  }

  /// Dispose resources
  void dispose() {
    _httpClient.close();
  }
}
