import 'package:flutter/material.dart';

/// A reusable filter dialog widget with optional date range, symbol, status, and side filters.
class FilterDialog extends StatefulWidget {
  final String title;
  final List<String> symbols;
  final List<String>? statuses;
  final List<String>? sides;
  final DateTimeRange? initialDateRange;
  final String? initialSymbol;
  final String? initialStatus;
  final String? initialSide;
  final void Function({
    DateTimeRange? dateRange,
    String? symbol,
    String? status,
    String? side,
  }) onApply;

  const FilterDialog({
    super.key,
    required this.title,
    required this.symbols,
    this.statuses,
    this.sides,
    this.initialDateRange,
    this.initialSymbol,
    this.initialStatus,
    this.initialSide,
    required this.onApply,
  });

  @override
  State<FilterDialog> createState() => _FilterDialogState();
}

class _FilterDialogState extends State<FilterDialog> {
  DateTimeRange? pickedDateRange;
  String? pickedSymbol;
  String? pickedStatus;
  String? pickedSide;

  @override
  void initState() {
    super.initState();
    pickedDateRange = widget.initialDateRange;
    pickedSymbol = widget.initialSymbol ?? (widget.symbols.isNotEmpty ? widget.symbols.first : null);
    pickedStatus = widget.initialStatus ?? (widget.statuses != null && widget.statuses!.isNotEmpty ? widget.statuses!.first : null);
    pickedSide = widget.initialSide ?? (widget.sides != null && widget.sides!.isNotEmpty ? widget.sides!.first : null);
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.title),
      content: SizedBox(
        height: 320,
        child: SingleChildScrollView(
          child: Column(
            children: [
              ListTile(
                title: Text(
                  pickedDateRange == null
                      ? 'Select Date Range'
                      : '${pickedDateRange!.start.toLocal()} - ${pickedDateRange!.end.toLocal()}',
                ),
                trailing: const Icon(Icons.calendar_today),
                onTap: () async {
                  final now = DateTime.now();
                  final picked = await showDateRangePicker(
                    context: context,
                    firstDate: DateTime(now.year - 5),
                    lastDate: DateTime(now.year + 1),
                    initialDateRange: pickedDateRange,
                  );
                  if (picked != null) {
                    setState(() {
                      pickedDateRange = picked;
                    });
                  }
                },
              ),
              const SizedBox(height: 12),
              DropdownButton<String>(
                value: pickedSymbol,
                isExpanded: true,
                items: widget.symbols
                    .map((s) => DropdownMenuItem(value: s, child: Text(s)))
                    .toList(),
                onChanged: (val) {
                  setState(() {
                    pickedSymbol = val;
                  });
                },
              ),
              if (widget.statuses != null) ...[
                const SizedBox(height: 12),
                DropdownButton<String>(
                  value: pickedStatus,
                  isExpanded: true,
                  items: widget.statuses!
                      .map((s) => DropdownMenuItem(value: s, child: Text(s)))
                      .toList(),
                  onChanged: (val) {
                    setState(() {
                      pickedStatus = val;
                    });
                  },
                ),
              ],
              if (widget.sides != null) ...[
                const SizedBox(height: 12),
                DropdownButton<String>(
                  value: pickedSide,
                  isExpanded: true,
                  items: widget.sides!
                      .map((s) => DropdownMenuItem(value: s, child: Text(s)))
                      .toList(),
                  onChanged: (val) {
                    setState(() {
                      pickedSide = val;
                    });
                  },
                ),
              ],
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            widget.onApply(
              dateRange: pickedDateRange,
              symbol: pickedSymbol == 'All' ? null : pickedSymbol,
              status: pickedStatus == 'All' ? null : pickedStatus,
              side: pickedSide == 'All' ? null : pickedSide,
            );
            Navigator.pop(context);
          },
          child: const Text('Apply'),
        ),
      ],
    );
  }
}