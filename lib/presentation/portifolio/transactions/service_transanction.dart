import 'dart:async';
import 'dart:math';
import '../models_portfolio.dart';

List<Transaction> mockTransactions = [
  Transaction(
    id: 'tx1',
    date: DateTime.now().subtract(const Duration(days: 1)),
    symbol: 'USD',
    type: 'deposit',
    amount: 1000,
    status: 'completed',
  ),
  Transaction(
    id: 'tx2',
    date: DateTime.now().subtract(const Duration(hours: 5)),
    symbol: 'BTCUSD',
    type: 'fee',
    amount: -10,
    status: 'completed',
  ),
  Transaction(
    id: 'tx3',
    date: DateTime.now().subtract(const Duration(hours: 2)),
    symbol: 'USD',
    type: 'withdrawal',
    amount: -500,
    status: 'pending',
  ),
];

class TransactionStreamService {
  final _controller = StreamController<List<Transaction>>.broadcast();
  final Random _random = Random();
  Timer? _timer;

  final List<Transaction> _transactions = List.from(mockTransactions);

  Stream<List<Transaction>> get transactionsStream => _controller.stream;

  void start() {
    _timer = Timer.periodic(const Duration(seconds: 8), (_) {
      bool hasChange = false;

      // Randomly add a transaction for demo
      if (_random.nextInt(4) == 0) {
        final now = DateTime.now();
        final types = ['deposit', 'withdrawal', 'fee'];
        final newTx = Transaction(
          id: 'tx${_transactions.length + 1}',
          date: now,
          symbol: _random.nextBool() ? 'USD' : 'BTCUSD',
          type: types[_random.nextInt(types.length)],
          amount: (_random.nextDouble() * 1000) * (_random.nextBool() ? 1 : -1),
          status: 'completed',
        );
        _transactions.add(newTx);
        hasChange = true;
      }

      if (hasChange) {
        _controller.add(List<Transaction>.from(_transactions));
      }
    });
  }

  void stop() {
    _timer?.cancel();
    _controller.close();
  }
}