import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../filter_providers.dart';
import '../filter_widget.dart';
import '../models_portfolio.dart';
import 'service_transanction.dart';

class TransactionsTab extends ConsumerStatefulWidget {
  const TransactionsTab({super.key});

  @override
  ConsumerState<TransactionsTab> createState() => TransactionsTabState();
}

class TransactionsTabState extends ConsumerState<TransactionsTab> {
  final TransactionStreamService _transactionStreamService = TransactionStreamService();

  @override
  void initState() {
    super.initState();
    _transactionStreamService.start();
  }

  @override
  void dispose() {
    _transactionStreamService.stop();
    super.dispose();
  }

  void showFilterDialog() async {
    final String all = 'All';
    final symbols = [all, 'BTCUSD', 'ETHUSD', 'ADAUSD'];
    final statuses = [all, 'completed', 'pending', 'failed'];
    final currentFilter = ref.read(transactionsFilterProvider);

    await showDialog(
      context: context,
      builder: (context) => FilterDialog(
        title: 'Filter Transactions',
        symbols: symbols,
        statuses: statuses,
        initialDateRange: currentFilter.dateRange,
        initialSymbol: currentFilter.symbol ?? all,
        initialStatus: currentFilter.status ?? all,
        onApply: ({dateRange, symbol, status, side}) {
          ref.read(transactionsFilterProvider.notifier).applyFilters(
            dateRange: dateRange,
            symbol: symbol,
            status: status == all ? null : status,
          );
        },
      ),
    );
  }

  List<Transaction> _applyFilters(List<Transaction> data) {
    final filter = ref.watch(transactionsFilterProvider);

    return data.where((tx) {
      final inSymbol = filter.symbol == null || tx.symbol == filter.symbol;
      final inStatus = filter.status == null || tx.status.toLowerCase() == filter.status?.toLowerCase();
      final inDateRange = filter.dateRange == null ||
          (tx.date.isAfter(filter.dateRange!.start) &&
              tx.date.isBefore(filter.dateRange!.end.add(const Duration(days: 1))));

      return inSymbol && inStatus && inDateRange;
    }).toList();
  }

  Color _amountColor(double amount) {
    if (amount > 0) return Colors.green;
    if (amount < 0) return Colors.red;
    return Colors.grey;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;

    return Column(
      children: [
        SizedBox(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: const [
              Expanded(flex: 3, child: Text('Date', style: TextStyle(fontWeight: FontWeight.bold))),
              Expanded(flex: 2, child: Text('Currency', style: TextStyle(fontWeight: FontWeight.bold))),
              Expanded(flex: 3, child: Text('Type', style: TextStyle(fontWeight: FontWeight.bold))),
              Expanded(flex: 2, child: Text('Amount', style: TextStyle(fontWeight: FontWeight.bold))),
              Expanded(flex: 2, child: Text('Status', style: TextStyle(fontWeight: FontWeight.bold))),
            ],
          ),
        ),
        Expanded(
          child: StreamBuilder<List<Transaction>>(
            stream: _transactionStreamService.transactionsStream,
            initialData: const [],
            builder: (context, snapshot) {
              final txList = snapshot.data ?? [];
              final filtered = _applyFilters(txList);

              if (filtered.isEmpty) {
                return const Center(child: Text('No transactions'));
              }

              return ListView.builder(
                itemCount: filtered.length,
                itemBuilder: (context, index) {
                  final tx = filtered[index];

                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                    decoration: BoxDecoration(
                      border: Border(bottom: BorderSide(color: theme.tertiary)),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 3,
                          child: Text(
                            '${tx.date.toLocal()}'.split(' ')[0],
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(tx.symbol),
                        ),
                        Expanded(
                          flex: 3,
                          child: Text(tx.type),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            tx.amount.toStringAsFixed(2),
                            style: TextStyle(
                              color: _amountColor(tx.amount),
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(tx.status),
                        ),
                      ],
                    ),
                  );
                },
              );
            },
          ),
        ),
      ],
    );
  }
}