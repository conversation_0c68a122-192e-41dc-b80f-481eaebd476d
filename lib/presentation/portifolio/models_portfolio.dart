class Position {
  final String id;
  final String symbol;
  final String instrument;
  final Side side;
  final double quantity;
  final double entryPrice;
  final double currentPrice;
  final double unrealizedPnl;
  final double leverage;
  final double marginUsed;
  final DateTime timestamp;
  final double? fee;

  Position({
    required this.id,
    required this.symbol,
    required this.instrument,
    required this.side,
    required this.quantity,
    required this.entryPrice,
    required this.currentPrice,
    required this.unrealizedPnl,
    required this.leverage,
    required this.marginUsed,
    required this.timestamp,
    this.fee,
  });

  
}


// order_model.dart
class Order {
  final String id;
  final String symbol;
  final String orderType;
  final Side side;
  double price;
  double quantity;
  Status status;
  final DateTime createdAt;
  DateTime updatedAt;

  Order({
    required this.id,
    required this.symbol,
    required this.orderType,
    required this.side,
    required this.price,
    required this.quantity,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Order.fromMap(Map<String, dynamic> map) {
    return Order(
      id: map['id'] as String,
      symbol: map['symbol'] as String,
      orderType: map['order_type'] as String,
      side: map['side'] as Side,
      price: (map['price'] as num).toDouble(),
      quantity: (map['quantity'] as num).toDouble(),
      status: map['status'] as Status,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: DateTime.parse(map['updated_at'] as String),
    );
  }
}


class History {
  final String id;
  final String symbol;
  final Side side;
  final double quantity;
  final double entryPrice;
  final double exitPrice;
  final double realizedPnl;
  final DateTime openedAt;
  final DateTime closedAt;
  final int durationMinutes;

  History({
    required this.id,
    required this.symbol,
    required this.side,
    required this.quantity,
    required this.entryPrice,
    required this.exitPrice,
    required this.realizedPnl,
    required this.openedAt,
    required this.closedAt,
    required this.durationMinutes,
  });

  factory History.fromMap(Map<String, dynamic> map) {
    return History(
      id: map['id'] as String,
      symbol: map['symbol'] as String,
      side: map['side'] as Side,
      quantity: (map['quantity'] as num).toDouble(),
      entryPrice: (map['entry_price'] as num).toDouble(),
      exitPrice: (map['exit_price'] as num).toDouble(),
      realizedPnl: (map['realized_pnl'] as num).toDouble(),
      openedAt: DateTime.parse(map['opened_at'] as String),
      closedAt: DateTime.parse(map['closed_at'] as String),
      durationMinutes: map['duration_minutes'] as int,
    );
  }
}


class Transaction {
  final String id;
  final DateTime date;
  final String symbol;
  final String type; // e.g., deposit, withdrawal, fee
  final double amount;
  final String status;

  Transaction({
    required this.id,
    required this.date,
    required this.symbol,
    required this.type,
    required this.amount,
    required this.status,
  });

  factory Transaction.fromMap(Map<String, dynamic> map) {
    return Transaction(
      id: map['id'] as String,
      date: DateTime.parse(map['date'] as String),
      symbol: map['symbol'] as String,
      type: map['type'] as String,
      amount: (map['amount'] as num).toDouble(),
      status: map['status'] as String,
    );
  }
}
enum Status { 
  active, filled, 
  pending, cancelled, rejected;

  String get name {
    switch (this) {
      case Status.active:
        return 'active';
      case Status.filled:
        return 'filled';
      case Status.pending:
        return 'pending';
      case Status.cancelled:
        return 'cancelled';
      case Status.rejected:
        return 'rejected';
    }
  }
}
enum Side {
  buy, sell;

  String get name {
    switch (this) {
      case Side.buy:
        return 'Buy';
      case Side.sell:
        return 'Sell';
    }
  }
}

// Portfolio-specific models
enum TransactionType {
  buy, sell, deposit, withdrawal, dividend, fee;

  String get name {
    switch (this) {
      case TransactionType.buy:
        return 'Buy';
      case TransactionType.sell:
        return 'Sell';
      case TransactionType.deposit:
        return 'Deposit';
      case TransactionType.withdrawal:
        return 'Withdrawal';
      case TransactionType.dividend:
        return 'Dividend';
      case TransactionType.fee:
        return 'Fee';
    }
  }
}

class Portfolio {
  final String id;
  final String name;
  final String description;
  final String? brokerId;
  final double cash;
  final double totalValue;
  final double dayChange;
  final double dayChangePercent;
  final double totalReturn;
  final double totalReturnPercent;
  final List<PortfolioPosition> positions;
  final List<PortfolioTransaction> transactions;
  final DateTime createdAt;
  final DateTime updatedAt;

  Portfolio({
    required this.id,
    required this.name,
    required this.description,
    this.brokerId,
    required this.cash,
    required this.totalValue,
    required this.dayChange,
    required this.dayChangePercent,
    required this.totalReturn,
    required this.totalReturnPercent,
    required this.positions,
    required this.transactions,
    required this.createdAt,
    required this.updatedAt,
  });

  Portfolio copyWith({
    String? id,
    String? name,
    String? description,
    String? brokerId,
    double? cash,
    double? totalValue,
    double? dayChange,
    double? dayChangePercent,
    double? totalReturn,
    double? totalReturnPercent,
    List<PortfolioPosition>? positions,
    List<PortfolioTransaction>? transactions,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Portfolio(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      brokerId: brokerId ?? this.brokerId,
      cash: cash ?? this.cash,
      totalValue: totalValue ?? this.totalValue,
      dayChange: dayChange ?? this.dayChange,
      dayChangePercent: dayChangePercent ?? this.dayChangePercent,
      totalReturn: totalReturn ?? this.totalReturn,
      totalReturnPercent: totalReturnPercent ?? this.totalReturnPercent,
      positions: positions ?? this.positions,
      transactions: transactions ?? this.transactions,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

class PortfolioPosition {
  final String symbol;
  final double quantity;
  final double averagePrice;
  final double currentPrice;
  final double marketValue;
  final double unrealizedPnL;
  final double unrealizedPnLPercent;
  final double dayChange;
  final double dayChangePercent;
  final DateTime addedAt;
  final DateTime updatedAt;

  PortfolioPosition({
    required this.symbol,
    required this.quantity,
    required this.averagePrice,
    required this.currentPrice,
    required this.marketValue,
    required this.unrealizedPnL,
    required this.unrealizedPnLPercent,
    required this.dayChange,
    required this.dayChangePercent,
    required this.addedAt,
    required this.updatedAt,
  });

  PortfolioPosition copyWith({
    String? symbol,
    double? quantity,
    double? averagePrice,
    double? currentPrice,
    double? marketValue,
    double? unrealizedPnL,
    double? unrealizedPnLPercent,
    double? dayChange,
    double? dayChangePercent,
    DateTime? addedAt,
    DateTime? updatedAt,
  }) {
    return PortfolioPosition(
      symbol: symbol ?? this.symbol,
      quantity: quantity ?? this.quantity,
      averagePrice: averagePrice ?? this.averagePrice,
      currentPrice: currentPrice ?? this.currentPrice,
      marketValue: marketValue ?? this.marketValue,
      unrealizedPnL: unrealizedPnL ?? this.unrealizedPnL,
      unrealizedPnLPercent: unrealizedPnLPercent ?? this.unrealizedPnLPercent,
      dayChange: dayChange ?? this.dayChange,
      dayChangePercent: dayChangePercent ?? this.dayChangePercent,
      addedAt: addedAt ?? this.addedAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

class PortfolioTransaction {
  final String id;
  final String portfolioId;
  final String symbol;
  final TransactionType type;
  final double quantity;
  final double price;
  final double total;
  final String? brokerId;
  final DateTime timestamp;

  PortfolioTransaction({
    required this.id,
    required this.portfolioId,
    required this.symbol,
    required this.type,
    required this.quantity,
    required this.price,
    required this.total,
    this.brokerId,
    required this.timestamp,
  });
}

class PortfolioResult<T> {
  final bool isSuccess;
  final T? data;
  final String? error;

  PortfolioResult._({
    required this.isSuccess,
    this.data,
    this.error,
  });

  factory PortfolioResult.success(T data) {
    return PortfolioResult._(
      isSuccess: true,
      data: data,
    );
  }

  factory PortfolioResult.failure(String error) {
    return PortfolioResult._(
      isSuccess: false,
      error: error,
    );
  }
}

abstract class PortfolioEvent {
  const PortfolioEvent();

  factory PortfolioEvent.created(Portfolio portfolio) = PortfolioCreatedEvent;
  factory PortfolioEvent.updated(Portfolio portfolio) = PortfolioUpdatedEvent;
  factory PortfolioEvent.deleted(String portfolioId) = PortfolioDeletedEvent;
  factory PortfolioEvent.positionAdded(Portfolio portfolio, PortfolioTransaction transaction) = PortfolioPositionAddedEvent;
  factory PortfolioEvent.synced(Portfolio portfolio) = PortfolioSyncedEvent;
  factory PortfolioEvent.pricesUpdated(Portfolio portfolio) = PortfolioPricesUpdatedEvent;
}

class PortfolioCreatedEvent extends PortfolioEvent {
  final Portfolio portfolio;
  const PortfolioCreatedEvent(this.portfolio);
}

class PortfolioUpdatedEvent extends PortfolioEvent {
  final Portfolio portfolio;
  const PortfolioUpdatedEvent(this.portfolio);
}

class PortfolioDeletedEvent extends PortfolioEvent {
  final String portfolioId;
  const PortfolioDeletedEvent(this.portfolioId);
}

class PortfolioPositionAddedEvent extends PortfolioEvent {
  final Portfolio portfolio;
  final PortfolioTransaction transaction;
  const PortfolioPositionAddedEvent(this.portfolio, this.transaction);
}

class PortfolioSyncedEvent extends PortfolioEvent {
  final Portfolio portfolio;
  const PortfolioSyncedEvent(this.portfolio);
}

class PortfolioPricesUpdatedEvent extends PortfolioEvent {
  final Portfolio portfolio;
  const PortfolioPricesUpdatedEvent(this.portfolio);
}

class PortfolioPerformance {
  final double totalValue;
  final double totalReturn;
  final double totalReturnPercent;
  final double dayChange;
  final double dayChangePercent;
  final double cash;
  final double investedAmount;
  final int positionCount;
  final PortfolioPosition? topPosition;
  final PortfolioPosition? worstPosition;
  final Map<String, double> assetAllocation;
  final Map<String, double> sectorAllocation;

  PortfolioPerformance({
    required this.totalValue,
    required this.totalReturn,
    required this.totalReturnPercent,
    required this.dayChange,
    required this.dayChangePercent,
    required this.cash,
    required this.investedAmount,
    required this.positionCount,
    this.topPosition,
    this.worstPosition,
    required this.assetAllocation,
    required this.sectorAllocation,
  });
}