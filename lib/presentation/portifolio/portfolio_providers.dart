import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'portfolio_service.dart';
import 'models_portfolio.dart';
import '../../services/auth_client.dart';
import '../../presentation/symbol/providers/symbol_provider.dart';
import '../../brokers/providers/broker_service_provider.dart';

/// Enhanced portfolio service providers
final enhancedPortfolioServiceProvider = Provider<PortfolioService>((ref) {
  final service = PortfolioService();
  final symbolService = ref.watch(symbolServiceProvider);
  final brokerService = ref.watch(enhancedBrokerServiceProvider);
  final authClient = AuthClient();

  service.initialize(
    symbolService: symbolService,
    brokerService: brokerService,
    authClient: authClient,
  );

  ref.onDispose(() => service.dispose());
  return service;
});

/// All portfolios provider
final portfoliosProvider =
    StateNotifierProvider<PortfoliosNotifier, List<Portfolio>>((ref) {
      return PortfoliosNotifier(ref);
    });

class PortfoliosNotifier extends StateNotifier<List<Portfolio>> {
  final Ref ref;
  late final PortfolioService _portfolioService;

  PortfoliosNotifier(this.ref) : super([]) {
    _portfolioService = ref.read(enhancedPortfolioServiceProvider);
    _loadPortfolios();
  }

  void _loadPortfolios() {
    state = _portfolioService.getAllPortfolios();
  }

  Future<void> createPortfolio({
    required String name,
    String? description,
    double? initialCash,
    String? brokerId,
  }) async {
    final result = await _portfolioService.createPortfolio(
      name: name,
      description: description,
      initialCash: initialCash,
      brokerId: brokerId,
    );

    if (result.isSuccess) {
      _loadPortfolios();
    } else {
      throw Exception(result.error);
    }
  }

  Future<void> updatePortfolio({
    required String portfolioId,
    String? name,
    String? description,
    String? brokerId,
  }) async {
    final result = await _portfolioService.updatePortfolio(
      portfolioId: portfolioId,
      name: name,
      description: description,
      brokerId: brokerId,
    );

    if (result.isSuccess) {
      _loadPortfolios();
    } else {
      throw Exception(result.error);
    }
  }

  Future<void> deletePortfolio(String portfolioId) async {
    final success = await _portfolioService.deletePortfolio(portfolioId);
    if (success) {
      _loadPortfolios();
    }
  }

  Future<void> addPosition({
    required String portfolioId,
    required String symbol,
    required double quantity,
    required double price,
    required TransactionType transactionType,
    String? brokerId,
  }) async {
    final result = await _portfolioService.addPosition(
      portfolioId: portfolioId,
      symbol: symbol,
      quantity: quantity,
      price: price,
      transactionType: transactionType,
      brokerId: brokerId,
    );

    if (result.isSuccess) {
      _loadPortfolios();
    } else {
      throw Exception(result.error);
    }
  }

  Future<void> syncWithBroker(String portfolioId) async {
    final result = await _portfolioService.syncWithBroker(portfolioId);
    if (result.isSuccess) {
      _loadPortfolios();
    } else {
      throw Exception(result.error);
    }
  }

  Future<void> updatePrices(String portfolioId) async {
    final result = await _portfolioService.updatePortfolioPrices(portfolioId);
    if (result.isSuccess) {
      _loadPortfolios();
    } else {
      throw Exception(result.error);
    }
  }
}

/// Portfolio by ID provider
final portfolioProvider = Provider.family<Portfolio?, String>((
  ref,
  portfolioId,
) {
  final portfolios = ref.watch(portfoliosProvider);
  try {
    return portfolios.firstWhere((p) => p.id == portfolioId);
  } catch (e) {
    return null;
  }
});

/// Portfolio performance provider
final portfolioPerformanceProvider =
    FutureProvider.family<PortfolioPerformance, String>((
      ref,
      portfolioId,
    ) async {
      final portfolioService = ref.watch(enhancedPortfolioServiceProvider);
      return await portfolioService.getPortfolioPerformance(portfolioId);
    });

/// Portfolio events stream provider
final portfolioEventsProvider = StreamProvider<PortfolioEvent>((ref) {
  final portfolioService = ref.watch(enhancedPortfolioServiceProvider);
  return portfolioService.portfolioEvents;
});

/// Total portfolio value provider
final totalPortfolioValueProvider = Provider<double>((ref) {
  final portfolios = ref.watch(portfoliosProvider);
  return portfolios.fold<double>(
    0.0,
    (sum, portfolio) => sum + portfolio.totalValue,
  );
});

/// Total portfolio return provider
final totalPortfolioReturnProvider = Provider<double>((ref) {
  final portfolios = ref.watch(portfoliosProvider);
  return portfolios.fold<double>(
    0.0,
    (sum, portfolio) => sum + portfolio.totalReturn,
  );
});
