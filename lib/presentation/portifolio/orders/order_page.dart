import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../filter_providers.dart';
import '../filter_widget.dart';
import '../models_portfolio.dart';
import 'service_orders.dart';

class OrdersTab extends ConsumerStatefulWidget {
  const OrdersTab({super.key});

  @override
  ConsumerState<OrdersTab> createState() => OrdersTabState();
}

class OrdersTabState extends ConsumerState<OrdersTab> {
  final OrderStreamService _orderStreamService = OrderStreamService();

  @override
  void initState() {
    super.initState();
    _orderStreamService.start();
  }

  @override
  void dispose() {
    _orderStreamService.stop();
    super.dispose();
  }

  void showFilterDialog() async {
    final String all = 'All';
    final symbols = [all, 'BTCUSD', 'ETHUSD', 'ADAUSD'];
    final statuses = [
      all,
      Status.active.name,
      Status.filled.name,
      Status.pending.name,
      Status.cancelled.name,
      Status.rejected.name
    ];
    final sides = [all, Side.buy.name, Side.sell.name];

    final currentFilter = ref.read(ordersFilterProvider);

    await showDialog(
      context: context,
      builder: (context) => FilterDialog(
        title: 'Filter Orders',
        symbols: symbols,
        statuses: statuses,
        sides: sides,
        initialDateRange: currentFilter.dateRange,
        initialSymbol: currentFilter.symbol ?? all,
        initialStatus: currentFilter.status?.name ?? all,
        initialSide: currentFilter.side?.name ?? all,
        onApply: ({dateRange, symbol, status, side}) {
          ref.read(ordersFilterProvider.notifier).applyFilters(
            dateRange: dateRange,
            symbol: symbol,
            status: status == all ? null : Status.values.firstWhere((s) => s.name == status),
            side: side == all ? null : Side.values.firstWhere((s) => s.name == side),
          );
        },
      ),
    );
  }

  List<Order> _applyFilters(List<Order> data) {
    final filter = ref.watch(ordersFilterProvider);

    return data.where((order) {
      final inSymbol = filter.symbol == null || order.symbol == filter.symbol;
      final inStatus =
          filter.status == null || order.status.name == filter.status?.name;
      final inSide = filter.side == null || order.side.name == filter.side?.name;
      final inDateRange = filter.dateRange == null ||
          (order.createdAt.isAfter(filter.dateRange!.start) &&
              order.createdAt.isBefore(filter.dateRange!.end.add(const Duration(days: 1))));

      return inSymbol && inStatus && inSide && inDateRange;
    }).toList();
  }

  Color _statusColor(Status status) {
    switch (status) {
      case Status.active:
        return const Color.fromARGB(255, 4, 238, 12);
      case Status.filled:
        return const Color.fromARGB(255, 0, 94, 255);
      case Status.pending:
        return Colors.grey;
      case Status.cancelled:
        return const Color.fromARGB(255, 255, 17, 0);
      case Status.rejected:
        return const Color.fromARGB(255, 245, 141, 5);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;

    return StreamBuilder<List<Order>>(
      stream: _orderStreamService.ordersStream,
      initialData: [],
      builder: (context, snapshot) {
        final orders = snapshot.data ?? [];
        final filteredOrders = _applyFilters(orders);

        return Column(
          children: [
            Container(
              color: theme.primary,
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: const [
                  Expanded(flex: 2, child: Text('Symbol', style: TextStyle(fontWeight: FontWeight.bold))),
                  Expanded(flex: 1, child: Text('Side', style: TextStyle(fontWeight: FontWeight.bold))),
                  Expanded(flex: 2, child: Text('Qty', style: TextStyle(fontWeight: FontWeight.bold))),
                  Expanded(flex: 3, child: Text('Price', style: TextStyle(fontWeight: FontWeight.bold))),
                  Expanded(flex: 3, child: Text('created At', style: TextStyle(fontWeight: FontWeight.bold))),
                  Expanded(flex: 2, child: Text('Status', style: TextStyle(fontWeight: FontWeight.bold))),
                ],
              ),
            ),
            const SizedBox(height: 8),
            if (filteredOrders.isEmpty)
              const Expanded(
                child: Center(child: Text('No orders available')),
              )
            else
              Expanded(
                child: ListView.builder(
                  itemCount: filteredOrders.length,
                  itemBuilder: (context, index) {
                    final o = filteredOrders[index];

                    return Card(
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            Expanded(
                              flex: 2,
                              child: Text(
                                o.symbol.toString(),
                                style: const TextStyle(fontWeight: FontWeight.w400),
                              ),
                            ),
                            Expanded(
                              flex: 1,
                              child: Text(o.side.name),
                            ),
                            Expanded(
                              flex: 2,
                              child: Text(o.quantity.toStringAsFixed(2)),
                            ),
                            Expanded(
                              flex: 3,
                              child: Text(
                                o.price.toStringAsFixed(2),
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            Expanded(
                              flex: 3,
                              child: Text(o.createdAt.toLocal().toString()),
                            ),
                            Expanded(
                              child: Text(
                                o.status.name,
                                style: TextStyle(color: _statusColor(o.status)),
                              )
                            )
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
          ],
        );
      },
    );
  }
}