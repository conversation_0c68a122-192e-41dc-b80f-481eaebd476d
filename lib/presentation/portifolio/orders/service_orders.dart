import 'dart:async';
import 'dart:math';
import '../models_portfolio.dart';

class OrderStreamService {
  final _controller = StreamController<List<Order>>.broadcast();
  final Random _random = Random();
  Timer? _timer;

  final List<Order> _orders = List.from(mockOrders);

  Stream<List<Order>> get ordersStream => _controller.stream;

  void start() {
    // Simulate random updates but only push when changes occur
    _timer = Timer.periodic(Duration(seconds: 5), (_) {
      bool hasChange = false;

      for (var order in _orders) {
        // Random chance to update an order
        if (_random.nextBool()) {
          // Randomly update quantity
          double deltaQty = (_random.nextDouble() * 0.2) - 0.1;
          order.quantity = (order.quantity + deltaQty).clamp(0.1, 10);

          // Randomly update status if pending
          if (order.status == Status.pending && _random.nextBool()) {
            order.status = _random.nextBool() ? Status.filled : Status.filled;
            order.updatedAt = DateTime.now();
          }

          hasChange = true;
        }
      }

      if (hasChange) {
        _controller.add(List<Order>.from(_orders));
      }
    });
  }

  void stop() {
    _timer?.cancel();
    _controller.close();
  }
}

// mock_orders.dart

List<Order> mockOrders = [
  Order(
    id: 'ord1',
    symbol: 'BTCUSD',
    orderType: 'limit',
    side: Side.buy,
    price: 29000,
    quantity: 0.5,
    status: Status.filled,
    createdAt: DateTime.now().subtract(const Duration(minutes: 10)),
    updatedAt: DateTime.now().subtract(const Duration(minutes: 10)),
  ),
  Order(
    id: 'ord2',
    symbol: 'ETHUSD',
    orderType: 'market',
    side: Side.sell,
    price: 1900,
    quantity: 1.2,
    status: Status.filled,
    createdAt: DateTime.now().subtract(const Duration(hours: 1)),
    updatedAt: DateTime.now().subtract(const Duration(minutes: 30)),
  ),
];
