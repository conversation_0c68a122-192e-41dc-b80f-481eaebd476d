import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../filter_providers.dart';
import '../filter_widget.dart';
import '../models_portfolio.dart';
import 'service_positions.dart';

class PositionsTab extends ConsumerStatefulWidget {
  const PositionsTab({super.key});

  @override
  ConsumerState<PositionsTab> createState() => PositionsTabState();
}

class PositionsTabState extends ConsumerState<PositionsTab> {
  late AccountSummary summary;
  Timer? _timer;
  final Random _random = Random();

  @override
  void initState() {
    super.initState();
    summary = mockAccountSummary;
    _startAutoRefresh();
  }

  void _startAutoRefresh() {
    _timer = Timer.periodic(const Duration(seconds: 3), (_) {
      if (!mounted) return;
      _updateSummary();
    });
  }

  void _updateSummary() {
    // Simulate a slight change in PnL
    final double newPnl = summary.pnl + _random.nextDouble() * 20 - 10;
    final double newEquity = summary.balance + newPnl;
    final double newFreeMargin = newEquity - summary.marginUsed;
    final double newMarginLevel = summary.marginUsed > 0
        ? (newEquity / summary.marginUsed) * 100
        : 0;

    setState(() {
      summary = AccountSummary(
        pnl: double.parse(newPnl.toStringAsFixed(2)),
        equity: double.parse(newEquity.toStringAsFixed(2)),
        balance: summary.balance,
        marginUsed: summary.marginUsed,
        marginLevel: double.parse(newMarginLevel.toStringAsFixed(1)),
        freeMargin: double.parse(newFreeMargin.toStringAsFixed(2)),
      );
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void showFilterDialog() async {
    final String all = 'All';
    final symbols = [all, 'BTCUSD', 'ETHUSD', 'ADAUSD'];
    final sides = [all, Side.buy.name, Side.sell.name];

    final currentFilter = ref.read(positionsFilterProvider);

    await showDialog(
      context: context,
      builder: (context) => FilterDialog(
        title: 'Filter Positions',
        symbols: symbols,
        sides: sides,
        initialDateRange: currentFilter.dateRange,
        initialSymbol: currentFilter.symbol ?? all,
        initialSide: currentFilter.side?.name ?? all,
        onApply: ({dateRange, symbol, status, side}) {
          ref.read(positionsFilterProvider.notifier).applyFilters(
            dateRange: dateRange,
            symbol: symbol,
            side: side == all ? null : Side.values.firstWhere((s) => s.name == side),
          );
        },
      ),
    );
  }

  List<Position> _applyFilters(List<Position> data) {
    final filter = ref.watch(positionsFilterProvider);

    return data.where((position) {
      final inSymbol = filter.symbol == null || position.symbol == filter.symbol;
      final inSide = filter.side == null || position.side.name == filter.side?.name.toLowerCase();

      return inSymbol && inSide;
    }).toList();
  }

  Widget _buildSummaryBox(String label, String value, {Color? color}) {
    final theme = Theme.of(context).colorScheme;
    return SizedBox(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            label,
            style: TextStyle(color: theme.onPrimary),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: color ?? theme.onPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryHeader() {
    final width = MediaQuery.of(context).size.width;

    return SizedBox(
      width: width,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 1, vertical: 4),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildSummaryBox(
              'PnL',
              '${summary.pnl >= 0 ? '+' : ''}${summary.pnl.toStringAsFixed(2)}',
              color: summary.pnl >= 0 ? Colors.green : Colors.red,
            ),
            _buildSummaryBox('Equity', summary.equity.toStringAsFixed(2)),
            _buildSummaryBox('Balance', summary.balance.toStringAsFixed(2)),
            _buildSummaryBox('Margin Used', summary.marginUsed.toStringAsFixed(2)),
            _buildSummaryBox('Margin Level', '${summary.marginLevel.toStringAsFixed(1)}%'),
            _buildSummaryBox('Free Margin', summary.freeMargin.toStringAsFixed(2)),
          ],
        ),
      )
    );
  }

  (Color, IconData) _getSideProperties(Side side) {
    final bool isBuy = side.name == Side.buy.name;
    return (
      isBuy ? Colors.green : Colors.red,
      isBuy ? Icons.arrow_upward : Icons.arrow_downward,
    );
  }

  Widget _buildPositionCard(Position pos) {
    final theme = Theme.of(context).colorScheme;
    final bool isProfit = pos.unrealizedPnl >= 0;
    final (sideColor, sideIcon) = _getSideProperties(pos.side);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: ListTile(
        minTileHeight: 30,
        leading: CircleAvatar(
          child: Icon(sideIcon, color: sideColor),
        ),
        title: Text(
          pos.symbol,
          style: TextStyle(fontWeight: FontWeight.bold, color: theme.onPrimary),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Qty: ${pos.quantity}, Leverage: x${pos.leverage.toStringAsFixed(0)}', 
              style:TextStyle(fontSize: 12, color: theme.onPrimary)),
            Text('Entry: ${pos.entryPrice}, Now: ${pos.currentPrice}', 
              style: TextStyle(fontSize: 12, color: theme.onPrimary)),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '${isProfit ? '+' : ''}${pos.unrealizedPnl.toStringAsFixed(2)} USD',
              style: TextStyle(
                color: isProfit ? Colors.green : Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              'fee: ${pos.fee?.toStringAsFixed(1) ?? 'N/A'}',
              style: TextStyle(fontSize: 12, color: theme.secondary),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final filteredPositions = _applyFilters(mockPositions);

    return Column(
      children: [
        _buildSummaryHeader(),
        const SizedBox(height: 8),
        Expanded(
          child: filteredPositions.isEmpty
              ? const Center(child: Text('No positions match the current filters'))
              : ListView.builder(
                  itemCount: filteredPositions.length,
                  itemBuilder: (context, index) => _buildPositionCard(filteredPositions[index]),
                ),
        ),
      ],
    );
  }
}