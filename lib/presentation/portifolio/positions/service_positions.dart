import '../models_portfolio.dart';

// account_summary_model.dart
class AccountSummary {
  final double pnl;
  final double equity;
  final double balance;
  final double marginUsed;
  final double marginLevel;
  final double freeMargin;

  AccountSummary({
    required this.pnl,
    required this.equity,
    required this.balance,
    required this.marginUsed,
    required this.marginLevel,
    required this.freeMargin,
  });
}


final AccountSummary mockAccountSummary = AccountSummary(
  pnl: 85.0,
  equity: 1085.0,
  balance: 1000.0,
  marginUsed: 150.0,
  marginLevel: 723.3,
  freeMargin: 935.0,
);

final List<Position> mockPositions = [
  Position(
    id: 'pos1',
    symbol: 'EURUSD',
    instrument: 'Euro / US Dollar',
    side: Side.buy,
    quantity: 1.0,
    entryPrice: 1.0830,
    currentPrice: 1.0890,
    unrealizedPnl: 60.0,
    leverage: 30,
    marginUsed: 100.0,
    timestamp: DateTime.now(),
  ),
  Position(
    id: 'pos2',
    symbol: 'XAUUSD',
    instrument: 'Gold / USD',
    side: Side.sell,
    quantity: 0.5,
    entryPrice: 1920.0,
    currentPrice: 1915.0,
    unrealizedPnl: 25.0,
    leverage: 20,
    marginUsed: 150.0,
    timestamp: DateTime.now(),
  ),
];

