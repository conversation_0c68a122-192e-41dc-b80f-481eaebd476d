import 'dart:async';
import 'package:flutter/foundation.dart';
import '../symbol/symbol_service.dart';
import '../symbol/models/symbol_models.dart';
import '../../brokers/services/broker_service.dart';
import '../../services/auth_client.dart';
import 'models_portfolio.dart';

/// Enhanced portfolio service with comprehensive portfolio management
class PortfolioService {
  static final PortfolioService _instance = PortfolioService._();
  factory PortfolioService() => _instance;
  PortfolioService._();

  late final SymbolService _symbolService;
  late final BrokerService _brokerService;
  // ignore: unused_field
  late final AuthClient _authClient;

  final Map<String, Portfolio> _portfolios = {};
  final StreamController<PortfolioEvent> _portfolioEventController =
      StreamController.broadcast();

  /// Initialize portfolio service
  Future<void> initialize({
    required SymbolService symbolService,
    required BrokerService brokerService,
    required AuthClient authClient,
  }) async {
    _symbolService = symbolService;
    _brokerService = brokerService;
    _authClient = authClient;

    await _loadPortfolios();
  }

  /// Create a new portfolio
  Future<PortfolioResult> createPortfolio({
    required String name,
    String? description,
    double? initialCash,
    String? brokerId,
  }) async {
    try {
      final portfolioId = 'portfolio_${DateTime.now().millisecondsSinceEpoch}';

      final portfolio = Portfolio(
        id: portfolioId,
        name: name,
        description: description ?? '',
        brokerId: brokerId,
        cash: initialCash ?? 0.0,
        totalValue: initialCash ?? 0.0,
        dayChange: 0.0,
        dayChangePercent: 0.0,
        totalReturn: 0.0,
        totalReturnPercent: 0.0,
        positions: [],
        transactions: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      _portfolios[portfolioId] = portfolio;
      _emitPortfolioEvent(PortfolioEvent.created(portfolio));

      return PortfolioResult.success(portfolio);
    } catch (e) {
      return PortfolioResult.failure(e.toString());
    }
  }

  /// Get portfolio by ID
  Portfolio? getPortfolio(String portfolioId) {
    return _portfolios[portfolioId];
  }

  /// Get all portfolios
  List<Portfolio> getAllPortfolios() {
    return _portfolios.values.toList();
  }

  /// Update portfolio
  Future<PortfolioResult> updatePortfolio({
    required String portfolioId,
    String? name,
    String? description,
    String? brokerId,
  }) async {
    try {
      final portfolio = _portfolios[portfolioId];
      if (portfolio == null) {
        return PortfolioResult.failure('Portfolio not found');
      }

      final updatedPortfolio = portfolio.copyWith(
        name: name,
        description: description,
        brokerId: brokerId,
        updatedAt: DateTime.now(),
      );

      _portfolios[portfolioId] = updatedPortfolio;
      _emitPortfolioEvent(PortfolioEvent.updated(updatedPortfolio));

      return PortfolioResult.success(updatedPortfolio);
    } catch (e) {
      return PortfolioResult.failure(e.toString());
    }
  }

  /// Delete portfolio
  Future<bool> deletePortfolio(String portfolioId) async {
    try {
      final portfolio = _portfolios.remove(portfolioId);
      if (portfolio != null) {
        _emitPortfolioEvent(PortfolioEvent.deleted(portfolioId));
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('Error deleting portfolio: $e');
      return false;
    }
  }

  /// Add position to portfolio
  Future<PortfolioResult> addPosition({
    required String portfolioId,
    required String symbol,
    required double quantity,
    required double price,
    required TransactionType transactionType,
    String? brokerId,
  }) async {
    try {
      final portfolio = _portfolios[portfolioId];
      if (portfolio == null) {
        return PortfolioResult.failure('Portfolio not found');
      }

      // Create transaction
      final transaction = PortfolioTransaction(
        id: 'tx_${DateTime.now().millisecondsSinceEpoch}',
        portfolioId: portfolioId,
        symbol: symbol,
        type: transactionType,
        quantity: quantity,
        price: price,
        total: quantity * price,
        brokerId: brokerId,
        timestamp: DateTime.now(),
      );

      // Update or create position
      final existingPositionIndex = portfolio.positions.indexWhere(
        (p) => p.symbol == symbol,
      );
      List<PortfolioPosition> updatedPositions = List.from(portfolio.positions);

      if (existingPositionIndex >= 0) {
        // Update existing position
        final existingPosition = updatedPositions[existingPositionIndex];
        final newQuantity =
            transactionType == TransactionType.buy
                ? existingPosition.quantity + quantity
                : existingPosition.quantity - quantity;

        if (newQuantity > 0) {
          // Update position
          final newAveragePrice =
              transactionType == TransactionType.buy
                  ? ((existingPosition.quantity *
                              existingPosition.averagePrice) +
                          (quantity * price)) /
                      newQuantity
                  : existingPosition.averagePrice;

          final newMarketValue = newQuantity * existingPosition.currentPrice;
          final newUnrealizedPnL =
              newMarketValue - (newQuantity * newAveragePrice);
          final newUnrealizedPnLPercent =
              newUnrealizedPnL / (newQuantity * newAveragePrice) * 100;

          updatedPositions[existingPositionIndex] = existingPosition.copyWith(
            quantity: newQuantity,
            averagePrice: newAveragePrice,
            marketValue: newMarketValue,
            unrealizedPnL: newUnrealizedPnL,
            unrealizedPnLPercent: newUnrealizedPnLPercent,
            updatedAt: DateTime.now(),
          );
        } else {
          // Remove position if quantity is 0 or negative
          updatedPositions.removeAt(existingPositionIndex);
        }
      } else if (transactionType == TransactionType.buy) {
        // Create new position
        final newPosition = PortfolioPosition(
          symbol: symbol,
          quantity: quantity,
          averagePrice: price,
          currentPrice: price,
          marketValue: quantity * price,
          unrealizedPnL: 0.0,
          unrealizedPnLPercent: 0.0,
          dayChange: 0.0,
          dayChangePercent: 0.0,
          addedAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        updatedPositions.add(newPosition);
      }

      // Update portfolio cash
      final cashChange =
          transactionType == TransactionType.buy
              ? -transaction.total
              : transaction.total;

      final updatedTransactions = List<PortfolioTransaction>.from(
        portfolio.transactions,
      )..add(transaction);
      final updatedCash = portfolio.cash + cashChange;

      final updatedPortfolio = portfolio.copyWith(
        positions: updatedPositions,
        transactions: updatedTransactions,
        cash: updatedCash,
        updatedAt: DateTime.now(),
      );

      // Recalculate portfolio values
      final recalculatedPortfolio = await _recalculatePortfolioValues(
        updatedPortfolio,
      );
      _portfolios[portfolioId] = recalculatedPortfolio;

      _emitPortfolioEvent(
        PortfolioEvent.positionAdded(recalculatedPortfolio, transaction),
      );

      return PortfolioResult.success(recalculatedPortfolio);
    } catch (e) {
      return PortfolioResult.failure(e.toString());
    }
  }

  /// Sync portfolio with broker
  Future<PortfolioResult> syncWithBroker(String portfolioId) async {
    try {
      final portfolio = _portfolios[portfolioId];
      if (portfolio == null) {
        return PortfolioResult.failure('Portfolio not found');
      }

      if (portfolio.brokerId == null) {
        return PortfolioResult.failure('No broker associated with portfolio');
      }

      // Get broker positions
      final brokerPositions = await _brokerService.getPositions(
        portfolio.brokerId!,
      );
      final brokerAccountInfo = await _brokerService.getAccountInfo(
        portfolio.brokerId!,
      );

      // Convert broker positions to portfolio positions
      final List<PortfolioPosition> syncedPositions = [];
      for (final brokerPosition in brokerPositions) {
        final portfolioPosition = PortfolioPosition(
          symbol: brokerPosition.symbol,
          quantity: brokerPosition.quantity,
          averagePrice: brokerPosition.averagePrice,
          currentPrice: brokerPosition.currentPrice,
          marketValue: brokerPosition.quantity * brokerPosition.currentPrice,
          unrealizedPnL: brokerPosition.unrealizedPnL,
          unrealizedPnLPercent:
              brokerPosition.unrealizedPnL /
              (brokerPosition.quantity * brokerPosition.averagePrice) *
              100,
          dayChange: 0.0, // Would need day change from market data
          dayChangePercent: 0.0,
          addedAt: brokerPosition.openedAt,
          updatedAt: DateTime.now(),
        );
        syncedPositions.add(portfolioPosition);
      }

      final updatedPortfolio = portfolio.copyWith(
        positions: syncedPositions,
        cash: brokerAccountInfo?.availableBalance ?? portfolio.cash,
        updatedAt: DateTime.now(),
      );

      // Recalculate portfolio values
      final recalculatedPortfolio = await _recalculatePortfolioValues(
        updatedPortfolio,
      );
      _portfolios[portfolioId] = recalculatedPortfolio;

      _emitPortfolioEvent(PortfolioEvent.synced(recalculatedPortfolio));

      return PortfolioResult.success(recalculatedPortfolio);
    } catch (e) {
      return PortfolioResult.failure(e.toString());
    }
  }

  /// Update portfolio positions with current market prices
  Future<PortfolioResult> updatePortfolioPrices(String portfolioId) async {
    try {
      final portfolio = _portfolios[portfolioId];
      if (portfolio == null) {
        return PortfolioResult.failure('Portfolio not found');
      }

      if (portfolio.positions.isEmpty) {
        return PortfolioResult.success(portfolio);
      }

      // Get current prices for all symbols
      final symbols = portfolio.positions.map((p) => p.symbol).toList();
      final bulkPrices = await _symbolService.getBulkPrices(symbols);

      // Update positions with current prices
      final updatedPositions =
          portfolio.positions.map((position) {
            final priceData = bulkPrices.prices.firstWhere(
              (p) => p.symbol == position.symbol,
              orElse:
                  () => SymbolPriceData(
                    symbol: position.symbol,
                    currentPrice: position.currentPrice,
                    timestamp: DateTime.now(),
                  ),
            );

            final currentPrice =
                priceData.currentPrice ?? position.currentPrice;
            final marketValue = position.quantity * currentPrice;
            final unrealizedPnL =
                marketValue - (position.quantity * position.averagePrice);
            final unrealizedPnLPercent =
                unrealizedPnL /
                (position.quantity * position.averagePrice) *
                100;

            final dayChange = (priceData.change ?? 0.0) * position.quantity;
            final dayChangePercent = priceData.changePercent ?? 0.0;

            return position.copyWith(
              currentPrice: currentPrice,
              marketValue: marketValue,
              unrealizedPnL: unrealizedPnL,
              unrealizedPnLPercent: unrealizedPnLPercent,
              dayChange: dayChange,
              dayChangePercent: dayChangePercent,
              updatedAt: DateTime.now(),
            );
          }).toList();

      final updatedPortfolio = portfolio.copyWith(
        positions: updatedPositions,
        updatedAt: DateTime.now(),
      );

      // Recalculate portfolio values
      final recalculatedPortfolio = await _recalculatePortfolioValues(
        updatedPortfolio,
      );
      _portfolios[portfolioId] = recalculatedPortfolio;

      _emitPortfolioEvent(PortfolioEvent.pricesUpdated(recalculatedPortfolio));

      return PortfolioResult.success(recalculatedPortfolio);
    } catch (e) {
      return PortfolioResult.failure(e.toString());
    }
  }

  /// Get portfolio performance metrics
  Future<PortfolioPerformance> getPortfolioPerformance(
    String portfolioId,
  ) async {
    final portfolio = _portfolios[portfolioId];
    if (portfolio == null) {
      throw Exception('Portfolio not found');
    }

    return PortfolioPerformance(
      totalValue: portfolio.totalValue,
      totalReturn: portfolio.totalReturn,
      totalReturnPercent: portfolio.totalReturnPercent,
      dayChange: portfolio.dayChange,
      dayChangePercent: portfolio.dayChangePercent,
      cash: portfolio.cash,
      investedAmount: portfolio.totalValue - portfolio.cash,
      positionCount: portfolio.positions.length,
      topPosition:
          portfolio.positions.isNotEmpty
              ? portfolio.positions.reduce(
                (a, b) => a.marketValue > b.marketValue ? a : b,
              )
              : null,
      worstPosition:
          portfolio.positions.isNotEmpty
              ? portfolio.positions.reduce(
                (a, b) => a.unrealizedPnL < b.unrealizedPnL ? a : b,
              )
              : null,
      assetAllocation: _calculateAssetAllocation(portfolio),
      sectorAllocation: await _calculateSectorAllocation(portfolio),
    );
  }

  /// Stream of portfolio events
  Stream<PortfolioEvent> get portfolioEvents =>
      _portfolioEventController.stream;

  /// Recalculate portfolio values
  Future<Portfolio> _recalculatePortfolioValues(Portfolio portfolio) async {
    final totalPositionValue = portfolio.positions.fold<double>(
      0.0,
      (sum, position) => sum + position.marketValue,
    );

    final totalValue = portfolio.cash + totalPositionValue;

    final totalCost = portfolio.positions.fold<double>(
      0.0,
      (sum, position) => sum + (position.quantity * position.averagePrice),
    );

    final totalReturn = totalPositionValue - totalCost;
    final totalReturnPercent =
        totalCost > 0 ? (totalReturn / totalCost) * 100 : 0.0;

    final dayChange = portfolio.positions.fold<double>(
      0.0,
      (sum, position) => sum + position.dayChange,
    );

    final dayChangePercent =
        totalValue > 0 ? (dayChange / totalValue) * 100 : 0.0;

    return portfolio.copyWith(
      totalValue: totalValue,
      totalReturn: totalReturn,
      totalReturnPercent: totalReturnPercent,
      dayChange: dayChange,
      dayChangePercent: dayChangePercent,
      updatedAt: DateTime.now(),
    );
  }

  /// Calculate asset allocation
  Map<String, double> _calculateAssetAllocation(Portfolio portfolio) {
    final Map<String, double> allocation = {};
    final totalValue = portfolio.totalValue;

    if (totalValue <= 0) return allocation;

    // Cash allocation
    if (portfolio.cash > 0) {
      allocation['Cash'] = (portfolio.cash / totalValue) * 100;
    }

    // Stock allocation (simplified - would need more sophisticated categorization)
    final stockValue = portfolio.positions.fold<double>(
      0.0,
      (sum, position) => sum + position.marketValue,
    );

    if (stockValue > 0) {
      allocation['Stocks'] = (stockValue / totalValue) * 100;
    }

    return allocation;
  }

  /// Calculate sector allocation
  Future<Map<String, double>> _calculateSectorAllocation(
    Portfolio portfolio,
  ) async {
    final Map<String, double> allocation = {};
    final totalValue = portfolio.positions.fold<double>(
      0.0,
      (sum, position) => sum + position.marketValue,
    );

    if (totalValue <= 0) return allocation;

    // This would ideally fetch sector information from market service
    // For now, return a simplified allocation
    final Map<String, String> sectorMapping = {
      'AAPL': 'Technology',
      'GOOGL': 'Technology',
      'MSFT': 'Technology',
      'AMZN': 'Consumer Discretionary',
      'TSLA': 'Consumer Discretionary',
      'JPM': 'Financial',
      'BAC': 'Financial',
      'JNJ': 'Healthcare',
      'PFE': 'Healthcare',
    };

    final Map<String, double> sectorValues = {};

    for (final position in portfolio.positions) {
      final sector = sectorMapping[position.symbol] ?? 'Other';
      sectorValues[sector] =
          (sectorValues[sector] ?? 0.0) + position.marketValue;
    }

    sectorValues.forEach((sector, value) {
      allocation[sector] = (value / totalValue) * 100;
    });

    return allocation;
  }

  /// Load portfolios from storage
  Future<void> _loadPortfolios() async {
    // This would load portfolios from persistent storage
    // For now, it's a placeholder
  }

  /// Emit portfolio event
  void _emitPortfolioEvent(PortfolioEvent event) {
    if (!_portfolioEventController.isClosed) {
      _portfolioEventController.add(event);
    }
  }

  /// Dispose resources
  void dispose() {
    _portfolioEventController.close();
    _portfolios.clear();
  }
}
