import 'package:abra/presentation/portifolio/history/history_page.dart';
import 'package:abra/presentation/portifolio/positions/position_page.dart';
import 'package:abra/presentation/portifolio/transactions/transaction_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'orders/order_page.dart';

class PortfolioScreen extends ConsumerStatefulWidget {
  const PortfolioScreen({super.key});

  @override
  ConsumerState<PortfolioScreen> createState() => PortfolioScreenState();
}

class PortfolioScreenState extends ConsumerState<PortfolioScreen> {
  int currentIndex = 0;
  final List<String> items = const [
    'Positions',
    'Orders',
    'History',
    'Transactions',
  ];
  final List<GlobalKey> itemKeys = List.generate(4, (_) => GlobalKey());

  // Keys for accessing child widgets
  final GlobalKey<OrdersTabState> _ordersKey = GlobalKey<OrdersTabState>();
  final GlobalKey<TransactionsTabState> _transactionsKey = GlobalKey<TransactionsTabState>();
  final GlobalKey<HistoryTabState> _historyKey = GlobalKey<HistoryTabState>();
  final GlobalKey<PositionsTabState> _positionKey = GlobalKey<PositionsTabState>();

  void _showFilterDialog() {
  switch (currentIndex) {
    case 0:
      // Handle positions filter
      _positionKey.currentState?.showFilterDialog();
      break;
    case 1:
      // Trigger orders filter dialog
      _ordersKey.currentState?.showFilterDialog();
      break;
    case 2:
      // Handle history filter
      _historyKey.currentState?.showFilterDialog();
      break;
    case 3:
      // Trigger transactions filter dialog
      _transactionsKey.currentState?.showFilterDialog();
      break;
  }
}

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;
    
    return SafeArea(
      child: Scaffold(
        backgroundColor: theme.primary,
        appBar: AppBar(
          backgroundColor: theme.primary,
          elevation: 6,
          shape: Border(bottom: BorderSide(color: theme.onPrimary, width: 1.0)),
          centerTitle: true,
          title: Text(
            'unRealizedPnL',
            style: TextStyle(
              color: theme.onPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          actions: [
            IconButton.outlined(
              onPressed: () {
                _showFilterDialog();
              },
              icon: Icon(Icons.filter_list)
            ),
            const SizedBox(width: 6)
          ],
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(40.0), 
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: buildNavItems(),
            )
          ),
          bottomOpacity: 1.0, // Fixed: Changed from 15.0 to 1.0
        ),
        body: IndexedStack(
          index: currentIndex,
          children: [
            PositionsTab(key: _positionKey),
            OrdersTab(key: _ordersKey),
            HistoryTab(key: _historyKey),
            TransactionsTab(key: _transactionsKey),
          ],
        ),
      ),
    );
  }  List<Widget> buildNavItems() {
    final theme = Theme.of(context).colorScheme;
    return List.generate(items.length, (index) {
      final isSelected = currentIndex == index;

      return GestureDetector(
        onTap: () {
          setState(() {
            currentIndex = index;
          });
        },
        child: SizedBox(
          key: itemKeys[index],
          child: Padding(
            padding: const EdgeInsets.all(1),
            child: Text(
              items[index],
              style: TextStyle(
                color: isSelected ? theme.onPrimary : theme.secondary,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                fontSize: isSelected? 22 : 18,
              ),
            ),
          ),
        ),
      );
    });
  }
}