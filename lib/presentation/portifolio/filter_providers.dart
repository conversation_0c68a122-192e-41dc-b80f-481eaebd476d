import 'package:abra/presentation/portifolio/models_portfolio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Filter state class
class FilterState {
  final DateTimeRange? dateRange;
  final String? symbol;
  final Status? status; // Add status for orders
  final Side? side; // Add side for orders

  const FilterState({
    this.dateRange,
    this.symbol,
    this.status,
    this.side,
  });

  FilterState copyWith({
    DateTimeRange? dateRange,
    String? symbol,
    Status? status,
    Side? side,
  }) {
    return FilterState(
      dateRange: dateRange ?? this.dateRange,
      symbol: symbol ?? this.symbol,
      status: status ?? this.status,
      side: side ?? this.side,
    );
  }
}

// Transaction-specific filter state
class TransactionFilterState {
  final DateTimeRange? dateRange;
  final String? symbol;
  final String? status; // String status for transactions

  const TransactionFilterState({
    this.dateRange,
    this.symbol,
    this.status,
  });

  TransactionFilterState copyWith({
    DateTimeRange? dateRange,
    String? symbol,
    String? status,
  }) {
    return TransactionFilterState(
      dateRange: dateRange ?? this.dateRange,
      symbol: symbol ?? this.symbol,
      status: status ?? this.status,
    );
  }
}

// Filter state notifier
class FilterNotifier extends StateNotifier<FilterState> {
  FilterNotifier() : super(const FilterState());

  void updateDateRange(DateTimeRange? dateRange) {
    state = state.copyWith(dateRange: dateRange);
  }

  void updateSymbol(String? symbol) {
    state = state.copyWith(symbol: symbol);
  }

  void updateStatus(Status? status) {
    state = state.copyWith(status: status);
  }

  void updateSide(Side? side) {
    state = state.copyWith(side: side);
  }

  void clearFilters() {
    state = const FilterState();
  }

  void applyFilters({
    DateTimeRange? dateRange, 
    String? symbol,
    Status? status,
    Side? side,
  }) {
    state = FilterState(
      dateRange: dateRange,
      symbol: symbol,
      status: status,
      side: side,
    );
  }
}

// Transaction filter notifier
class TransactionFilterNotifier extends StateNotifier<TransactionFilterState> {
  TransactionFilterNotifier() : super(const TransactionFilterState());

  void updateDateRange(DateTimeRange? dateRange) {
    state = state.copyWith(dateRange: dateRange);
  }

  void updateSymbol(String? symbol) {
    state = state.copyWith(symbol: symbol);
  }

  void updateStatus(String? status) {
    state = state.copyWith(status: status);
  }

  void clearFilters() {
    state = const TransactionFilterState();
  }

  void applyFilters({
    DateTimeRange? dateRange,
    String? symbol,
    String? status,
  }) {
    state = TransactionFilterState(
      dateRange: dateRange,
      symbol: symbol,
      status: status,
    );
  }
}

// Providers for different tabs
final transactionsFilterProvider = StateNotifierProvider<TransactionFilterNotifier, TransactionFilterState>((ref) {
  return TransactionFilterNotifier();
});

final historyFilterProvider = StateNotifierProvider<FilterNotifier, FilterState>((ref) {
  return FilterNotifier();
});

final ordersFilterProvider = StateNotifierProvider<FilterNotifier, FilterState>((ref) {
  return FilterNotifier();
});

final positionsFilterProvider = StateNotifierProvider<FilterNotifier, FilterState>((ref) {
  return FilterNotifier();
});