import 'package:abra/presentation/portifolio/filter_providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../filter_widget.dart';
import '../models_portfolio.dart';
import 'service_history.dart';

class HistoryTab extends ConsumerStatefulWidget {
  const HistoryTab({super.key});

  @override
  ConsumerState<HistoryTab> createState() => HistoryTabState();
}

class HistoryTabState extends ConsumerState<HistoryTab> {
  final HistoryStreamService _historyStreamService = HistoryStreamService();

  @override
  void initState() {
    super.initState();
    _historyStreamService.start();
  }

  @override
  void dispose() {
    _historyStreamService.stop();
    super.dispose();
  }

  void showFilterDialog() async {
    final String all = 'All';
    final symbols = [all, 'BTCUSD', 'ETHUSD', 'ADAUSD'];
    final sides = [all, Side.buy.name, Side.sell.name];

    final currentFilter = ref.read(historyFilterProvider);

    await showDialog(
      context: context,
      builder: (context) => FilterDialog(
        title: 'Filter History',
        symbols: symbols,
        sides: sides,
        initialDateRange: currentFilter.dateRange,
        initialSymbol: currentFilter.symbol ?? all,
        initialSide: currentFilter.side?.name ?? all,
        onApply: ({dateRange, symbol, status, side}) {
          ref.read(historyFilterProvider.notifier).applyFilters(
            dateRange: dateRange,
            symbol: symbol,
            side: side == all ? null : Side.values.firstWhere((s) => s.name == side),
          );
        },
      ),
    );
  }

  List<History> _applyFilters(List<History> data) {
    final filter = ref.watch(historyFilterProvider);

    return data.where((history) {
      final inSymbol = filter.symbol == null || history.symbol == filter.symbol;
      final inSide = filter.side == null || history.side.name == filter.side?.name;
      final inDateRange = filter.dateRange == null ||
          (history.closedAt.isAfter(filter.dateRange!.start) &&
              history.closedAt.isBefore(filter.dateRange!.end.add(const Duration(days: 1))));

      return inSymbol && inSide && inDateRange;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildHeader(context),
        const Divider(),
        _buildHistoryList(),
      ],
    );
  }

  Widget _buildHeader(BuildContext contenxt) {
    return Container(
      height: 30,
      padding: EdgeInsets.symmetric(horizontal: 10),
      width: MediaQuery.of(context).size.width,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: const [
          Expanded(flex: 2, child: Text('Symbol', style: TextStyle(fontWeight: FontWeight.bold))),
          Expanded(flex: 1, child: Text('Side', style: TextStyle(fontWeight: FontWeight.bold))),
          Expanded(flex: 2, child: Text('Qty', style: TextStyle(fontWeight: FontWeight.bold))),
          Expanded(flex: 3, child: Text('Opened At', style: TextStyle(fontWeight: FontWeight.bold))),
          Expanded(flex: 3, child: Text('Created At', style: TextStyle(fontWeight: FontWeight.bold))),
          Expanded(flex: 2, child: Text('PnL', style: TextStyle(fontWeight: FontWeight.bold))),
        ],
      ),
    );
  }
  Widget _buildHistoryList() {

      return StreamBuilder<List<History>>(
      stream: _historyStreamService.historyStream,
      initialData: const [],
      builder: (context, snapshot) {
        final historyList = snapshot.data ?? [];
        final filteredHistory = _applyFilters(historyList);

        if (filteredHistory.isEmpty) {
          return const Center(child: Text('No trade history matches the current filters'));
        }

        return Expanded(
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 10),
            child:  ListView.builder(
            itemCount: filteredHistory.length,
            itemBuilder: (context, index) {
              final h = filteredHistory[index];
              final sideColor = h.side == Side.buy ? Colors.green : Colors.red;

              return Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      h.symbol.toString(),
                      style: const TextStyle(fontWeight: FontWeight.w400),
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Text(
                      h.side.name,
                      style: TextStyle(color: sideColor),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(h.quantity.toStringAsFixed(2)),
                  ),
                  Expanded(
                    flex: 3,
                    child: Text(
                      h.openedAt.toLocal().toString(),
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 3,
                    child: Text(h.closedAt.toLocal().toString()),
                  ),
                  Expanded(
                    child: Text(
                      h.realizedPnl.toStringAsFixed(2),
                      style: TextStyle(
                        color: h.realizedPnl >= 0 ? Colors.green : Colors.red,
                        fontWeight: FontWeight.bold
                      )
                    )
                   )
                  ],
              );
            }
          ))
        );
      }
    );
  }
}