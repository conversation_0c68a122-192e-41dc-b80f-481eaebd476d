import 'dart:async';
import 'dart:math';
import '../models_portfolio.dart';

List<History> mockHistory = [
  History(
    id: 'h1',
    symbol: 'EURUSD',
    side: Side.buy,
    quantity: 1.0,
    entryPrice: 1.0800,
    exitPrice: 1.0850,
    realizedPnl: 50.0,
    openedAt: DateTime.now().subtract(Duration(hours: 5)),
    closedAt: DateTime.now().subtract(Duration(hours: 3)),
    durationMinutes: 120,
  ),
  History(
    id: 'h2',
    symbol: 'BTCUSD',
    side: Side.sell,
    quantity: 0.3,
    entryPrice: 30000,
    exitPrice: 29500,
    realizedPnl: 150.0,
    openedAt: DateTime.now().subtract(Duration(days: 1, hours: 2)),
    closedAt: DateTime.now().subtract(Duration(days: 1)),
    durationMinutes: 120,
  ),
];

class HistoryStreamService {
  final _controller = StreamController<List<History>>.broadcast();
  final Random _random = Random();
  Timer? _timer;

  final List<History> _history = List.from(mockHistory);

  Stream<List<History>> get historyStream => _controller.stream;

  void start() {
    // Simulate occasional updates every 6 seconds
    _timer = Timer.periodic(Duration(seconds: 6), (_) {
      bool hasChange = false;

      // For demo: randomly add a new history record sometimes
      if (_random.nextInt(5) == 0) {
        final now = DateTime.now();
        final newHistory = History(
          id: 'h${_history.length + 1}',
          symbol: _random.nextBool() ? 'ETHUSD' : 'XAUUSD',
          side: _random.nextBool() ? Side.buy : Side.sell,
          quantity: (_random.nextDouble() * 2) + 0.1,
          entryPrice: (_random.nextDouble() * 1000) + 100,
          exitPrice: (_random.nextDouble() * 1000) + 100,
          realizedPnl: (_random.nextDouble() * 200) - 50,
          openedAt: now.subtract(Duration(minutes: _random.nextInt(500))),
          closedAt: now,
          durationMinutes: _random.nextInt(500),
        );
        _history.add(newHistory);
        hasChange = true;
      }

      if (hasChange) {
        _controller.add(List<History>.from(_history));
      }
    });
  }

  void stop() {
    _timer?.cancel();
    _controller.close();
  }
}