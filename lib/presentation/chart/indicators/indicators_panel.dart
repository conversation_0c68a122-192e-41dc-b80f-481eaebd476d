import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/indicator_provider.dart';
import 'indicator_config_dialog.dart';

/// Dedicated indicators panel that slides from the left side
class IndicatorsPanel extends ConsumerStatefulWidget {
  final StateNotifierProvider<IndicatorNotifier, IndicatorState> indicatorProvider;
  final VoidCallback? onClose;

  const IndicatorsPanel({
    super.key,
    required this.indicatorProvider,
    this.onClose,
  });

  @override
  ConsumerState<IndicatorsPanel> createState() => _IndicatorsPanelState();
}

class _IndicatorsPanelState extends ConsumerState<IndicatorsPanel>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  
  IndicatorCategory? expandedCategory;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(-1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    // Start the slide-in animation
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _closePanel() async {
    await _animationController.reverse();
    if (widget.onClose != null) {
      widget.onClose!();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;
    final indicatorState = ref.watch(widget.indicatorProvider);

    return SlideTransition(
      position: _slideAnimation,
      child: Container(
        width: 320,
        height: double.infinity,
        decoration: BoxDecoration(
          color: theme.surface,
          boxShadow: [
            BoxShadow(
              color: theme.shadow.withValues(alpha: 0.2),
              blurRadius: 16,
              offset: const Offset(4, 0),
            ),
          ],
        ),
        child: Column(
          children: [
            // Header
            _buildHeader(theme),
            
            // Content
            Expanded(
              child: _buildContent(theme, indicatorState),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(ColorScheme theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.primaryContainer,
        border: Border(
          bottom: BorderSide(
            color: theme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.analytics_outlined,
            color: theme.onPrimaryContainer,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Technical Indicators',
              style: TextStyle(
                color: theme.onPrimaryContainer,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          IconButton(
            onPressed: _closePanel,
            icon: Icon(
              Icons.close,
              color: theme.onPrimaryContainer,
              size: 20,
            ),
            tooltip: 'Close panel',
          ),
        ],
      ),
    );
  }

  Widget _buildContent(ColorScheme theme, IndicatorState indicatorState) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Active indicators section
          if (indicatorState.indicators.isNotEmpty) ...[
            _buildActiveIndicatorsSection(theme, indicatorState),
            const SizedBox(height: 24),
          ],

          // Add indicators section
          _buildAddIndicatorsSection(theme),

          // Error display
          if (indicatorState.error != null) ...[
            const SizedBox(height: 16),
            _buildErrorSection(theme, indicatorState.error!),
          ],

          // Loading indicator
          if (indicatorState.isLoading) ...[
            const SizedBox(height: 16),
            _buildLoadingSection(),
          ],
        ],
      ),
    );
  }

  Widget _buildActiveIndicatorsSection(ColorScheme theme, IndicatorState indicatorState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Active Indicators',
              style: TextStyle(
                color: theme.onSurface,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: theme.primaryContainer,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${indicatorState.indicators.length}',
                style: TextStyle(
                  color: theme.onPrimaryContainer,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            const Spacer(),
            if (indicatorState.indicators.isNotEmpty)
              IconButton(
                onPressed: () => _showClearAllDialog(),
                icon: Icon(Icons.clear_all, color: theme.error, size: 18),
                tooltip: 'Clear all indicators',
              ),
          ],
        ),
        const SizedBox(height: 12),
        ...indicatorState.indicators.map((indicator) => 
          _buildActiveIndicatorCard(theme, indicator)
        ),
      ],
    );
  }

  Widget _buildActiveIndicatorCard(ColorScheme theme, IndicatorConfig indicator) {
    final displayInfo = _getIndicatorDisplayInfo(indicator.type);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: indicator.color.withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  color: indicator.color,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  indicator.name,
                  style: TextStyle(
                    color: theme.onSurface,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              IconButton(
                onPressed: () => ref.read(widget.indicatorProvider.notifier)
                    .toggleIndicatorVisibility(indicator.id),
                icon: Icon(
                  indicator.isVisible ? Icons.visibility : Icons.visibility_off,
                  color: theme.onSurface.withValues(alpha: 0.6),
                  size: 18,
                ),
                tooltip: indicator.isVisible ? 'Hide' : 'Show',
              ),
              IconButton(
                onPressed: () => ref.read(widget.indicatorProvider.notifier)
                    .removeIndicator(indicator.id),
                icon: Icon(Icons.delete_outline, color: theme.error, size: 18),
                tooltip: 'Remove',
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            displayInfo.description,
            style: TextStyle(
              color: theme.onSurface.withValues(alpha: 0.7),
              fontSize: 11,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddIndicatorsSection(ColorScheme theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Add Indicators',
          style: TextStyle(
            color: theme.onSurface,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        ...IndicatorCategory.values.map((category) => 
          _buildCategorySection(theme, category)
        ),
      ],
    );
  }

  Widget _buildCategorySection(ColorScheme theme, IndicatorCategory category) {
    final indicators = _getIndicatorsForCategory(category);
    if (indicators.isEmpty) return const SizedBox.shrink();
    
    final isExpanded = expandedCategory == category;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: theme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () => setState(() {
              expandedCategory = isExpanded ? null : category;
            }),
            borderRadius: BorderRadius.circular(12),
            child: Container(
              padding: const EdgeInsets.all(12),
              child: Row(
                children: [
                  Icon(
                    _getCategoryIcon(category),
                    color: theme.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _getCategoryName(category),
                      style: TextStyle(
                        color: theme.onSurface,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Icon(
                    isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: theme.onSurface.withValues(alpha: 0.6),
                  ),
                ],
              ),
            ),
          ),
          if (isExpanded) ...[
            const Divider(height: 1),
            ...indicators.map((indicatorType) => 
              _buildIndicatorItem(theme, indicatorType)
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildIndicatorItem(ColorScheme theme, IndicatorType indicatorType) {
    final displayInfo = _getIndicatorDisplayInfo(indicatorType);
    
    return InkWell(
      onTap: () => _addIndicatorWithConfig(indicatorType),
      child: Container(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            Icon(
              displayInfo.icon,
              color: theme.onSurface.withValues(alpha: 0.7),
              size: 18,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    displayInfo.name,
                    style: TextStyle(
                      color: theme.onSurface,
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    displayInfo.description,
                    style: TextStyle(
                      color: theme.onSurface.withValues(alpha: 0.6),
                      fontSize: 11,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.add_circle_outline,
              color: theme.primary,
              size: 18,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorSection(ColorScheme theme, String error) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.errorContainer,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: theme.error, size: 18),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              error,
              style: TextStyle(
                color: theme.onErrorContainer,
                fontSize: 12,
              ),
            ),
          ),
          IconButton(
            onPressed: () => ref.read(widget.indicatorProvider.notifier).clearError(),
            icon: Icon(Icons.close, color: theme.error, size: 16),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingSection() {
    return const Center(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: CircularProgressIndicator(),
      ),
    );
  }

  void _addIndicatorWithConfig(IndicatorType type) async {
    final parameters = await showIndicatorConfigDialog(context, type);
    
    if (parameters != null) {
      final success = await ref.read(widget.indicatorProvider.notifier)
          .addIndicator(type, customParameters: parameters);
      
      if (success) {
        setState(() => expandedCategory = null);
      }
    }
  }

  void _showClearAllDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Indicators'),
        content: const Text('Remove all indicators from the chart?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              ref.read(widget.indicatorProvider.notifier).clearAllIndicators();
              Navigator.of(context).pop();
            },
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }

  // Helper methods for indicator categories and display info
  List<IndicatorType> _getIndicatorsForCategory(IndicatorCategory category) {
    switch (category) {
      case IndicatorCategory.movingAverages:
        return [IndicatorType.sma, IndicatorType.ema];
      case IndicatorCategory.momentum:
        return [IndicatorType.rsi, IndicatorType.macd];
      case IndicatorCategory.volatility:
        return [IndicatorType.bollingerBands, IndicatorType.atr];
      case IndicatorCategory.volume:
        return []; // Future volume indicators
    }
  }

  IconData _getCategoryIcon(IndicatorCategory category) {
    switch (category) {
      case IndicatorCategory.movingAverages:
        return Icons.trending_up;
      case IndicatorCategory.momentum:
        return Icons.speed;
      case IndicatorCategory.volatility:
        return Icons.bar_chart;
      case IndicatorCategory.volume:
        return Icons.volume_up;
    }
  }

  String _getCategoryName(IndicatorCategory category) {
    switch (category) {
      case IndicatorCategory.movingAverages:
        return 'Moving Averages';
      case IndicatorCategory.momentum:
        return 'Momentum';
      case IndicatorCategory.volatility:
        return 'Volatility';
      case IndicatorCategory.volume:
        return 'Volume';
    }
  }

  IndicatorDisplayInfo _getIndicatorDisplayInfo(IndicatorType type) {
    const displayInfo = {
      IndicatorType.sma: IndicatorDisplayInfo(
        name: 'Simple Moving Average',
        shortName: 'SMA',
        icon: Icons.trending_up,
        description: 'Average price over specified period',
      ),
      IndicatorType.ema: IndicatorDisplayInfo(
        name: 'Exponential Moving Average',
        shortName: 'EMA',
        icon: Icons.show_chart,
        description: 'Weighted average favoring recent prices',
      ),
      IndicatorType.rsi: IndicatorDisplayInfo(
        name: 'Relative Strength Index',
        shortName: 'RSI',
        icon: Icons.speed,
        description: 'Momentum oscillator (0-100)',
      ),
      IndicatorType.macd: IndicatorDisplayInfo(
        name: 'MACD',
        shortName: 'MACD',
        icon: Icons.timeline,
        description: 'Trend-following momentum indicator',
      ),
      IndicatorType.bollingerBands: IndicatorDisplayInfo(
        name: 'Bollinger Bands',
        shortName: 'BB',
        icon: Icons.linear_scale,
        description: 'Volatility bands around moving average',
      ),
      IndicatorType.atr: IndicatorDisplayInfo(
        name: 'Average True Range',
        shortName: 'ATR',
        icon: Icons.bar_chart,
        description: 'Volatility measurement indicator',
      ),
    };
    return displayInfo[type]!;
  }
}

// Indicator categories enum
enum IndicatorCategory {
  movingAverages,
  momentum,
  volatility,
  volume,
}

// Indicator display info class
class IndicatorDisplayInfo {
  final String name;
  final String shortName;
  final IconData icon;
  final String description;

  const IndicatorDisplayInfo({
    required this.name,
    required this.shortName,
    required this.icon,
    required this.description,
  });
}
