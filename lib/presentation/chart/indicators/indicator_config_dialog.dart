import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../providers/indicator_provider.dart';

/// Dialog for configuring indicator parameters before adding
class IndicatorConfigDialog extends StatefulWidget {
  final IndicatorType indicatorType;
  final Map<String, dynamic>? initialParameters;

  const IndicatorConfigDialog({
    super.key,
    required this.indicatorType,
    this.initialParameters,
  });

  @override
  State<IndicatorConfigDialog> createState() => _IndicatorConfigDialogState();
}

class _IndicatorConfigDialogState extends State<IndicatorConfigDialog> {
  late Map<String, dynamic> parameters;
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    parameters = Map<String, dynamic>.from(widget.initialParameters ?? _getDefaultParameters());
  }

  Map<String, dynamic> _getDefaultParameters() {
    switch (widget.indicatorType) {
      case IndicatorType.sma:
        return {'period': 20};
      case IndicatorType.ema:
        return {'period': 20};
      case IndicatorType.rsi:
        return {'period': 14};
      case IndicatorType.macd:
        return {'fastPeriod': 12, 'slowPeriod': 26, 'signalPeriod': 9};
      case IndicatorType.bollingerBands:
        return {'period': 20, 'stdDevMultiplier': 2.0};
      case IndicatorType.atr:
        return {'period': 14, 'useWildersSmoothing': true};
    }
  }

  String _getIndicatorName() {
    switch (widget.indicatorType) {
      case IndicatorType.sma:
        return 'Simple Moving Average';
      case IndicatorType.ema:
        return 'Exponential Moving Average';
      case IndicatorType.rsi:
        return 'Relative Strength Index';
      case IndicatorType.macd:
        return 'MACD';
      case IndicatorType.bollingerBands:
        return 'Bollinger Bands';
      case IndicatorType.atr:
        return 'Average True Range';
    }
  }

  IconData _getIndicatorIcon() {
    switch (widget.indicatorType) {
      case IndicatorType.sma:
        return Icons.trending_up;
      case IndicatorType.ema:
        return Icons.show_chart;
      case IndicatorType.rsi:
        return Icons.speed;
      case IndicatorType.macd:
        return Icons.timeline;
      case IndicatorType.bollingerBands:
        return Icons.linear_scale;
      case IndicatorType.atr:
        return Icons.bar_chart;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;

    return AlertDialog(
      title: Row(
        children: [
          Icon(_getIndicatorIcon(), color: theme.primary),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Configure ${_getIndicatorName()}',
              style: const TextStyle(fontSize: 18),
            ),
          ),
        ],
      ),
      content: SizedBox(
        width: 300,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildParameterFields(),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _onSave,
          child: const Text('Add Indicator'),
        ),
      ],
    );
  }

  Widget _buildParameterFields() {
    switch (widget.indicatorType) {
      case IndicatorType.sma:
      case IndicatorType.ema:
        return _buildPeriodField();
      case IndicatorType.rsi:
        return _buildPeriodField();
      case IndicatorType.macd:
        return _buildMACDFields();
      case IndicatorType.bollingerBands:
        return _buildBollingerBandsFields();
      case IndicatorType.atr:
        return _buildATRFields();
    }
  }

  Widget _buildPeriodField() {
    return TextFormField(
      initialValue: parameters['period'].toString(),
      decoration: const InputDecoration(
        labelText: 'Period',
        helperText: 'Number of periods for calculation',
        border: OutlineInputBorder(),
      ),
      keyboardType: TextInputType.number,
      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Please enter a period';
        }
        final period = int.tryParse(value);
        if (period == null || period < 1 || period > 200) {
          return 'Period must be between 1 and 200';
        }
        return null;
      },
      onSaved: (value) {
        parameters['period'] = int.parse(value!);
      },
    );
  }

  Widget _buildMACDFields() {
    return Column(
      children: [
        TextFormField(
          initialValue: parameters['fastPeriod'].toString(),
          decoration: const InputDecoration(
            labelText: 'Fast Period',
            helperText: 'Fast EMA period (typically 12)',
            border: OutlineInputBorder(),
          ),
          keyboardType: TextInputType.number,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          validator: (value) {
            if (value == null || value.isEmpty) return 'Required';
            final period = int.tryParse(value);
            if (period == null || period < 1 || period > 50) {
              return 'Must be between 1 and 50';
            }
            return null;
          },
          onSaved: (value) => parameters['fastPeriod'] = int.parse(value!),
        ),
        const SizedBox(height: 16),
        TextFormField(
          initialValue: parameters['slowPeriod'].toString(),
          decoration: const InputDecoration(
            labelText: 'Slow Period',
            helperText: 'Slow EMA period (typically 26)',
            border: OutlineInputBorder(),
          ),
          keyboardType: TextInputType.number,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          validator: (value) {
            if (value == null || value.isEmpty) return 'Required';
            final period = int.tryParse(value);
            if (period == null || period < 1 || period > 100) {
              return 'Must be between 1 and 100';
            }
            return null;
          },
          onSaved: (value) => parameters['slowPeriod'] = int.parse(value!),
        ),
        const SizedBox(height: 16),
        TextFormField(
          initialValue: parameters['signalPeriod'].toString(),
          decoration: const InputDecoration(
            labelText: 'Signal Period',
            helperText: 'Signal line EMA period (typically 9)',
            border: OutlineInputBorder(),
          ),
          keyboardType: TextInputType.number,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          validator: (value) {
            if (value == null || value.isEmpty) return 'Required';
            final period = int.tryParse(value);
            if (period == null || period < 1 || period > 50) {
              return 'Must be between 1 and 50';
            }
            return null;
          },
          onSaved: (value) => parameters['signalPeriod'] = int.parse(value!),
        ),
      ],
    );
  }

  Widget _buildBollingerBandsFields() {
    return Column(
      children: [
        TextFormField(
          initialValue: parameters['period'].toString(),
          decoration: const InputDecoration(
            labelText: 'Period',
            helperText: 'Moving average period (typically 20)',
            border: OutlineInputBorder(),
          ),
          keyboardType: TextInputType.number,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          validator: (value) {
            if (value == null || value.isEmpty) return 'Required';
            final period = int.tryParse(value);
            if (period == null || period < 1 || period > 200) {
              return 'Must be between 1 and 200';
            }
            return null;
          },
          onSaved: (value) => parameters['period'] = int.parse(value!),
        ),
        const SizedBox(height: 16),
        TextFormField(
          initialValue: parameters['stdDevMultiplier'].toString(),
          decoration: const InputDecoration(
            labelText: 'Standard Deviation Multiplier',
            helperText: 'Band width multiplier (typically 2.0)',
            border: OutlineInputBorder(),
          ),
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          validator: (value) {
            if (value == null || value.isEmpty) return 'Required';
            final multiplier = double.tryParse(value);
            if (multiplier == null || multiplier < 0.1 || multiplier > 5.0) {
              return 'Must be between 0.1 and 5.0';
            }
            return null;
          },
          onSaved: (value) => parameters['stdDevMultiplier'] = double.parse(value!),
        ),
      ],
    );
  }

  Widget _buildATRFields() {
    return Column(
      children: [
        TextFormField(
          initialValue: parameters['period'].toString(),
          decoration: const InputDecoration(
            labelText: 'Period',
            helperText: 'Number of periods for ATR calculation',
            border: OutlineInputBorder(),
          ),
          keyboardType: TextInputType.number,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          validator: (value) {
            if (value == null || value.isEmpty) return 'Required';
            final period = int.tryParse(value);
            if (period == null || period < 1 || period > 200) {
              return 'Must be between 1 and 200';
            }
            return null;
          },
          onSaved: (value) => parameters['period'] = int.parse(value!),
        ),
        const SizedBox(height: 16),
        SwitchListTile(
          title: const Text('Use Wilder\'s Smoothing'),
          subtitle: const Text('Traditional ATR calculation method'),
          value: parameters['useWildersSmoothing'] as bool,
          onChanged: (value) {
            setState(() {
              parameters['useWildersSmoothing'] = value;
            });
          },
        ),
      ],
    );
  }

  void _onSave() {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();
      Navigator.of(context).pop(parameters);
    }
  }
}

/// Show indicator configuration dialog
Future<Map<String, dynamic>?> showIndicatorConfigDialog(
  BuildContext context,
  IndicatorType indicatorType, {
  Map<String, dynamic>? initialParameters,
}) {
  return showDialog<Map<String, dynamic>>(
    context: context,
    builder: (context) => IndicatorConfigDialog(
      indicatorType: indicatorType,
      initialParameters: initialParameters,
    ),
  );
}
