import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/indicator_provider.dart';
import 'indicator_config_dialog.dart';

// Indicator categories for organization
enum IndicatorCategory { movingAverages, momentum, volatility, volume }

// Mapping of indicators to categories
const Map<IndicatorCategory, List<IndicatorType>> indicatorCategories = {
  IndicatorCategory.movingAverages: [IndicatorType.sma, IndicatorType.ema],
  IndicatorCategory.momentum: [IndicatorType.rsi, IndicatorType.macd],
  IndicatorCategory.volatility: [
    IndicatorType.bollingerBands,
    IndicatorType.atr,
  ],
  IndicatorCategory.volume: [], // Future volume indicators
};

// Indicator display information
const Map<IndicatorType, IndicatorDisplayInfo> indicatorDisplayInfo = {
  IndicatorType.sma: IndicatorDisplayInfo(
    name: 'Simple Moving Average',
    shortName: 'SMA',
    icon: Icons.trending_up,
    description: 'Average price over a specified period',
  ),
  IndicatorType.ema: IndicatorDisplayInfo(
    name: 'Exponential Moving Average',
    shortName: 'EMA',
    icon: Icons.show_chart,
    description: 'Weighted average giving more importance to recent prices',
  ),
  IndicatorType.rsi: IndicatorDisplayInfo(
    name: 'Relative Strength Index',
    shortName: 'RSI',
    icon: Icons.speed,
    description:
        'Momentum oscillator measuring speed and change of price movements',
  ),
  IndicatorType.macd: IndicatorDisplayInfo(
    name: 'MACD',
    shortName: 'MACD',
    icon: Icons.timeline,
    description:
        'Moving Average Convergence Divergence trend-following indicator',
  ),
  IndicatorType.bollingerBands: IndicatorDisplayInfo(
    name: 'Bollinger Bands',
    shortName: 'BB',
    icon: Icons.linear_scale,
    description: 'Volatility bands around a moving average',
  ),
  IndicatorType.atr: IndicatorDisplayInfo(
    name: 'Average True Range',
    shortName: 'ATR',
    icon: Icons.bar_chart,
    description: 'Volatility indicator measuring market volatility',
  ),
};

class IndicatorDisplayInfo {
  final String name;
  final String shortName;
  final IconData icon;
  final String description;

  const IndicatorDisplayInfo({
    required this.name,
    required this.shortName,
    required this.icon,
    required this.description,
  });
}

// Main indicators controller widget
class IndicatorsController extends ConsumerStatefulWidget {
  final StateNotifierProvider<IndicatorNotifier, IndicatorState>
  indicatorProvider;

  const IndicatorsController({super.key, required this.indicatorProvider});

  @override
  ConsumerState<IndicatorsController> createState() =>
      _IndicatorsControllerState();
}

class _IndicatorsControllerState extends ConsumerState<IndicatorsController> {
  IndicatorCategory? expandedCategory;
  bool isAnimating = false;

  void onCategoryTap(IndicatorCategory category) async {
    if (isAnimating) return;

    if (expandedCategory == null) {
      setState(() => expandedCategory = category);
    } else if (expandedCategory == category) {
      setState(() => expandedCategory = null);
    } else {
      isAnimating = true;
      setState(() => expandedCategory = null);
      await Future.delayed(const Duration(milliseconds: 200));
      if (mounted) {
        setState(() {
          expandedCategory = category;
          isAnimating = false;
        });
      }
    }
  }

  IconData getCategoryIcon(IndicatorCategory category) {
    switch (category) {
      case IndicatorCategory.movingAverages:
        return Icons.trending_up;
      case IndicatorCategory.momentum:
        return Icons.speed;
      case IndicatorCategory.volatility:
        return Icons.bar_chart;
      case IndicatorCategory.volume:
        return Icons.volume_up;
    }
  }

  String getCategoryName(IndicatorCategory category) {
    switch (category) {
      case IndicatorCategory.movingAverages:
        return 'Moving Averages';
      case IndicatorCategory.momentum:
        return 'Momentum';
      case IndicatorCategory.volatility:
        return 'Volatility';
      case IndicatorCategory.volume:
        return 'Volume';
    }
  }

  @override
  Widget build(BuildContext context) {
    final indicatorState = ref.watch(widget.indicatorProvider);
    final theme = Theme.of(context).colorScheme;

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: theme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: theme.shadow,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(Icons.analytics_outlined, color: theme.primary, size: 20),
              const SizedBox(width: 8),
              Text(
                'Technical Indicators',
                style: TextStyle(
                  color: theme.onSurface,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              if (indicatorState.indicators.isNotEmpty)
                IconButton(
                  icon: Icon(Icons.clear_all, color: theme.error, size: 18),
                  onPressed: () => _showClearAllDialog(context),
                  tooltip: 'Clear all indicators',
                ),
            ],
          ),

          const SizedBox(height: 8),

          // Active indicators list
          if (indicatorState.indicators.isNotEmpty) ...[
            Text(
              'Active Indicators (${indicatorState.indicators.length})',
              style: TextStyle(
                color: theme.onSurface,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            ...indicatorState.indicators.map(
              (indicator) =>
                  _buildActiveIndicatorTile(context, indicator, theme),
            ),
            const SizedBox(height: 12),
            Divider(color: theme.outline),
            const SizedBox(height: 8),
          ],

          // Category buttons
          Text(
            'Add Indicators',
            style: TextStyle(
              color: theme.onSurface,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),

          Wrap(
            spacing: 8,
            runSpacing: 8,
            children:
                IndicatorCategory.values.map((category) {
                  final indicators = indicatorCategories[category] ?? [];
                  if (indicators.isEmpty) return const SizedBox.shrink();

                  return _buildCategoryButton(context, category, theme);
                }).toList(),
          ),

          // Expanded indicator list
          if (expandedCategory != null) ...[
            const SizedBox(height: 12),
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 200),
              child: _buildIndicatorList(context, expandedCategory!, theme),
            ),
          ],

          // Error display
          if (indicatorState.error != null) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: theme.errorContainer,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.error_outline, color: theme.error, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      indicatorState.error!,
                      style: TextStyle(
                        color: theme.onErrorContainer,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: Icon(Icons.close, color: theme.error, size: 16),
                    onPressed:
                        () =>
                            ref
                                .read(widget.indicatorProvider.notifier)
                                .clearError(),
                  ),
                ],
              ),
            ),
          ],

          // Loading indicator
          if (indicatorState.isLoading) ...[
            const SizedBox(height: 8),
            const Center(
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActiveIndicatorTile(
    BuildContext context,
    IndicatorConfig indicator,
    ColorScheme theme,
  ) {
    // ignore: unused_local_variable
    final displayInfo = indicatorDisplayInfo[indicator.type]!;

    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: theme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: indicator.color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              indicator.name,
              style: TextStyle(
                color: theme.onSurface,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          IconButton(
            icon: Icon(
              indicator.isVisible ? Icons.visibility : Icons.visibility_off,
              color: theme.onSurface,
              size: 16,
            ),
            onPressed:
                () => ref
                    .read(widget.indicatorProvider.notifier)
                    .toggleIndicatorVisibility(indicator.id),
            tooltip: indicator.isVisible ? 'Hide indicator' : 'Show indicator',
          ),
          IconButton(
            icon: Icon(Icons.delete_outline, color: theme.error, size: 16),
            onPressed:
                () => ref
                    .read(widget.indicatorProvider.notifier)
                    .removeIndicator(indicator.id),
            tooltip: 'Remove indicator',
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryButton(
    BuildContext context,
    IndicatorCategory category,
    ColorScheme theme,
  ) {
    final isExpanded = expandedCategory == category;

    return Material(
      color:
          isExpanded ? theme.primaryContainer : theme.surfaceContainerHighest,
      borderRadius: BorderRadius.circular(8),
      child: InkWell(
        onTap: () => onCategoryTap(category),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                getCategoryIcon(category),
                color:
                    isExpanded
                        ? theme.onPrimaryContainer
                        : theme.onSurfaceVariant,
                size: 16,
              ),
              const SizedBox(width: 6),
              Text(
                getCategoryName(category),
                style: TextStyle(
                  color:
                      isExpanded
                          ? theme.onPrimaryContainer
                          : theme.onSurfaceVariant,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildIndicatorList(
    BuildContext context,
    IndicatorCategory category,
    ColorScheme theme,
  ) {
    final indicators = indicatorCategories[category] ?? [];

    return Container(
      key: ValueKey(category),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: theme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children:
            indicators.map((indicatorType) {
              final displayInfo = indicatorDisplayInfo[indicatorType]!;

              return ListTile(
                dense: true,
                leading: Icon(displayInfo.icon, color: theme.primary, size: 20),
                title: Text(
                  displayInfo.name,
                  style: TextStyle(
                    color: theme.onSurface,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                subtitle: Text(
                  displayInfo.description,
                  style: TextStyle(color: theme.onSurface, fontSize: 11),
                ),
                trailing: IconButton(
                  icon: Icon(
                    Icons.add_circle_outline,
                    color: theme.primary,
                    size: 20,
                  ),
                  onPressed: () => _addIndicatorWithConfig(indicatorType),
                ),
                onTap: () => _addIndicatorWithConfig(indicatorType),
              );
            }).toList(),
      ),
    );
  }

  void _addIndicatorWithConfig(IndicatorType type) async {
    // Show configuration dialog
    final parameters = await showIndicatorConfigDialog(context, type);

    if (parameters != null) {
      // Add indicator with custom parameters
      final success = await ref
          .read(widget.indicatorProvider.notifier)
          .addIndicator(type, customParameters: parameters);

      if (success) {
        setState(() => expandedCategory = null);
      }
    }
  }

  void _showClearAllDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Clear All Indicators'),
            content: const Text(
              'Are you sure you want to remove all indicators from the chart?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  ref
                      .read(widget.indicatorProvider.notifier)
                      .clearAllIndicators();
                  Navigator.of(context).pop();
                },
                child: const Text('Clear All'),
              ),
            ],
          ),
    );
  }
}
