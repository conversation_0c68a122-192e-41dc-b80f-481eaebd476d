import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../chart_utilities/symbol_selector.dart';
import '../chart_utilities/tf_selector.dart';
import '../indicators/indicators_controller.dart';
import '../providers/timeframe_provider.dart';
import '../providers/indicator_provider.dart';
import '../../../ffi/chart_engine_wrapper.dart';

/// Enhanced chart toolbar with indicators controller
class ChartToolbarWidget extends ConsumerWidget {
  final bool isVisible;
  final ChartEngineWrapper? chartEngine;

  const ChartToolbarWidget({
    super.key,
    required this.isVisible,
    this.chartEngine,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context).colorScheme;

    return AnimatedSize(
      duration: const Duration(milliseconds: 250),
      curve: Curves.easeInOut,
      child:
          isVisible
              ? Container(
                constraints: const BoxConstraints(maxHeight: 400),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: theme.surface,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: theme.shadow,
                      blurRadius: 8,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Top controls row
                      Container(
                        height: 40,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        decoration: BoxDecoration(
                          color: theme.surfaceContainerHighest,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.tune, color: theme.primary, size: 20),
                            const SizedBox(width: 8),
                            Text(
                              'Chart Controls',
                              style: TextStyle(
                                color: theme.onSurface,
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(width: 16),
                            SymbolSelector(),
                            const SizedBox(width: 12),
                            TimeframeSelector(
                              onTimeframeSelected: (label) {
                                ref
                                    .read(selectedTimeframeProvider.notifier)
                                    .updateTimeframeByLabel(label);
                              },
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 12),

                      // Indicators controller
                      if (chartEngine != null)
                        ProviderScope(
                          overrides: [
                            indicatorProvider.overrideWith(
                              (ref) => IndicatorNotifier(chartEngine!),
                            ),
                          ],
                          child: IndicatorsController(
                            indicatorProvider: indicatorProvider,
                          ),
                        )
                      else
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: theme.errorContainer,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.warning_amber,
                                color: theme.error,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  'Chart engine not available. Indicators disabled.',
                                  style: TextStyle(
                                    color: theme.onErrorContainer,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              )
              : const SizedBox.shrink(),
    );
  }
}
