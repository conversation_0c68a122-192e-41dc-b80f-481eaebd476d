import 'package:flutter/material.dart';

/// Error boundary widget for better error handling
class ErrorBoundary extends StatelessWidget {
  final Widget child;
  final Function(Object error, StackTrace stackTrace)? onError;

  const ErrorBoundary({super.key, required this.child, this.onError});

  @override
  Widget build(BuildContext context) {
    return Builder(
      builder: (context) {
        try {
          return child;
        } catch (error, stackTrace) {
          onError?.call(error, stackTrace);
          return _ErrorWidget(error: error);
        }
      },
    );
  }
}

/// Error display widget
class _ErrorWidget extends StatelessWidget {
  final Object error;

  const _ErrorWidget({required this.error});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;

    return Container(
      color: theme.primary,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48, color: theme.error),
            const SizedBox(height: 16),
            Text(
              'Chart Error',
              style: TextStyle(
                color: theme.onPrimary,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Failed to load chart data',
              style: TextStyle(
                color: theme.onPrimary.withValues(alpha: 0.7),
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                // Trigger a rebuild by changing the key or refreshing data
                debugPrint('Retry chart loading');
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }
}
