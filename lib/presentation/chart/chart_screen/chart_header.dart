import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../chart_utilities/header_chart.dart';

/// Separate widget for chart header to minimize rebuilds
class ChartHeaderWidget extends ConsumerWidget {
  final bool isTradingButtonsVisible;
  final VoidCallback? onIndicatorsPressed;

  const ChartHeaderWidget({
    super.key,
    required this.isTradingButtonsVisible,
    this.onIndicatorsPressed,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ChartHeader(
      isTradingButtonsVisible: isTradingButtonsVisible,
      onIndicatorsPressed: onIndicatorsPressed,
    );
  }
}
