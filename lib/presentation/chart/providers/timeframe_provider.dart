import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../chart_utilities/tf_selector.dart';

/// Provider for managing the selected timeframe across the chart screen
final selectedTimeframeProvider = StateNotifierProvider<TimeframeNotifier, Timeframe>((ref) {
  return TimeframeNotifier();
});

class TimeframeNotifier extends StateNotifier<Timeframe> {
  TimeframeNotifier() : super(Timeframe.m15); // Default to 15 minutes

  /// Update the selected timeframe
  void updateTimeframe(Timeframe newTimeframe) {
    state = newTimeframe;
  }

  /// Update timeframe by label string
  void updateTimeframeByLabel(String label) {
    final timeframe = Timeframe.values.firstWhere(
      (tf) => tf.label == label,
      orElse: () => Timeframe.m15,
    );
    state = timeframe;
  }

  /// Get the current timeframe label
  String get currentLabel => state.label;

  /// Get the current timeframe duration
  Duration get currentDuration => state.duration;
}

/// Provider for getting available timeframes
final availableTimeframesProvider = Provider<List<Timeframe>>((ref) {
  return Timeframe.values;
});

/// Provider for getting timeframe labels
final timeframeLabelsProvider = Provider<List<String>>((ref) {
  return Timeframe.values.map((tf) => tf.label).toList();
});
