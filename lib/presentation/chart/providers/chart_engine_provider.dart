import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../ffi/chart_engine_wrapper.dart';

/// Provider for the chart engine instance
/// This ensures the same chart engine is shared between chart and toolbar
final chartEngineProvider = Provider<ChartEngineWrapper>((ref) {
  final chartEngine = ChartEngineWrapper();
  
  // Dispose the chart engine when the provider is disposed
  ref.onDispose(() {
    chartEngine.dispose();
  });
  
  return chartEngine;
});

/// Provider for chart engine initialization state
final chartEngineStateProvider = Provider<ChartEngineState>((ref) {
  final chartEngine = ref.watch(chartEngineProvider);
  
  return ChartEngineState(
    isInitialized: chartEngine.isInitialized,
    error: chartEngine.initError,
  );
});

/// Chart engine state class
class ChartEngineState {
  final bool isInitialized;
  final String? error;

  const ChartEngineState({
    required this.isInitialized,
    this.error,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChartEngineState &&
        other.isInitialized == isInitialized &&
        other.error == error;
  }

  @override
  int get hashCode => isInitialized.hashCode ^ error.hashCode;

  @override
  String toString() {
    return 'ChartEngineState(isInitialized: $isInitialized, error: $error)';
  }
}
