import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../ffi/chart_engine_wrapper.dart';

// Indicator types enum
enum IndicatorType { sma, ema, rsi, macd, bollingerBands, atr }

// Indicator configuration class
class IndicatorConfig {
  final int id;
  final IndicatorType type;
  final String name;
  final Map<String, dynamic> parameters;
  final Color color;
  final bool isVisible;
  final DateTime createdAt;

  const IndicatorConfig({
    required this.id,
    required this.type,
    required this.name,
    required this.parameters,
    required this.color,
    this.isVisible = true,
    required this.createdAt,
  });

  IndicatorConfig copyWith({
    int? id,
    IndicatorType? type,
    String? name,
    Map<String, dynamic>? parameters,
    Color? color,
    bool? isVisible,
    DateTime? createdAt,
  }) {
    return IndicatorConfig(
      id: id ?? this.id,
      type: type ?? this.type,
      name: name ?? this.name,
      parameters: parameters ?? this.parameters,
      color: color ?? this.color,
      isVisible: isVisible ?? this.isVisible,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is IndicatorConfig && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'IndicatorConfig(id: $id, type: $type, name: $name, isVisible: $isVisible)';
  }
}

// Indicator state class
class IndicatorState {
  final List<IndicatorConfig> indicators;
  final bool isLoading;
  final String? error;

  const IndicatorState({
    this.indicators = const [],
    this.isLoading = false,
    this.error,
  });

  IndicatorState copyWith({
    List<IndicatorConfig>? indicators,
    bool? isLoading,
    String? error,
  }) {
    return IndicatorState(
      indicators: indicators ?? this.indicators,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  @override
  String toString() {
    return 'IndicatorState(indicators: ${indicators.length}, isLoading: $isLoading, error: $error)';
  }
}

// Indicator notifier class
class IndicatorNotifier extends StateNotifier<IndicatorState> {
  final ChartEngineWrapper _chartEngine;
  // ignore: unused_field
  final int _nextId = 1;

  IndicatorNotifier(this._chartEngine) : super(const IndicatorState());

  // Default indicator configurations
  static const Map<IndicatorType, Map<String, dynamic>> _defaultConfigs = {
    IndicatorType.sma: {'period': 20},
    IndicatorType.ema: {'period': 20},
    IndicatorType.rsi: {'period': 14},
    IndicatorType.macd: {'fastPeriod': 12, 'slowPeriod': 26, 'signalPeriod': 9},
    IndicatorType.bollingerBands: {'period': 20, 'stdDevMultiplier': 2.0},
    IndicatorType.atr: {'period': 14, 'useWildersSmoothing': true},
  };

  // Default indicator colors
  static const Map<IndicatorType, Color> _defaultColors = {
    IndicatorType.sma: Colors.blue,
    IndicatorType.ema: Colors.orange,
    IndicatorType.rsi: Colors.purple,
    IndicatorType.macd: Colors.cyan,
    IndicatorType.bollingerBands: Colors.red,
    IndicatorType.atr: Colors.yellow,
  };

  /// Add an indicator
  Future<bool> addIndicator(
    IndicatorType type, {
    Map<String, dynamic>? customParameters,
  }) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final parameters = customParameters ?? _defaultConfigs[type] ?? {};
      final color = _defaultColors[type] ?? Colors.white;

      int nativeId = 0;
      String name = '';

      // Add indicator to native chart engine
      switch (type) {
        case IndicatorType.sma:
          final period = parameters['period'] as int? ?? 20;
          nativeId = _chartEngine.addSMA(period);
          name = 'SMA($period)';
          break;
        case IndicatorType.ema:
          final period = parameters['period'] as int? ?? 20;
          nativeId = _chartEngine.addEMA(period);
          name = 'EMA($period)';
          break;
        case IndicatorType.rsi:
          final period = parameters['period'] as int? ?? 14;
          nativeId = _chartEngine.addRSI(period);
          name = 'RSI($period)';
          break;
        case IndicatorType.macd:
          final fastPeriod = parameters['fastPeriod'] as int? ?? 12;
          final slowPeriod = parameters['slowPeriod'] as int? ?? 26;
          final signalPeriod = parameters['signalPeriod'] as int? ?? 9;
          nativeId = _chartEngine.addMACD(fastPeriod, slowPeriod, signalPeriod);
          name = 'MACD($fastPeriod,$slowPeriod,$signalPeriod)';
          break;
        case IndicatorType.bollingerBands:
          final period = parameters['period'] as int? ?? 20;
          final stdDevMultiplier =
              parameters['stdDevMultiplier'] as double? ?? 2.0;
          nativeId = _chartEngine.addBollingerBands(period, stdDevMultiplier);
          name = 'BB($period,${stdDevMultiplier.toStringAsFixed(1)})';
          break;
        case IndicatorType.atr:
          final period = parameters['period'] as int? ?? 14;
          final useWildersSmoothing =
              parameters['useWildersSmoothing'] as bool? ?? true;
          nativeId = _chartEngine.addATR(period, useWildersSmoothing);
          name = 'ATR($period)';
          break;
      }

      if (nativeId == 0) {
        state = state.copyWith(
          isLoading: false,
          error: 'Failed to add indicator to native engine',
        );
        return false;
      }

      // Create indicator config
      final config = IndicatorConfig(
        id: nativeId,
        type: type,
        name: name,
        parameters: parameters,
        color: color,
        createdAt: DateTime.now(),
      );

      // Add to state
      final updatedIndicators = [...state.indicators, config];
      state = state.copyWith(indicators: updatedIndicators, isLoading: false);

      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to add indicator: $e',
      );
      return false;
    }
  }

  /// Remove an indicator
  Future<bool> removeIndicator(int indicatorId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Remove from native chart engine
      final success = _chartEngine.removeIndicator(indicatorId);

      if (!success) {
        state = state.copyWith(
          isLoading: false,
          error: 'Failed to remove indicator from native engine',
        );
        return false;
      }

      // Remove from state
      final updatedIndicators =
          state.indicators
              .where((indicator) => indicator.id != indicatorId)
              .toList();
      state = state.copyWith(indicators: updatedIndicators, isLoading: false);

      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to remove indicator: $e',
      );
      return false;
    }
  }

  /// Toggle indicator visibility
  void toggleIndicatorVisibility(int indicatorId) {
    final updatedIndicators =
        state.indicators.map((indicator) {
          if (indicator.id == indicatorId) {
            return indicator.copyWith(isVisible: !indicator.isVisible);
          }
          return indicator;
        }).toList();

    state = state.copyWith(indicators: updatedIndicators);
  }

  /// Update indicator color
  void updateIndicatorColor(int indicatorId, Color color) {
    final updatedIndicators =
        state.indicators.map((indicator) {
          if (indicator.id == indicatorId) {
            return indicator.copyWith(color: color);
          }
          return indicator;
        }).toList();

    state = state.copyWith(indicators: updatedIndicators);
  }

  /// Clear all indicators
  Future<void> clearAllIndicators() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      _chartEngine.clearAllIndicators();
      state = state.copyWith(indicators: [], isLoading: false);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to clear indicators: $e',
      );
    }
  }

  /// Get indicator by ID
  IndicatorConfig? getIndicator(int indicatorId) {
    try {
      return state.indicators.firstWhere(
        (indicator) => indicator.id == indicatorId,
      );
    } catch (e) {
      return null;
    }
  }

  /// Get indicators by type
  List<IndicatorConfig> getIndicatorsByType(IndicatorType type) {
    return state.indicators
        .where((indicator) => indicator.type == type)
        .toList();
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Provider for indicator state
final indicatorProvider =
    StateNotifierProvider<IndicatorNotifier, IndicatorState>((ref) {
      // This will need to be injected with the actual chart engine instance
      throw UnimplementedError('Chart engine must be provided');
    });

// Provider factory for creating indicator provider with chart engine
StateNotifierProvider<IndicatorNotifier, IndicatorState>
createIndicatorProvider(ChartEngineWrapper chartEngine) {
  return StateNotifierProvider<IndicatorNotifier, IndicatorState>((ref) {
    return IndicatorNotifier(chartEngine);
  });
}
