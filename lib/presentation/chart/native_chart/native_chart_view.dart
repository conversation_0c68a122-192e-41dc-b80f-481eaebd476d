import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../ffi/chart_engine_wrapper.dart';

class NativeChartView extends StatefulWidget {
  final ChartEngineWrapper chartEngine;
  final double width;
  final double height;
  final Duration timeframe;
  final bool includePriceAxis;
  final bool includeTimeAxis;
  final double priceAxisWidth;
  final double timeAxisHeight;

  const NativeChartView({
    super.key,
    required this.chartEngine,
    required this.width,
    required this.height,
    required this.timeframe,
    this.includePriceAxis = false,
    this.includeTimeAxis = false,
    this.priceAxisWidth = 50,
    this.timeAxisHeight = 30,
  });

  @override
  State<NativeChartView> createState() => _NativeChartViewState();
}

class _NativeChartViewState extends State<NativeChartView> {
  // Platform channel for communicating with native code
  static const MethodChannel _channel = MethodChannel('com.abra/chart_view');
  int? _textureId;

  @override
  void initState() {
    super.initState();
    _initializeTexture();
  }

  Future<void> _initializeTexture() async {
    try {
      final int textureId = await _channel.invokeMethod('createTexture', {
        'width': widget.width.toInt(),
        'height': widget.height.toInt(),
        'timeIntervalSeconds': widget.timeframe.inSeconds,
        'includePriceAxis': widget.includePriceAxis,
        'includeTimeAxis': widget.includeTimeAxis,
        'priceAxisWidth': widget.priceAxisWidth.toInt(),
        'timeAxisHeight': widget.timeAxisHeight.toInt(),
      });

      setState(() {
        _textureId = textureId;
      });
    } on PlatformException catch (e) {
      debugPrint('Failed to create texture: ${e.message}');
    }
  }

  @override
  void didUpdateWidget(NativeChartView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.width != widget.width || oldWidget.height != widget.height) {
      _resizeTexture();
    }
  }

  Future<void> _resizeTexture() async {
    if (_textureId == null) return;
    
    try {
      await _channel.invokeMethod('resizeTexture', {
        'textureId': _textureId,
        'width': widget.width.toInt(),
        'height': widget.height.toInt(),
      });
    } on PlatformException catch (e) {
      debugPrint('Failed to resize texture: ${e.message}');
    }
  }

  @override
  void dispose() {
    if (_textureId != null) {
      _channel.invokeMethod('disposeTexture', {'textureId': _textureId});
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_textureId == null) {
      return Container(
        width: widget.width,
        height: widget.height,
        color: Colors.black,
        child: const Center(child: CircularProgressIndicator()),
      );
    }
    
    return SizedBox(
      width: widget.width,
      height: widget.height,
      child: Texture(textureId: _textureId!),
    );
  }
}