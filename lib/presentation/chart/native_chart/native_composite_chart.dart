import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/chart_engine_provider.dart';
import 'native_chart_view.dart';

class NativeCompositeChart extends ConsumerStatefulWidget {
  final String symbol;
  final Duration interval;
  final double priceAxisWidth;
  final double timeAxisHeight;

  const NativeCompositeChart({
    super.key,
    required this.symbol,
    required this.interval,
    this.priceAxisWidth = 50,
    this.timeAxisHeight = 30,
  });

  @override
  ConsumerState<NativeCompositeChart> createState() =>
      _NativeCompositeChartState();
}

class _NativeCompositeChartState extends ConsumerState<NativeCompositeChart> {
  @override
  void initState() {
    super.initState();
    // Chart engine is now provided by the provider
  }

  @override
  Widget build(BuildContext context) {
    final chartEngine = ref.watch(chartEngineProvider);

    return LayoutBuilder(
      builder: (context, constraints) {
        // Check if chart engine is properly initialized
        if (!chartEngine.isInitialized) {
          return Container(
            width: constraints.maxWidth,
            height: constraints.maxHeight,
            color: Theme.of(context).colorScheme.surface,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.warning_amber_outlined,
                    size: 48,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Chart Engine Not Available',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Native library not built. Run: scripts/build_android.sh',
                    style: Theme.of(context).textTheme.bodySmall,
                    textAlign: TextAlign.center,
                  ),
                  if (chartEngine.initError != null) ...[
                    const SizedBox(height: 8),
                    Text(
                      'Error: ${chartEngine.initError}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.error,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ],
              ),
            ),
          );
        }

        return NativeChartView(
          chartEngine: chartEngine,
          width: constraints.maxWidth,
          height: constraints.maxHeight,
          timeframe: widget.interval,
          includePriceAxis: true,
          includeTimeAxis: true,
          priceAxisWidth: widget.priceAxisWidth,
          timeAxisHeight: widget.timeAxisHeight,
        );
      },
    );
  }
}
