import 'package:flutter/material.dart';
import 'lot_input.dart';

class ChartHeader extends StatefulWidget {
  final bool isTradingButtonsVisible;
  final VoidCallback? onIndicatorsPressed;

  const ChartHeader({
    super.key,
    required this.isTradingButtonsVisible,
    this.onIndicatorsPressed,
  });

  @override
  State<ChartHeader> createState() => _ChartHeaderState();
}

class _ChartHeaderState extends State<ChartHeader>
    with SingleTickerProviderStateMixin {
  final TextEditingController _lotController = TextEditingController(
    text: '1.0',
  );

  @override
  void dispose() {
    _lotController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;

    return SizedBox(
      height: 90, // Increased height to accommodate indicators button
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Top row with indicators button
          if (widget.onIndicatorsPressed != null)
            Padding(
              padding: const EdgeInsets.only(left: 8, top: 4),
              child: Row(
                children: [
                  Material(
                    color: theme.primaryContainer,
                    borderRadius: BorderRadius.circular(8),
                    child: InkWell(
                      onTap: widget.onIndicatorsPressed,
                      borderRadius: BorderRadius.circular(8),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.analytics_outlined,
                              color: theme.onPrimaryContainer,
                              size: 16,
                            ),
                            const SizedBox(width: 6),
                            Text(
                              'Indicators',
                              style: TextStyle(
                                color: theme.onPrimaryContainer,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

          const SizedBox(height: 8),

          // Trading buttons (centered)
          AnimatedSize(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            child: Visibility(
              visible: widget.isTradingButtonsVisible,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  buildTradeButton(context, Colors.red, "Sell", () {}),
                  SizedBox(width: 7),
                  LotSizeComboBox(
                    controller: _lotController,
                    recentLotSizes: const ['0.01', '0.1', '1.0', '5.0', '10.0'],
                    onChanged: (value) {
                      // Handle lot size change
                      debugPrint('Lot size changed to: $value');
                    },
                  ),
                  SizedBox(width: 7),
                  buildTradeButton(context, Colors.green, "Buy", () {}),
                ],
              ),
            ),
          ),
          const SizedBox(height: 3),
          // Symbol info (left-aligned)
          Row(
            children: [
              const Text(
                "EURUSD",
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              const SizedBox(width: 12),
              DropdownButton<String>(
                value: "M15",
                items:
                    ["M1", "M5", "M15", "H1", "H4"]
                        .map(
                          (tf) => DropdownMenuItem(value: tf, child: Text(tf)),
                        )
                        .toList(),
                onChanged: (_) {},
              ),
              const Spacer(),
              IconButton(icon: const Icon(Icons.show_chart), onPressed: () {}),
            ],
          ),
        ],
      ),
    );
  }

  Widget buildTradeButton(
    BuildContext context,
    Color color,
    String text,
    VoidCallback onPressed,
  ) {
    return Container(
      width: 70,
      height: 35,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.zero,
          backgroundColor: Colors.transparent,
        ),
        onPressed: onPressed,
        child: Text(
          text,
          style: TextStyle(
            fontSize: 18,
            fontStyle: FontStyle.italic,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onPrimary,
          ),
        ),
      ),
    );
  }
}
