import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/timeframe_provider.dart';

class TimeframeSelector extends ConsumerStatefulWidget {
  final Function(String) onTimeframeSelected;

  const TimeframeSelector({super.key, required this.onTimeframeSelected});

  @override
  ConsumerState<TimeframeSelector> createState() => TimeframeSelectorState();
}

class TimeframeSelectorState extends ConsumerState<TimeframeSelector>
    with SingleTickerProviderStateMixin {
  late String selectedTimeframe;
  // Use a static const list to avoid recreating the list on each rebuild
  static List<String> timeframes =
      Timeframe.values.map((tf) => tf.label).toList();

  late final AnimationController _animationController;
  late final Animation<double> _slideAnimation;
  OverlayEntry? _overlayEntry;
  OverlayEntry? _barrierEntry;

  @override
  void initState() {
    super.initState();
    // Initialize with current timeframe from provider
    selectedTimeframe = ref.read(selectedTimeframeProvider).label;

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 250), // Slightly faster animation
    );
    _slideAnimation = Tween<double>(begin: -1.0, end: 0.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutCubic),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _removeOverlay();
    super.dispose();
  }

  void _showOverlay(BuildContext context) {
    final theme = Theme.of(context).colorScheme;
    final renderBox = context.findRenderObject() as RenderBox?;
    final position = renderBox?.localToGlobal(Offset.zero);

    if (position != null) {
      _animationController.forward();
      _barrierEntry = _createBarrierEntry();
      _overlayEntry = _createOverlayEntry(context, position, theme);

      // Insert barrier first, then overlay for proper layering
      Overlay.of(context).insert(_barrierEntry!);
      Overlay.of(context).insert(_overlayEntry!);
    }
  }

  void _removeOverlay() {
    if (_overlayEntry != null || _barrierEntry != null) {
      _animationController.reverse().then((_) {
        _overlayEntry?.remove();
        _overlayEntry = null;
        _barrierEntry?.remove();
        _barrierEntry = null;
      });
    }
  }

  OverlayEntry _createBarrierEntry() {
    return OverlayEntry(
      builder:
          (_) => GestureDetector(
            onTap: () {
              _removeOverlay();
            },
            behavior: HitTestBehavior.translucent,
            child: Container(
              color:
                  Colors
                      .transparent, // Make it transparent so it doesn't obscure the view
              width: double.infinity,
              height: double.infinity,
            ),
          ),
    );
  }

  OverlayEntry _createOverlayEntry(
    BuildContext context,
    Offset position,
    ColorScheme theme,
  ) {
    return OverlayEntry(
      builder: (context) {
        return Stack(
          children: [
            Positioned(
              left: 0,
              top: position.dy - 50, // Position above the button
              width: MediaQuery.of(context).size.width,
              child: AnimatedBuilder(
                animation: _slideAnimation,
                builder: (context, child) {
                  return Transform.translate(
                    offset: Offset(
                      MediaQuery.of(context).size.width * _slideAnimation.value,
                      0,
                    ),
                    child: Material(
                      elevation:
                          8.0, // Add elevation for better visual hierarchy
                      color: Colors.transparent,
                      borderRadius: BorderRadius.circular(
                        8.0,
                      ), // Rounded corners
                      child: Container(
                        height: 50.0,
                        padding: const EdgeInsets.symmetric(horizontal: 16.0),
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: timeframes.length,
                          itemBuilder: (context, index) {
                            final timeframe = timeframes[index];
                            final isSelected = timeframe == selectedTimeframe;

                            return Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 4.0,
                              ),
                              child: Center(
                                child: ElevatedButton(
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor:
                                        isSelected
                                            ? theme.primary
                                            : theme.secondary,
                                    foregroundColor:
                                        isSelected
                                            ? theme.onPrimary
                                            : theme.onSecondary,
                                    elevation: isSelected ? 2 : 0,
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12.0,
                                      vertical: 8.0,
                                    ),
                                    minimumSize: const Size(40, 30),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(6.0),
                                      side:
                                          isSelected
                                              ? BorderSide(
                                                color: theme.onPrimary,
                                                width: 1,
                                              )
                                              : BorderSide.none,
                                    ),
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      selectedTimeframe = timeframe;
                                    });
                                    widget.onTimeframeSelected(timeframe);
                                    _removeOverlay();
                                  },
                                  child: Text(
                                    timeframe,
                                    style: TextStyle(
                                      fontWeight:
                                          isSelected
                                              ? FontWeight.bold
                                              : FontWeight.normal,
                                      fontSize: 12,
                                    ),
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        boxShadow: [
          BoxShadow(
            color: theme.shadow,
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: ElevatedButton(
        style: ButtonStyle(
          minimumSize: WidgetStateProperty.all<Size>(const Size(30, 30)),
          maximumSize: WidgetStateProperty.all<Size>(const Size(40, 30)),
          shape: WidgetStateProperty.all(
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
          ),
          foregroundColor: WidgetStateProperty.all<Color>(theme.primary),
          backgroundColor: WidgetStateProperty.all<Color>(theme.onPrimary),
          padding: WidgetStateProperty.all<EdgeInsets>(EdgeInsets.zero),
          elevation: WidgetStateProperty.resolveWith<double>((
            Set<WidgetState> states,
          ) {
            if (states.contains(WidgetState.pressed)) return 0;
            return 1.0;
          }),
        ),
        onPressed: () {
          if (_overlayEntry == null) {
            _showOverlay(context);
          } else {
            _removeOverlay();
          }
        },
        child: Center(
          child: Text(
            selectedTimeframe,
            style: TextStyle(
              color: theme.primary,
              fontSize: 11,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }
}

enum Timeframe {
  s5,
  s10,
  s30,
  m1,
  m3,
  m5,
  m10,
  m15,
  m30,
  m45,
  h1,
  h2,
  h3,
  h4,
  h6,
  h8,
  h12,
  d1,
  w1,
  m1d,
  m3d,
}

extension TimeframeExtension on Timeframe {
  String get label {
    switch (this) {
      case Timeframe.s5:
        return '5s';
      case Timeframe.s10:
        return '10s';
      case Timeframe.s30:
        return '30s';
      case Timeframe.m1:
        return '1m';
      case Timeframe.m3:
        return '3m';
      case Timeframe.m5:
        return '5m';
      case Timeframe.m10:
        return '10m';
      case Timeframe.m15:
        return '15m';
      case Timeframe.m30:
        return '30m';
      case Timeframe.m45:
        return '45m';
      case Timeframe.h1:
        return '1H';
      case Timeframe.h2:
        return '2H';
      case Timeframe.h3:
        return '3H';
      case Timeframe.h4:
        return '4H';
      case Timeframe.h6:
        return '6H';
      case Timeframe.h8:
        return '8H';
      case Timeframe.h12:
        return '12H';
      case Timeframe.d1:
        return '1D';
      case Timeframe.w1:
        return '1W';
      case Timeframe.m1d:
        return '1M';
      case Timeframe.m3d:
        return '3M';
    }
  }

  Duration get duration {
    switch (this) {
      case Timeframe.s5:
        return const Duration(seconds: 5);
      case Timeframe.s10:
        return const Duration(seconds: 10);
      case Timeframe.s30:
        return const Duration(seconds: 30);
      case Timeframe.m1:
        return const Duration(minutes: 1);
      case Timeframe.m3:
        return const Duration(minutes: 3);
      case Timeframe.m5:
        return const Duration(minutes: 5);
      case Timeframe.m10:
        return const Duration(minutes: 10);
      case Timeframe.m15:
        return const Duration(minutes: 15);
      case Timeframe.m30:
        return const Duration(minutes: 30);
      case Timeframe.m45:
        return const Duration(minutes: 45);
      case Timeframe.h1:
        return const Duration(hours: 1);
      case Timeframe.h2:
        return const Duration(hours: 2);
      case Timeframe.h3:
        return const Duration(hours: 3);
      case Timeframe.h4:
        return const Duration(hours: 4);
      case Timeframe.h6:
        return const Duration(hours: 6);
      case Timeframe.h8:
        return const Duration(hours: 8);
      case Timeframe.h12:
        return const Duration(hours: 12);
      case Timeframe.d1:
        return const Duration(days: 1);
      case Timeframe.w1:
        return const Duration(days: 5);
      case Timeframe.m1d:
        return const Duration(days: 30);
      case Timeframe.m3d:
        return const Duration(days: 90);
    }
  }
}
