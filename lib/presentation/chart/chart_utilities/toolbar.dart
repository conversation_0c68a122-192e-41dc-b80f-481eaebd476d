import 'package:flutter/material.dart';
import '../providers/indicator_provider.dart';

// Toolbar side enum
enum RocketbarSide { left, right }

// Tool groups enum
enum ToolGroup {
  indicators,
  trendlines,
  gannFibs,
  patterns,
  forecast,
  shapes,
  annotations,
  visuals,
}

// Tool types enum
enum ToolType {
  // Indicators
  sma,
  ema,
  rsi,
  macd,
  bollingerBands,
  atr,

  // Drawing tools
  trendLine,
  horizontalLine,
  verticalLine,
  horizontalray,
  ray,
  fibRetracement,
  gannFan,
  headAndShoulders,
  doubleTop,
  ruler,
  path,
  projectionBox,
  rectangle,
  ellipse,
  triangle,
  text,
  arrow,
  label,
  priceNote,
  icon,
  brush,
  imageOverlay,
}

// Extension to get tool name
extension ToolTypeExtension on ToolType {
  String get name {
    switch (this) {
      // Indicators
      case ToolType.sma:
        return 'Simple Moving Average';
      case ToolType.ema:
        return 'Exponential Moving Average';
      case ToolType.rsi:
        return 'Relative Strength Index';
      case ToolType.macd:
        return 'MACD';
      case ToolType.bollingerBands:
        return 'Bollinger Bands';
      case ToolType.atr:
        return 'Average True Range';

      // Drawing tools
      case ToolType.trendLine:
        return 'Trend Line';
      case ToolType.horizontalLine:
        return 'Horizontal Line';
      case ToolType.verticalLine:
        return 'Vertical Line';
      case ToolType.horizontalray:
        return 'Horizontal Ray';
      case ToolType.ray:
        return 'Ray';
      case ToolType.fibRetracement:
        return 'Fibonacci Retracement';
      case ToolType.gannFan:
        return 'Gann Fan';
      case ToolType.headAndShoulders:
        return 'Head and Shoulders';
      case ToolType.doubleTop:
        return 'Double Top';
      case ToolType.ruler:
        return 'Ruler';
      case ToolType.path:
        return 'Path';
      case ToolType.projectionBox:
        return 'Projection Box';
      case ToolType.rectangle:
        return 'Rectangle';
      case ToolType.ellipse:
        return 'Ellipse';
      case ToolType.triangle:
        return 'Triangle';
      case ToolType.text:
        return 'Text';
      case ToolType.arrow:
        return 'Arrow';
      case ToolType.label:
        return 'Label';
      case ToolType.priceNote:
        return 'Price Note';
      case ToolType.icon:
        return 'Icon';
      case ToolType.brush:
        return 'Brush';
      case ToolType.imageOverlay:
        return 'Image Overlay';
    }
  }
}

final Map<ToolGroup, List<ToolType>> toolGroups = {
  ToolGroup.indicators: [
    ToolType.sma,
    ToolType.ema,
    ToolType.rsi,
    ToolType.macd,
    ToolType.bollingerBands,
    ToolType.atr,
  ],
  ToolGroup.trendlines: [
    ToolType.trendLine,
    ToolType.horizontalLine,
    ToolType.verticalLine,
    ToolType.horizontalray,
    ToolType.ray,
  ],
  ToolGroup.gannFibs: [ToolType.fibRetracement, ToolType.gannFan],
  ToolGroup.patterns: [ToolType.headAndShoulders, ToolType.doubleTop],
  ToolGroup.forecast: [ToolType.ruler, ToolType.path, ToolType.projectionBox],
  ToolGroup.shapes: [ToolType.rectangle, ToolType.ellipse, ToolType.triangle],
  ToolGroup.annotations: [
    ToolType.text,
    ToolType.arrow,
    ToolType.label,
    ToolType.priceNote,
  ],
  ToolGroup.visuals: [ToolType.icon, ToolType.brush, ToolType.imageOverlay],
};

class RocketToolbar extends StatefulWidget {
  final RocketbarSide side;
  final Function(ToolType) onToolSelected;
  final Function(IndicatorType)? onIndicatorSelected;

  const RocketToolbar({
    required this.side,
    required this.onToolSelected,
    this.onIndicatorSelected,
    super.key,
  });

  @override
  State<RocketToolbar> createState() => _RocketToolbarState();
}

class _RocketToolbarState extends State<RocketToolbar> {
  ToolGroup? expandedGroup;
  bool isAnimating = false;

  void onGroupTap(ToolGroup group) async {
    if (isAnimating) return;

    if (expandedGroup == null) {
      setState(() => expandedGroup = group);
    } else if (expandedGroup == group) {
      setState(() => expandedGroup = null);
    } else {
      isAnimating = true;
      setState(() => expandedGroup = null);
      await Future.delayed(const Duration(milliseconds: 200));
      setState(() {
        expandedGroup = group;
        isAnimating = false;
      });
    }
  }

  IconData getGroupIcon(ToolGroup group) {
    switch (group) {
      case ToolGroup.indicators:
        return Icons.analytics;
      case ToolGroup.trendlines:
        return Icons.show_chart;
      case ToolGroup.gannFibs:
        return Icons.timeline;
      case ToolGroup.patterns:
        return Icons.auto_graph;
      case ToolGroup.forecast:
        return Icons.stacked_line_chart;
      case ToolGroup.shapes:
        return Icons.crop_square;
      case ToolGroup.annotations:
        return Icons.text_fields;
      case ToolGroup.visuals:
        return Icons.brush;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children:
          ToolGroup.values.map((group) {
            return Stack(
              alignment: Alignment.center,
              children: [
                IconButton(
                  icon: Icon(
                    getGroupIcon(group),
                  ), // Replace with actual group icon
                  onPressed: () => onGroupTap(group),
                ),
                if (expandedGroup == group)
                  Positioned(
                    left: widget.side == RocketbarSide.right ? -60 : null,
                    right: widget.side == RocketbarSide.left ? -60 : null,
                    child: AnimatedSwitcher(
                      duration: const Duration(milliseconds: 100),
                      child: BabyToolbar(
                        key: ValueKey(group),
                        group: group,
                        side: widget.side,
                        onToolSelected: widget.onToolSelected,
                        onIndicatorSelected: widget.onIndicatorSelected,
                      ),
                    ),
                  ),
              ],
            );
          }).toList(),
    );
  }
}

class BabyToolbar extends StatelessWidget {
  final ToolGroup group;
  final RocketbarSide side;
  final Function(ToolType) onToolSelected;
  final Function(IndicatorType)? onIndicatorSelected;

  const BabyToolbar({
    super.key,
    required this.group,
    required this.side,
    required this.onToolSelected,
    this.onIndicatorSelected,
  });

  @override
  Widget build(BuildContext context) {
    final tools = toolGroups[group]!;

    return Container(
      padding: const EdgeInsets.all(6),
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.secondary,
            blurRadius: 8,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children:
            tools.map((tool) {
              return IconButton(
                icon: Icon(getToolIcon(tool), size: 20, color: Colors.white),
                onPressed: () => _handleToolSelection(tool),
                tooltip: tool.name,
              );
            }).toList(),
      ),
    );
  }

  void _handleToolSelection(ToolType tool) {
    // Check if this is an indicator tool
    if (group == ToolGroup.indicators) {
      // Convert ToolType to IndicatorType for indicators
      final indicatorType = _toolTypeToIndicatorType(tool);
      if (indicatorType != null && onIndicatorSelected != null) {
        onIndicatorSelected!(indicatorType);
      }
    } else {
      // Handle regular drawing tools
      onToolSelected(tool);
    }
  }

  IndicatorType? _toolTypeToIndicatorType(ToolType toolType) {
    switch (toolType) {
      case ToolType.sma:
        return IndicatorType.sma;
      case ToolType.ema:
        return IndicatorType.ema;
      case ToolType.rsi:
        return IndicatorType.rsi;
      case ToolType.macd:
        return IndicatorType.macd;
      case ToolType.bollingerBands:
        return IndicatorType.bollingerBands;
      case ToolType.atr:
        return IndicatorType.atr;
      default:
        return null;
    }
  }

  IconData getToolIcon(ToolType tool) {
    switch (tool) {
      // Indicators
      case ToolType.sma:
        return Icons.trending_up;
      case ToolType.ema:
        return Icons.show_chart;
      case ToolType.rsi:
        return Icons.speed;
      case ToolType.macd:
        return Icons.timeline;
      case ToolType.bollingerBands:
        return Icons.linear_scale;
      case ToolType.atr:
        return Icons.bar_chart;

      // Drawing tools
      case ToolType.trendLine:
        return Icons.trending_up;
      case ToolType.horizontalLine:
        return Icons.horizontal_rule;
      case ToolType.horizontalray:
        return Icons.trending_flat;
      case ToolType.verticalLine:
        return Icons.vertical_align_top;
      case ToolType.ray:
        return Icons.show_chart;
      case ToolType.fibRetracement:
        return Icons.fitbit;
      case ToolType.gannFan:
        return Icons.change_history;
      case ToolType.headAndShoulders:
        return Icons.account_tree;
      case ToolType.doubleTop:
        return Icons.format_shapes;
      case ToolType.ruler:
        return Icons.straighten;
      case ToolType.path:
        return Icons.timeline;
      case ToolType.projectionBox:
        return Icons.grid_on;
      case ToolType.rectangle:
        return Icons.crop_square;
      case ToolType.ellipse:
        return Icons.circle_outlined;
      case ToolType.triangle:
        return Icons.change_history;
      case ToolType.text:
        return Icons.text_fields;
      case ToolType.arrow:
        return Icons.arrow_right_alt;
      case ToolType.label:
        return Icons.label;
      case ToolType.priceNote:
        return Icons.sticky_note_2;
      case ToolType.icon:
        return Icons.star;
      case ToolType.brush:
        return Icons.brush;
      case ToolType.imageOverlay:
        return Icons.image;
    }
  }
}
