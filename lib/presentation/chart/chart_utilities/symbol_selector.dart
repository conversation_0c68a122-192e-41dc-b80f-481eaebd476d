import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../symbol/providers/symbol_provider.dart';
import '../../watchlist/providers/watchlist_provider.dart';

/// Watchlist symbol selector for chart trading - shows only watchlist symbols
class SymbolSelector extends ConsumerWidget {
  const SymbolSelector({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch watchlist symbols instead of all available symbols
    final watchlistSymbols = ref.watch(selectedWatchlistSymbolsProvider);
    final selectedSymbol = ref.watch(selectedSymbolProvider);
    final theme = Theme.of(context).colorScheme;

    return Container(
      width: 80,
      height: 40,
      padding: const EdgeInsets.all(0.5),
      decoration: BoxDecoration(
        color: theme.primary,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.primary),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap:
              () => _showWatchlistSymbolDialog(context, ref, watchlistSymbols),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  selectedSymbol,
                  style: TextStyle(
                    color: theme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Show watchlist symbol selection dialog
  Future<void> _showWatchlistSymbolDialog(
    BuildContext context,
    WidgetRef ref,
    List<String> watchlistSymbols,
  ) async {
    if (watchlistSymbols.isEmpty) {
      // Show message if no symbols in watchlist
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No symbols in here.'),
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    return showDialog<void>(
      context: context,
      builder:
          (context) => symbolItems(
            symbols: watchlistSymbols,
            onSymbolSelected: (symbol) {
              ref.read(selectedSymbolProvider.notifier).updateSymbol(symbol);
              Navigator.of(context).pop();
            },
          ),
    );
  }

  Widget symbolItems({
    required List<String> symbols,
    required Function(String) onSymbolSelected,
  }) {
    // Create dropdown menu entries from the symbols list
    final List<DropdownMenuEntry<String>> entries =
        symbols.map((symbol) {
          return DropdownMenuEntry<String>(value: symbol, label: symbol);
        }).toList();

    return DropdownMenu<String>(
      menuStyle: MenuStyle(
        backgroundColor: WidgetStateProperty.all(Colors.transparent),
      ),
      onSelected: (symbol) => onSymbolSelected(symbol!),
      dropdownMenuEntries: entries,
    );
  }
}
