import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'chart_screen/chart_content.dart';
import 'chart_screen/chart_header.dart';
import 'chart_screen/chart_toolbar.dart';
import 'chart_utilities/toolbar.dart';
import 'indicators/indicator_config_dialog.dart';
import 'providers/chart_engine_provider.dart';
import 'providers/indicator_provider.dart';

// Main Chart Screen
class ChartScreen extends ConsumerStatefulWidget {
  const ChartScreen({super.key});

  @override
  ConsumerState<ChartScreen> createState() => _ChartScreenState();
}

class _ChartScreenState extends ConsumerState<ChartScreen> {
  // UI state management
  bool isToolbarVisible = false;
  bool isTradingButtonsVisible = false;
  bool isRocketToolbarVisible = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;

    return Scaffold(
      backgroundColor: theme.primary,
      body: <PERSON><PERSON><PERSON>(
        child: Column(
          children: [
            Expanded(
              child: Stack(
                children: [
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    height: 90,
                    child: ChartHeaderWidget(
                      isTradingButtonsVisible: isTradingButtonsVisible,
                      onIndicatorsPressed:
                          () => setState(() {
                            isRocketToolbarVisible = !isRocketToolbarVisible;
                          }),
                    ),
                  ),
                  const Positioned.fill(
                    top: 90,
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: ChartContentWidget(),
                  ),
                  ChartGestureDetector(
                    onToggleToolbar:
                        () => setState(
                          () => isToolbarVisible = !isToolbarVisible,
                        ),
                    onToggleTradingButtons:
                        () => setState(
                          () =>
                              isTradingButtonsVisible =
                                  !isTradingButtonsVisible,
                        ),
                  ),

                  // RocketToolbar overlay
                  if (isRocketToolbarVisible) ...[
                    // Backdrop
                    Positioned.fill(
                      child: GestureDetector(
                        onTap:
                            () => setState(() {
                              isRocketToolbarVisible = false;
                            }),
                        child: Container(
                          color: Colors.black.withValues(alpha: 0.3),
                        ),
                      ),
                    ),
                    // RocketToolbar
                    Positioned(
                      top: 100,
                      left: 16,
                      child: Consumer(
                        builder: (context, ref, child) {
                          final chartEngine = ref.watch(chartEngineProvider);
                          return RocketToolbar(
                            side: RocketbarSide.left,
                            onToolSelected: (toolType) {
                              // Handle drawing tool selection
                              debugPrint('Drawing tool selected: $toolType');
                              setState(() {
                                isRocketToolbarVisible = false;
                              });
                            },
                            onIndicatorSelected: (indicatorType) async {
                              // Handle indicator selection with configuration dialog
                              final parameters =
                                  await showIndicatorConfigDialog(
                                    context,
                                    indicatorType,
                                  );

                              if (parameters != null) {
                                final success = await ref
                                    .read(
                                      createIndicatorProvider(
                                        chartEngine,
                                      ).notifier,
                                    )
                                    .addIndicator(
                                      indicatorType,
                                      customParameters: parameters,
                                    );

                                if (success) {
                                  setState(() {
                                    isRocketToolbarVisible = false;
                                  });
                                }
                              }
                            },
                          );
                        },
                      ),
                    ),
                  ],
                ],
              ),
            ),
            Consumer(
              builder: (context, ref, child) {
                final chartEngine = ref.watch(chartEngineProvider);
                return ChartToolbarWidget(
                  isVisible: isToolbarVisible,
                  chartEngine: chartEngine,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}
