import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'market_service.dart';

/// Enhanced real-time service that reduces WebSocket complexity
///
/// Instead of managing individual WebSocket connections per symbol,
/// this service uses periodic server calls to get bulk price updates.
/// This reduces client complexity and server load.
class RealtimeService {
  static final RealtimeService _instance = RealtimeService._();
  factory RealtimeService() => _instance;
  RealtimeService._();

  final MarketService _marketService = MarketService();
  final Set<String> _subscribedSymbols = {};
  Timer? _updateTimer;

  // Stream controller for price updates
  final StreamController<Map<String, SymbolPriceDto>> _priceStreamController =
      StreamController<Map<String, SymbolPriceDto>>.broadcast();

  // Update frequency - much less aggressive than WebSocket
  static const Duration _updateInterval = Duration(seconds: 30);

  // Cache for latest prices
  final Map<String, SymbolPriceDto> _latestPrices = {};

  /// Subscribe to price updates for symbols
  /// Replaces complex WebSocket management with simple periodic updates
  void subscribeToSymbols(List<String> symbols) {
    final hadSymbols = _subscribedSymbols.isNotEmpty;
    _subscribedSymbols.addAll(symbols);

    // Start periodic updates if this is the first subscription
    if (!hadSymbols && _subscribedSymbols.isNotEmpty) {
      _startPeriodicUpdates();
    }

    // Immediately fetch prices for new symbols (async to avoid blocking)
    Future.microtask(() => _fetchPricesForSymbols(symbols));
  }

  /// Unsubscribe from symbols
  void unsubscribeFromSymbols(List<String> symbols) {
    _subscribedSymbols.removeAll(symbols);

    // Remove from cache
    for (final symbol in symbols) {
      _latestPrices.remove(symbol);
    }

    // Stop updates if no symbols left
    if (_subscribedSymbols.isEmpty) {
      _stopPeriodicUpdates();
    }
  }

  /// Get stream of price updates
  Stream<Map<String, SymbolPriceDto>> get priceUpdates =>
      _priceStreamController.stream;

  /// Get latest price for a symbol
  SymbolPriceDto? getLatestPrice(String symbol) {
    return _latestPrices[symbol];
  }

  /// Get all latest prices
  Map<String, SymbolPriceDto> get allLatestPrices => Map.from(_latestPrices);

  /// Get count of subscribed symbols for performance monitoring
  int get subscribedSymbolsCount => _subscribedSymbols.length;

  /// Start periodic price updates
  void _startPeriodicUpdates() {
    _updateTimer?.cancel();
    _updateTimer = Timer.periodic(_updateInterval, (_) {
      _fetchAllSubscribedPrices();
    });

    debugPrint(
      'Started periodic price updates every ${_updateInterval.inSeconds}s',
    );
  }

  /// Stop periodic updates
  void _stopPeriodicUpdates() {
    _updateTimer?.cancel();
    _updateTimer = null;
    debugPrint('Stopped periodic price updates');
  }

  /// Fetch prices for all subscribed symbols
  Future<void> _fetchAllSubscribedPrices() async {
    if (_subscribedSymbols.isEmpty) return;

    await _fetchPricesForSymbols(_subscribedSymbols.toList());
  }

  /// Fetch prices for specific symbols
  Future<void> _fetchPricesForSymbols(List<String> symbols) async {
    if (symbols.isEmpty) return;

    try {
      final response = await _marketService.getBulkPrices(symbols);
      final updates = <String, SymbolPriceDto>{};

      for (final price in response.prices) {
        _latestPrices[price.symbol] = price;
        updates[price.symbol] = price;
      }

      // Handle failed symbols by creating error entries
      for (final failedSymbol in response.failedSymbols) {
        final errorPrice = SymbolPriceDto(
          symbol: failedSymbol,
          status: 'error',
          currentPrice: null,
          priceChange: null,
          priceChangePercent: null,
          lastUpdated: DateTime.now(),
        );
        _latestPrices[failedSymbol] = errorPrice;
        updates[failedSymbol] = errorPrice;
      }

      // Emit updates if there are any
      if (updates.isNotEmpty && !_priceStreamController.isClosed) {
        _priceStreamController.add(updates);
      }

      debugPrint(
        '✅ Updated prices for ${response.successCount}/${symbols.length} symbols (${response.failureCount} failed)',
      );

      if (response.failedSymbols.isNotEmpty) {
        debugPrint('❌ Failed symbols: ${response.failedSymbols.join(', ')}');
      }
    } catch (error) {
      debugPrint('❌ Error fetching prices for symbols: $error');

      // Create error entries for all symbols
      final errorUpdates = <String, SymbolPriceDto>{};
      for (final symbol in symbols) {
        final errorPrice = SymbolPriceDto(
          symbol: symbol,
          status: 'error',
          currentPrice: null,
          priceChange: null,
          priceChangePercent: null,
          lastUpdated: DateTime.now(),
        );
        _latestPrices[symbol] = errorPrice;
        errorUpdates[symbol] = errorPrice;
      }

      if (errorUpdates.isNotEmpty && !_priceStreamController.isClosed) {
        _priceStreamController.add(errorUpdates);
      }
    }
  }

  /// Force refresh all prices (bypass cache)
  Future<void> forceRefresh() async {
    if (_subscribedSymbols.isEmpty) return;

    try {
      final response = await _marketService.getBulkPrices(
        _subscribedSymbols.toList(),
        bypassCache: true,
      );

      final updates = <String, SymbolPriceDto>{};
      for (final price in response.prices) {
        _latestPrices[price.symbol] = price;
        updates[price.symbol] = price;
      }

      if (updates.isNotEmpty && !_priceStreamController.isClosed) {
        _priceStreamController.add(updates);
      }

      debugPrint('Force refreshed ${updates.length} symbols');
    } catch (error) {
      debugPrint('Error force refreshing prices: $error');
    }
  }

  /// Get subscription status
  bool get hasActiveSubscriptions => _subscribedSymbols.isNotEmpty;

  /// Get subscribed symbols
  Set<String> get subscribedSymbols => Set.from(_subscribedSymbols);

  /// Dispose resources
  void dispose() {
    _stopPeriodicUpdates();
    _priceStreamController.close();
    _latestPrices.clear();
    _subscribedSymbols.clear();
  }
}

/// Provider for the enhanced realtime service
final enhancedRealtimeServiceProvider = Provider<RealtimeService>((ref) {
  final service = RealtimeService();

  // Dispose when provider is disposed
  ref.onDispose(() {
    service.dispose();
  });

  return service;
});

/// Provider for real-time price updates stream
final realtimePriceUpdatesProvider =
    StreamProvider<Map<String, SymbolPriceDto>>((ref) {
      final service = ref.watch(enhancedRealtimeServiceProvider);
      return service.priceUpdates;
    });

/// Provider for latest price of a specific symbol
final latestSymbolPriceProvider = Provider.family<SymbolPriceDto?, String>((
  ref,
  symbol,
) {
  final service = ref.watch(enhancedRealtimeServiceProvider);

  // Watch for updates to trigger rebuilds
  ref.watch(realtimePriceUpdatesProvider);

  return service.getLatestPrice(symbol);
});

/// Notifier for managing symbol subscriptions
class SymbolSubscriptionNotifier extends StateNotifier<Set<String>> {
  final RealtimeService _service;

  SymbolSubscriptionNotifier(this._service) : super({});

  /// Subscribe to a symbol
  void subscribe(String symbol) {
    if (!state.contains(symbol)) {
      _service.subscribeToSymbols([symbol]);
      state = {...state, symbol};
    }
  }

  /// Subscribe to multiple symbols
  void subscribeToMultiple(List<String> symbols) {
    final newSymbols = symbols.where((s) => !state.contains(s)).toList();
    if (newSymbols.isNotEmpty) {
      _service.subscribeToSymbols(newSymbols);
      state = {...state, ...newSymbols};
    }
  }

  /// Unsubscribe from a symbol
  void unsubscribe(String symbol) {
    if (state.contains(symbol)) {
      _service.unsubscribeFromSymbols([symbol]);
      state = Set.from(state)..remove(symbol);
    }
  }

  /// Unsubscribe from multiple symbols
  void unsubscribeFromMultiple(List<String> symbols) {
    final toRemove = symbols.where((s) => state.contains(s)).toList();
    if (toRemove.isNotEmpty) {
      _service.unsubscribeFromSymbols(toRemove);
      state = Set.from(state)..removeAll(toRemove);
    }
  }

  /// Clear all subscriptions
  void clearAll() {
    if (state.isNotEmpty) {
      _service.unsubscribeFromSymbols(state.toList());
      state = {};
    }
  }

  /// Force refresh all subscribed prices
  Future<void> forceRefresh() async {
    await _service.forceRefresh();
  }
}

/// Provider for symbol subscription management
final symbolSubscriptionProvider =
    StateNotifierProvider<SymbolSubscriptionNotifier, Set<String>>((ref) {
      final service = ref.watch(enhancedRealtimeServiceProvider);
      return SymbolSubscriptionNotifier(service);
    });

/// Provider that automatically subscribes to watchlist symbols
final autoWatchlistSubscriptionProvider = Provider<void>((ref) {
  // This would be connected to your watchlist provider
  // For now, it's a placeholder showing the pattern

  // Example: Subscribe to symbols from selected watchlist
  // final selectedWatchlist = ref.watch(selectedWatchlistProvider);
  // final subscriptionNotifier = ref.watch(symbolSubscriptionProvider.notifier);
  // if (selectedWatchlist != null) {
  //   final symbols = selectedWatchlist.symbols.map((s) => s.symbol).toList();
  //   subscriptionNotifier.subscribeToMultiple(symbols);
  // }

  return;
});

/// Performance metrics for the enhanced service
final realtimePerformanceProvider = Provider<Map<String, dynamic>>((ref) {
  final service = ref.watch(enhancedRealtimeServiceProvider);

  return {
    'hasActiveSubscriptions': service.hasActiveSubscriptions,
    'subscribedSymbolsCount': service.subscribedSymbols.length,
    'updateInterval': 30, // seconds
    'cachedPricesCount': service.allLatestPrices.length,
  };
});
