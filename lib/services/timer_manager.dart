import 'dart:async';
import 'package:flutter/foundation.dart';
import '../core/constants.dart';

/// Centralized timer manager to optimize background tasks and reduce main thread load
/// Consolidates multiple Timer.periodic instances into a single coordinated system
class TimerManager {
  static final TimerManager _instance = TimerManager._internal();
  factory TimerManager() => _instance;
  TimerManager._internal();

  Timer? _masterTimer;
  int _tickCount = 0;
  bool _isDisposed = false;

  // Registered callbacks for different intervals
  final Map<Duration, List<VoidCallback>> _callbacks = {};
  final Map<String, VoidCallback> _namedCallbacks = {};

  // Performance monitoring
  final List<Duration> _executionTimes = [];
  static const int _maxExecutionTimesSamples = 100;

  /// Initialize the timer manager with a master timer
  void initialize() {
    if (_masterTimer != null || _isDisposed) return;

    debugPrint('TimerManager: Initializing centralized timer system');

    // Use a 1-second master timer as the base unit
    _masterTimer = Timer.periodic(const Duration(seconds: 1), _onTick);
  }

  /// Register a callback to be executed at specified intervals
  void registerCallback({
    required Duration interval,
    required VoidCallback callback,
    String? name,
  }) {
    if (_isDisposed) return;

    // Store named callbacks for easy removal
    if (name != null) {
      _namedCallbacks[name] = callback;
    }

    // Group callbacks by interval
    _callbacks.putIfAbsent(interval, () => []).add(callback);

    debugPrint(
      'TimerManager: Registered callback for ${interval.inSeconds}s interval${name != null ? ' (name: $name)' : ''}',
    );
  }

  /// Unregister a named callback
  void unregisterCallback(String name) {
    final callback = _namedCallbacks.remove(name);
    if (callback != null) {
      // Remove from all interval lists
      _callbacks.forEach((interval, callbacks) {
        callbacks.remove(callback);
      });
      debugPrint('TimerManager: Unregistered callback: $name');
    }
  }

  /// Master tick handler - executes callbacks based on their intervals
  void _onTick(Timer timer) {
    if (_isDisposed) {
      timer.cancel();
      return;
    }

    final stopwatch = Stopwatch()..start();
    _tickCount++;

    try {
      // Execute callbacks based on their intervals
      _callbacks.forEach((interval, callbacks) {
        if (_shouldExecuteForInterval(interval)) {
          for (final callback in callbacks) {
            try {
              callback();
            } catch (e) {
              debugPrint('TimerManager: Error executing callback: $e');
            }
          }
        }
      });
    } catch (e) {
      debugPrint('TimerManager: Error in master tick: $e');
    }

    stopwatch.stop();
    _recordExecutionTime(stopwatch.elapsed);
  }

  /// Determine if callbacks should execute for a given interval
  bool _shouldExecuteForInterval(Duration interval) {
    final intervalSeconds = interval.inSeconds;
    return intervalSeconds > 0 && (_tickCount % intervalSeconds) == 0;
  }

  /// Record execution time for performance monitoring
  void _recordExecutionTime(Duration executionTime) {
    _executionTimes.add(executionTime);

    // Keep only recent samples
    if (_executionTimes.length > _maxExecutionTimesSamples) {
      _executionTimes.removeAt(0);
    }

    // Log warning if execution time is too high
    if (executionTime.inMilliseconds > 16) {
      // More than one frame at 60fps
      debugPrint(
        'TimerManager: High execution time: ${executionTime.inMilliseconds}ms',
      );
    }
  }

  /// Get performance statistics
  Map<String, dynamic> getPerformanceStats() {
    if (_executionTimes.isEmpty) {
      return {
        'averageExecutionTime': 0,
        'maxExecutionTime': 0,
        'sampleCount': 0,
      };
    }

    final totalTime = _executionTimes.fold<int>(
      0,
      (sum, time) => sum + time.inMicroseconds,
    );
    final averageTime = totalTime / _executionTimes.length;
    final maxTime = _executionTimes
        .map((t) => t.inMicroseconds)
        .reduce((a, b) => a > b ? a : b);

    return {
      'averageExecutionTime': averageTime / 1000, // Convert to milliseconds
      'maxExecutionTime': maxTime / 1000, // Convert to milliseconds
      'sampleCount': _executionTimes.length,
      'activeCallbacks': _callbacks.values.fold<int>(
        0,
        (sum, list) => sum + list.length,
      ),
    };
  }

  /// Dispose the timer manager
  void dispose() {
    if (_isDisposed) return;

    debugPrint('TimerManager: Disposing centralized timer system');

    _masterTimer?.cancel();
    _masterTimer = null;
    _callbacks.clear();
    _namedCallbacks.clear();
    _executionTimes.clear();
    _isDisposed = true;
  }

  /// Check if the timer manager is active
  bool get isActive => _masterTimer != null && !_isDisposed;

  /// Get current tick count
  int get tickCount => _tickCount;
}

/// Extension methods for common timer intervals
extension TimerManagerExtensions on TimerManager {
  /// Register a callback for price updates (30 seconds)
  void registerPriceUpdateCallback(VoidCallback callback, {String? name}) {
    registerCallback(
      interval: MarketServiceConfig.priceUpdateInterval,
      callback: callback,
      name: name,
    );
  }

  /// Register a callback for cache cleanup (15 minutes)
  void registerCacheCleanupCallback(VoidCallback callback, {String? name}) {
    registerCallback(
      interval: MarketServiceConfig.cacheCleanupInterval,
      callback: callback,
      name: name,
    );
  }

  /// Register a callback for connection checks (5 minutes)
  void registerConnectionCheckCallback(VoidCallback callback, {String? name}) {
    registerCallback(
      interval: MarketServiceConfig.connectionCheckInterval,
      callback: callback,
      name: name,
    );
  }

  /// Register a callback for health checks (30 seconds)
  void registerHealthCheckCallback(VoidCallback callback, {String? name}) {
    registerCallback(
      interval: const Duration(seconds: 30),
      callback: callback,
      name: name,
    );
  }
}
