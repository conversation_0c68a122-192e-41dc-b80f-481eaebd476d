import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../core/constants.dart';

/// Server-centric authentication client for communicating with abra-servers auth-service
/// Handles all authentication operations through server endpoints
class AuthClient {
  static final AuthClient _instance = AuthClient._();
  factory AuthClient() => _instance;
  AuthClient._();

  final _httpClient = http.Client();

  // Token storage keys
  static const String _accessTokenKey = 'server_access_token';
  static const String _refreshTokenKey = 'server_refresh_token';
  static const String _userDataKey = 'server_user_data';

  /// Get the base URL for abra-servers
  String get baseUrl => AuthServiceConfig.baseUrl;

  /// Current stored access token
  String? _accessToken;
  String? _refreshToken;
  ServerUser? _currentUser;
  DateTime? _tokenObtainedAt;

  /// Get current access token
  /// Returns fresh Supabase token if available, otherwise stored token
  String? get accessToken {
    // First try to get fresh Supabase token
    final supabaseSession = Supabase.instance.client.auth.currentSession;
    if (supabaseSession != null && !supabaseSession.isExpired) {
      return supabaseSession.accessToken;
    }

    // Fallback to stored token
    return _accessToken;
  }

  /// Get current refresh token
  String? get refreshToken => _refreshToken;

  /// Get current user
  /// Returns user from Supabase session if available, otherwise stored user
  ServerUser? get currentUser {
    // If we have a Supabase session, create a ServerUser from it
    final supabaseUser = Supabase.instance.client.auth.currentUser;
    if (supabaseUser != null) {
      return ServerUser(
        id: supabaseUser.id,
        email: supabaseUser.email,
        phone: supabaseUser.phone,
        emailConfirmedAt:
            supabaseUser.emailConfirmedAt != null
                ? DateTime.parse(supabaseUser.emailConfirmedAt!)
                : null,
        phoneConfirmedAt:
            supabaseUser.phoneConfirmedAt != null
                ? DateTime.parse(supabaseUser.phoneConfirmedAt!)
                : null,
        createdAt: DateTime.parse(supabaseUser.createdAt),
        updatedAt:
            supabaseUser.updatedAt != null
                ? DateTime.parse(supabaseUser.updatedAt!)
                : DateTime.parse(supabaseUser.createdAt),
        userMetadata: supabaseUser.userMetadata,
        appMetadata: supabaseUser.appMetadata,
      );
    }

    // Fallback to stored user
    return _currentUser;
  }

  /// Get when the current token was obtained
  DateTime? get tokenObtainedAt => _tokenObtainedAt;

  /// Check if user is authenticated
  /// Prioritizes Supabase session over stored tokens
  bool get isAuthenticated {
    // First check if we have a valid Supabase session
    final supabaseSession = Supabase.instance.client.auth.currentSession;
    if (supabaseSession != null && !supabaseSession.isExpired) {
      return true;
    }

    // Fallback to stored tokens
    return _accessToken != null && _currentUser != null;
  }

  /// Initialize the client by loading stored tokens
  Future<void> initialize() async {
    await _loadStoredTokens();
  }

  /// Load stored tokens from SharedPreferences
  Future<void> _loadStoredTokens() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _accessToken = prefs.getString(_accessTokenKey);
      _refreshToken = prefs.getString(_refreshTokenKey);

      final userDataJson = prefs.getString(_userDataKey);
      if (userDataJson != null) {
        final userData = jsonDecode(userDataJson);
        _currentUser = ServerUser.fromJson(userData);
      }
    } catch (e) {
      debugPrint('Error loading stored tokens: $e');
    }
  }

  /// Store tokens in SharedPreferences
  Future<void> _storeTokens(
    String accessToken,
    String refreshToken,
    ServerUser user,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_accessTokenKey, accessToken);
      await prefs.setString(_refreshTokenKey, refreshToken);
      await prefs.setString(_userDataKey, jsonEncode(user.toJson()));

      _accessToken = accessToken;
      _refreshToken = refreshToken;
      _currentUser = user;
      _tokenObtainedAt = DateTime.now();

      debugPrint('Tokens stored successfully');
      debugPrint(
        'New access token (first 20 chars): ${accessToken.length > 20 ? accessToken.substring(0, 20) : accessToken}...',
      );
      debugPrint(
        'New refresh token (first 20 chars): ${refreshToken.isNotEmpty ? (refreshToken.length > 20 ? refreshToken.substring(0, 20) : refreshToken) : 'EMPTY'}...',
      );
    } catch (e) {
      debugPrint('Error storing tokens: $e');
    }
  }

  /// Clear stored tokens
  Future<void> _clearStoredTokens() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_accessTokenKey);
      await prefs.remove(_refreshTokenKey);
      await prefs.remove(_userDataKey);

      _accessToken = null;
      _refreshToken = null;
      _currentUser = null;
      _tokenObtainedAt = null;

      // Also sign out from Supabase client
      try {
        await Supabase.instance.client.auth.signOut();
        debugPrint('Successfully signed out from Supabase client');
      } catch (e) {
        debugPrint('Failed to sign out from Supabase client: $e');
      }
    } catch (e) {
      debugPrint('Error clearing tokens: $e');
    }
  }

  /// Create HTTP headers with authentication
  /// Always uses the latest Supabase token if available
  Map<String, String> _createHeaders({String? customToken}) {
    String? token = customToken;

    // If no custom token provided, get the latest Supabase token
    if (token == null) {
      final supabaseSession = Supabase.instance.client.auth.currentSession;
      if (supabaseSession != null) {
        token = supabaseSession.accessToken;
        debugPrint(
          'Using fresh Supabase token (expires: ${DateTime.fromMillisecondsSinceEpoch(supabaseSession.expiresAt! * 1000)})',
        );
      } else {
        token = _accessToken; // Fallback to stored token
        debugPrint('No Supabase session, using stored token');
      }
    }

    final headers = <String, String>{
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }

    return headers;
  }

  /// Make authenticated HTTP request with automatic token refresh
  Future<http.Response> _makeRequest(
    String method,
    String endpoint, {
    Map<String, dynamic>? body,
    String? customToken,
    Duration? timeout,
  }) async {
    final url = Uri.parse('$baseUrl$endpoint');
    final headers = _createHeaders(customToken: customToken);
    final requestTimeout = timeout ?? AuthServiceConfig.requestTimeout;

    try {
      late http.Response response;

      switch (method.toUpperCase()) {
        case 'GET':
          response = await _httpClient
              .get(url, headers: headers)
              .timeout(requestTimeout);
          break;
        case 'POST':
          final requestBody = body != null ? jsonEncode(body) : null;
          response = await _httpClient
              .post(url, headers: headers, body: requestBody)
              .timeout(requestTimeout);
          break;
        case 'PUT':
          final requestBody = body != null ? jsonEncode(body) : null;
          response = await _httpClient
              .put(url, headers: headers, body: requestBody)
              .timeout(requestTimeout);
          break;
        case 'DELETE':
          response = await _httpClient
              .delete(url, headers: headers)
              .timeout(requestTimeout);
          break;
        default:
          throw ArgumentError('Unsupported HTTP method: $method');
      }

      return response;
    } on SocketException {
      throw AbraAuthException(
        'Network error: Unable to connect to auth service',
      );
    } on http.ClientException catch (e) {
      throw AbraAuthException('HTTP error: ${e.message}');
    } on TimeoutException {
      throw AbraAuthException('Request timeout: Operation took too long');
    } catch (e) {
      throw AbraAuthException('Unexpected error: $e');
    }
  }

  /// Sign up with email and password
  Future<ServerAuthResult> signUpWithEmail({
    required String email,
    required String password,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      debugPrint('Signing up via server: $email');

      final body = {
        'email': email,
        'password': password,
        if (metadata != null) 'metadata': metadata,
      };

      final response = await _makeRequest(
        'POST',
        '${AuthServiceConfig.authEndpoint}/signup',
        body: body,
      );

      return _handleAuthResponse(response);
    } on AbraAuthException catch (e) {
      return ServerAuthResult.failure(e.message);
    } catch (e) {
      return ServerAuthResult.failure('Unexpected error: $e');
    }
  }

  /// Sign in with email and password
  Future<ServerAuthResult> signInWithEmail({
    required String email,
    required String password,
  }) async {
    try {
      debugPrint('Signing in via server: $email');
      debugPrint(
        'Auth endpoint: ${AuthServiceConfig.baseUrl}${AuthServiceConfig.authEndpoint}/login',
      );

      final body = {'email': email, 'password': password};

      final response = await _makeRequest(
        'POST',
        '${AuthServiceConfig.authEndpoint}/login',
        body: body,
      );

      debugPrint('Auth response status: ${response.statusCode}');
      debugPrint('Auth response body: ${response.body}');

      return _handleAuthResponse(response);
    } on AbraAuthException catch (e) {
      debugPrint('AbraAuthException during sign in: ${e.message}');
      return ServerAuthResult.failure(e.message);
    } catch (e) {
      debugPrint('Unexpected error during sign in: $e');
      return ServerAuthResult.failure('Unexpected error: $e');
    }
  }

  /// Handle authentication response from server
  Future<ServerAuthResult> _handleAuthResponse(http.Response response) async {
    try {
      final data = jsonDecode(response.body);
      debugPrint('Parsed auth response data: $data');

      if (response.statusCode == 200 || response.statusCode == 201) {
        // Handle Supabase-style response format
        if (data['access_token'] != null &&
            data['access_token'].toString().isNotEmpty) {
          final accessToken = data['access_token'] as String;
          final refreshToken = data['refresh_token'] as String? ?? '';

          debugPrint(
            'Access token received (first 50 chars): ${accessToken.length > 50 ? accessToken.substring(0, 50) : accessToken}...',
          );
          debugPrint('Refresh token available: ${refreshToken.isNotEmpty}');

          // Create user from the token's user_metadata or from separate user field
          ServerUser user;
          if (data['user'] != null && data['user'] is Map) {
            user = ServerUser.fromJson(data['user']);
          } else {
            // Extract user info from user_metadata in the response or create minimal user
            final userMetadata =
                data['user_metadata'] as Map<String, dynamic>? ?? {};
            final now = DateTime.now();

            user = ServerUser(
              id: data['sub'] as String? ?? 'unknown',
              email:
                  userMetadata['email'] as String? ?? data['email'] as String?,
              phone: userMetadata['phone'] as String?,
              emailConfirmedAt:
                  userMetadata['email_verified'] == true ? now : null,
              phoneConfirmedAt:
                  userMetadata['phone_verified'] == true ? now : null,
              createdAt: now,
              updatedAt: now,
              userMetadata: userMetadata,
              appMetadata: data['app_metadata'] as Map<String, dynamic>?,
            );
          }

          // Store tokens
          await _storeTokens(accessToken, refreshToken, user);

          debugPrint('Authentication successful for user: ${user.email}');
          return ServerAuthResult.success(
            user: user,
            accessToken: accessToken,
            refreshToken: refreshToken,
          );
        } else if (data['message']?.toString().contains('confirmation') ==
            true) {
          return ServerAuthResult.confirmationRequired();
        } else {
          final errorMsg =
              data['error'] ?? data['message'] ?? 'Authentication failed';
          debugPrint('Auth failed with error: $errorMsg');
          return ServerAuthResult.failure(errorMsg);
        }
      } else if (response.statusCode == 400) {
        final errorMsg = data['error'] ?? data['message'] ?? 'Invalid request';
        debugPrint('Bad request (400): $errorMsg');
        return ServerAuthResult.failure(_getFriendlyErrorMessage(errorMsg));
      } else if (response.statusCode == 401) {
        final errorMsg =
            data['error'] ?? data['message'] ?? 'Invalid credentials';
        debugPrint('Unauthorized (401): $errorMsg');
        return ServerAuthResult.failure(_getFriendlyErrorMessage(errorMsg));
      } else {
        final errorMsg =
            data['error'] ??
            data['message'] ??
            'Server error: ${response.statusCode}';
        debugPrint('Server error (${response.statusCode}): $errorMsg');
        return ServerAuthResult.failure(_getFriendlyErrorMessage(errorMsg));
      }
    } catch (e) {
      debugPrint('Failed to parse auth response: $e');
      return ServerAuthResult.failure('Failed to parse server response: $e');
    }
  }

  /// Sign out
  Future<bool> signOut() async {
    try {
      if (_accessToken != null) {
        await _makeRequest('POST', '${AuthServiceConfig.authEndpoint}/logout');
      }

      await _clearStoredTokens();
      return true;
    } catch (e) {
      debugPrint('Error signing out: $e');
      await _clearStoredTokens(); // Clear tokens even if server call fails
      return false;
    }
  }

  /// Refresh access token using stored refresh token
  /// Note: This is kept for compatibility but should be used sparingly
  /// The server should handle session persistence automatically
  Future<ServerAuthResult> refreshAccessToken() async {
    try {
      if (_refreshToken == null) {
        return ServerAuthResult.failure('No refresh token available');
      }

      debugPrint('Refreshing access token via server (manual refresh)');
      debugPrint(
        'Current refresh token (first 20 chars): ${_refreshToken!.length > 20 ? _refreshToken!.substring(0, 20) : _refreshToken}...',
      );

      final body = {'refresh_token': _refreshToken};

      final response = await _makeRequest(
        'POST',
        '${AuthServiceConfig.authEndpoint}/token/refresh',
        body: body,
      );

      debugPrint('Refresh response status: ${response.statusCode}');
      debugPrint('Refresh response body: ${response.body}');

      final result = await _handleAuthResponse(response);

      if (result.isSuccess) {
        debugPrint('Token refresh successful, new tokens stored');
      } else {
        debugPrint('Token refresh failed: ${result.error}');
      }

      return result;
    } on AbraAuthException catch (e) {
      debugPrint('AbraAuthException during token refresh: ${e.message}');
      return ServerAuthResult.failure(e.message);
    } catch (e) {
      debugPrint('Unexpected error during token refresh: $e');
      return ServerAuthResult.failure('Unexpected error: $e');
    }
  }

  /// Send OTP to phone number
  Future<ServerAuthResult> sendPhoneOtp(String phoneNumber) async {
    try {
      debugPrint('Sending OTP to phone: $phoneNumber');

      final body = {
        'phoneNumber': phoneNumber,
        'createUser': true, // Allow creating user if doesn't exist
      };

      final response = await _makeRequest(
        'POST',
        '${AuthServiceConfig.authEndpoint}/phone/send-otp',
        body: body,
      );

      if (response.statusCode == 200) {
        return ServerAuthResult.success(
          user: ServerUser(
            id: 'pending',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            phone: phoneNumber,
          ),
          accessToken: 'pending',
          refreshToken: 'pending',
        );
      } else {
        final data = jsonDecode(response.body);
        return ServerAuthResult.failure(
          data['message'] ?? 'Failed to send OTP',
        );
      }
    } on AbraAuthException catch (e) {
      return ServerAuthResult.failure(e.message);
    } catch (e) {
      return ServerAuthResult.failure('Unexpected error: $e');
    }
  }

  /// Verify phone OTP and complete authentication
  Future<ServerAuthResult> verifyPhoneOtp({
    required String phoneNumber,
    required String otp,
  }) async {
    try {
      debugPrint('Verifying OTP for phone: $phoneNumber');

      final body = {'phoneNumber': phoneNumber, 'otpCode': otp};

      final response = await _makeRequest(
        'POST',
        '${AuthServiceConfig.authEndpoint}/phone/signin',
        body: body,
      );

      return _handleAuthResponse(response);
    } on AbraAuthException catch (e) {
      return ServerAuthResult.failure(e.message);
    } catch (e) {
      return ServerAuthResult.failure('Unexpected error: $e');
    }
  }

  /// Get OAuth URL for social login (Google, etc.)
  Future<OAuthUrlResult> getOAuthUrl({
    required String provider,
    String? redirectTo,
    Map<String, dynamic>? options,
  }) async {
    try {
      debugPrint('Getting OAuth URL for provider: $provider');

      final body = {
        'provider': provider,
        if (redirectTo != null) 'redirect_to': redirectTo,
        if (options != null) 'options': options,
      };

      final response = await _makeRequest(
        'POST',
        '${AuthServiceConfig.authEndpoint}/oauth',
        body: body,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return OAuthUrlResult.success(
          url: data['url'] as String,
          provider: data['provider'] as String,
        );
      } else {
        final data = jsonDecode(response.body);
        return OAuthUrlResult.failure(
          data['error'] ?? 'Failed to get OAuth URL',
        );
      }
    } on AbraAuthException catch (e) {
      return OAuthUrlResult.failure(e.message);
    } catch (e) {
      return OAuthUrlResult.failure('Unexpected error: $e');
    }
  }

  /// Handle OAuth callback with authorization code
  Future<ServerAuthResult> handleOAuthCallback({
    required String code,
    required String provider,
  }) async {
    try {
      debugPrint('Handling OAuth callback for provider: $provider');

      // For OAuth callback, we need to use query parameters
      final endpoint =
          '${AuthServiceConfig.authEndpoint}/oauth/callback?code=$code&provider=$provider';

      final response = await _makeRequest('POST', endpoint);

      return _handleAuthResponse(response);
    } on AbraAuthException catch (e) {
      return ServerAuthResult.failure(e.message);
    } catch (e) {
      return ServerAuthResult.failure('Unexpected error: $e');
    }
  }

  /// Get user profile from abra-servers auth-service
  /// The server handles all authentication and database queries
  Future<AbraUserProfile?> getUserProfile({
    String? userId,
    String? token,
  }) async {
    try {
      debugPrint('Fetching profile from abra-servers');
      debugPrint(
        'Profile endpoint: $baseUrl${AuthServiceConfig.profileEndpoint}',
      );

      // Ensure we have a valid token
      if (!isAuthenticated && token == null) {
        throw AbraAuthException('Not authenticated: Please sign in first');
      }

      final tokenToUse = token ?? _accessToken;
      debugPrint(
        'Using token (first 50 chars): ${tokenToUse?.substring(0, 50)}...',
      );

      final response = await _makeRequest(
        'GET',
        AuthServiceConfig.profileEndpoint,
        customToken: token,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        debugPrint('Profile data received from server: $data');

        // Handle both single profile and array responses from server
        if (data is List && data.isNotEmpty) {
          return AbraUserProfile.fromJson(data.first);
        } else if (data is Map<String, dynamic>) {
          return AbraUserProfile.fromJson(data);
        } else {
          debugPrint('Unexpected response format from server: $data');
          return null;
        }
      } else if (response.statusCode == 401) {
        // For 401 errors, we need to be more careful about clearing tokens
        // Let's first check if this is a real auth failure or endpoint issue
        debugPrint('Profile request unauthorized (401)');
        debugPrint('Response body: ${response.body}');
        debugPrint('Response headers: ${response.headers}');

        // Check if the error message indicates a real auth failure
        final responseBody = response.body.toLowerCase();
        final authHeader =
            response.headers['www-authenticate']?.toLowerCase() ?? '';

        if (responseBody.contains('token') ||
            responseBody.contains('unauthorized') ||
            responseBody.contains('expired') ||
            authHeader.contains('invalid_token') ||
            authHeader.contains('expired') ||
            authHeader.contains('token')) {
          // Safeguard: Don't clear tokens that were obtained very recently (within 30 seconds)
          // This prevents clearing tokens immediately after login due to cached/delayed requests
          final now = DateTime.now();
          final tokenAge =
              _tokenObtainedAt != null
                  ? now.difference(_tokenObtainedAt!).inSeconds
                  : 999999; // Very old if no timestamp

          if (tokenAge < 30) {
            debugPrint(
              'Token appears invalid but was obtained recently ($tokenAge seconds ago), not clearing',
            );
            throw AbraAuthException('Profile endpoint error: ${response.body}');
          }

          debugPrint(
            'Token appears to be invalid, clearing tokens (age: $tokenAge seconds)',
          );
          debugPrint('Auth header: ${response.headers['www-authenticate']}');
          await _clearStoredTokens();
          throw AbraAuthException('Session expired: Please sign in again');
        } else {
          // This might be an endpoint issue, not an auth issue
          debugPrint(
            '401 error might be endpoint-related, not clearing tokens',
          );
          throw AbraAuthException('Profile endpoint error: ${response.body}');
        }
      } else if (response.statusCode == 404) {
        debugPrint(
          'Profile not found on server, user may need to complete profile setup',
        );
        return null;
      } else {
        throw AbraAuthException(
          'Server error: ${response.statusCode} - ${response.body}',
        );
      }
    } on AbraAuthException {
      rethrow;
    } catch (e) {
      throw AbraAuthException('Unexpected error: $e');
    }
  }

  /// Link a broker to the current user's profile
  /// The server handles all validation and database updates
  Future<bool> linkBroker({
    required String brokerId,
    String? userId,
    String? token,
  }) async {
    try {
      debugPrint('Linking broker via abra-servers: $brokerId');

      final body = {'brokerId': brokerId};

      final response = await _makeRequest(
        'POST',
        AuthServiceConfig.linkBrokerEndpoint,
        body: body,
        customToken: token,
      );

      if (response.statusCode == 200 || response.statusCode == 204) {
        debugPrint('Broker linked successfully via server');
        return true;
      } else if (response.statusCode == 401) {
        throw AbraAuthException('Unauthorized: Please sign in again');
      } else if (response.statusCode == 400) {
        throw AbraAuthException('Invalid broker ID or request');
      } else {
        throw AbraAuthException(
          'Server error: ${response.statusCode} - ${response.body}',
        );
      }
    } on AbraAuthException {
      rethrow;
    } catch (e) {
      throw AbraAuthException('Unexpected error: $e');
    }
  }

  /// Check if the auth service is healthy
  Future<bool> checkHealth() async {
    try {
      final response = await _makeRequest(
        'GET',
        AuthServiceConfig.healthEndpoint,
        timeout: AuthServiceConfig.healthCheckTimeout,
      );

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('Auth service health check failed: $e');
      return false;
    }
  }

  /// Get user-friendly error message from raw error string
  String _getFriendlyErrorMessage(String error) {
    final errorLower = error.toLowerCase();

    if (errorLower.contains('invalid login credentials') ||
        errorLower.contains('invalid credentials')) {
      return 'Invalid email or password';
    } else if (errorLower.contains('email not confirmed')) {
      return 'Please check your email and click the confirmation link';
    } else if (errorLower.contains('phone not confirmed')) {
      return 'Please verify your phone number';
    } else if (errorLower.contains('invalid phone number')) {
      return 'Please enter a valid phone number';
    } else if (errorLower.contains('invalid otp')) {
      return 'Invalid verification code. Please try again';
    } else if (errorLower.contains('otp expired')) {
      return 'Verification code has expired. Please request a new one';
    } else if (errorLower.contains('too many requests')) {
      return 'Too many attempts. Please wait before trying again';
    } else if (errorLower.contains('network') ||
        errorLower.contains('connection')) {
      return 'Network error. Please check your connection';
    } else if (errorLower.contains('timeout')) {
      return 'Request timed out. Please try again';
    }

    return error; // Return original error if no friendly message found
  }

  /// Get user-friendly error message from server auth result
  static String getAuthErrorMessage(ServerAuthResult result) {
    if (result.error == null) return 'Unknown error occurred';

    final error = result.error!.toLowerCase();

    switch (error) {
      case 'invalid login credentials':
      case 'invalid credentials':
        return 'Invalid email or password';
      case 'email not confirmed':
        return 'Please check your email and click the confirmation link';
      case 'phone not confirmed':
        return 'Please verify your phone number';
      case 'invalid phone number':
        return 'Please enter a valid phone number';
      case 'invalid otp':
        return 'Invalid verification code. Please try again';
      case 'otp expired':
        return 'Verification code has expired. Please request a new one';
      case 'too many requests':
        return 'Too many attempts. Please wait before trying again';
      default:
        if (error.contains('network')) {
          return 'Network error. Please check your connection';
        }
        if (error.contains('timeout')) {
          return 'Request timed out. Please try again';
        }
        return result.error!;
    }
  }

  /// Static convenience methods for easier migration from AuthService

  /// Static sign up method
  static Future<bool> staticSignUp({
    required String email,
    required String password,
    Map<String, dynamic>? metadata,
  }) async {
    final client = AuthClient();
    await client.initialize();
    final result = await client.signUpWithEmail(
      email: email,
      password: password,
      metadata: metadata,
    );
    return result.isSuccess;
  }

  /// Static sign in method
  static Future<bool> staticSignIn({
    required String email,
    required String password,
  }) async {
    final client = AuthClient();
    await client.initialize();
    final result = await client.signInWithEmail(
      email: email,
      password: password,
    );
    return result.isSuccess;
  }

  /// Static phone sign in method
  static Future<void> staticSignInWithPhone(String phoneNumber) async {
    final client = AuthClient();
    await client.initialize();
    final result = await client.sendPhoneOtp(phoneNumber);
    if (result.isFailure) {
      throw AbraAuthException(result.error ?? 'Failed to send OTP');
    }
  }

  /// Static phone OTP verification method
  static Future<void> staticVerifyPhoneOtp(
    String phoneNumber,
    String otp,
  ) async {
    final client = AuthClient();
    await client.initialize();
    final result = await client.verifyPhoneOtp(
      phoneNumber: phoneNumber,
      otp: otp,
    );
    if (result.isFailure) {
      throw AbraAuthException(result.error ?? 'Failed to verify OTP');
    }
  }

  /// Static access token getter
  static Future<String?> staticGetAccessToken() async {
    final client = AuthClient();
    await client.initialize();
    return client.accessToken;
  }

  /// Static login status checker
  static Future<bool> staticIsLoggedIn() async {
    final client = AuthClient();
    await client.initialize();
    return client.isAuthenticated;
  }

  /// Static sign out method
  static Future<void> staticSignOut() async {
    final client = AuthClient();
    await client.initialize();
    await client.signOut();
  }

  /// Static profile getter
  static Future<Map<String, dynamic>?> staticGetProfile() async {
    final client = AuthClient();
    await client.initialize();
    final profile = await client.getUserProfile();
    return profile?.toJson();
  }

  /// Static OAuth URL getter
  static Future<OAuthUrlResult> staticGetOAuthUrl({
    required String provider,
    String? redirectTo,
    Map<String, dynamic>? options,
  }) async {
    final client = AuthClient();
    await client.initialize();
    return await client.getOAuthUrl(
      provider: provider,
      redirectTo: redirectTo,
      options: options,
    );
  }

  /// Static OAuth callback handler
  static Future<ServerAuthResult> staticHandleOAuthCallback({
    required String code,
    required String provider,
  }) async {
    final client = AuthClient();
    await client.initialize();
    return await client.handleOAuthCallback(code: code, provider: provider);
  }

  /// Test API endpoint connectivity and token validation
  Future<void> testApiEndpoint() async {
    try {
      debugPrint('Testing API endpoint connectivity...');
      debugPrint('Base URL: $baseUrl');
      debugPrint('Profile endpoint: ${AuthServiceConfig.profileEndpoint}');
      debugPrint('Full URL: $baseUrl${AuthServiceConfig.profileEndpoint}');

      // Test without authentication first
      final response = await _httpClient
          .get(Uri.parse('$baseUrl${AuthServiceConfig.profileEndpoint}'))
          .timeout(AuthServiceConfig.requestTimeout);

      debugPrint('Test response status: ${response.statusCode}');
      debugPrint('Test response body: ${response.body}');
      debugPrint('Test response headers: ${response.headers}');

      // Test other endpoints to see which ones work
      await _testEndpoint('/api/auth/health', 'Health endpoint');
      await _testEndpoint('/api/posts', 'Posts endpoint');
      await _testEndpoint('/api/users/search', 'Users search endpoint');
    } catch (e) {
      debugPrint('Test API endpoint error: $e');
    }
  }

  /// Test a specific endpoint
  Future<void> _testEndpoint(String endpoint, String name) async {
    try {
      final response = await _httpClient
          .get(Uri.parse('$baseUrl$endpoint'))
          .timeout(AuthServiceConfig.requestTimeout);
      debugPrint(
        '$name: ${response.statusCode} - ${response.body.length} bytes',
      );
    } catch (e) {
      debugPrint('$name error: $e');
    }
  }

  /// Dispose resources
  void dispose() {
    _httpClient.close();
  }
}

/// User profile model for abra-servers auth-service
class AbraUserProfile {
  final String userId;
  final String fullName;
  final String username;
  final String? brokerId;
  final bool verified;
  final DateTime createdAt;

  AbraUserProfile({
    required this.userId,
    required this.fullName,
    required this.username,
    this.brokerId,
    required this.verified,
    required this.createdAt,
  });

  factory AbraUserProfile.fromJson(Map<String, dynamic> json) {
    return AbraUserProfile(
      userId: json['user_id'] ?? json['User_id'] ?? '',
      fullName: json['full_name'] ?? json['Full_name'] ?? '',
      username: json['username'] ?? json['Username'] ?? '',
      brokerId: json['broker_id'] ?? json['Broker_id'],
      verified: json['verified'] ?? json['Verified'] ?? false,
      createdAt:
          DateTime.tryParse(json['created_at'] ?? json['Created_at'] ?? '') ??
          DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'full_name': fullName,
      'username': username,
      'broker_id': brokerId,
      'verified': verified,
      'created_at': createdAt.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'AbraUserProfile(userId: $userId, fullName: $fullName, username: $username, brokerId: $brokerId, verified: $verified)';
  }
}

/// Server authentication result
class ServerAuthResult {
  final bool success;
  final ServerUser? user;
  final String? accessToken;
  final String? refreshToken;
  final String? error;
  final bool needsConfirmation;

  const ServerAuthResult._({
    required this.success,
    this.user,
    this.accessToken,
    this.refreshToken,
    this.error,
    this.needsConfirmation = false,
  });

  factory ServerAuthResult.success({
    required ServerUser user,
    required String accessToken,
    required String refreshToken,
  }) {
    return ServerAuthResult._(
      success: true,
      user: user,
      accessToken: accessToken,
      refreshToken: refreshToken,
    );
  }

  factory ServerAuthResult.failure(String error) {
    return ServerAuthResult._(success: false, error: error);
  }

  factory ServerAuthResult.confirmationRequired() {
    return const ServerAuthResult._(success: true, needsConfirmation: true);
  }

  bool get isSuccess => success && !needsConfirmation;
  bool get isFailure => !success;
}

/// Server user model
class ServerUser {
  final String id;
  final String? email;
  final String? phone;
  final DateTime? emailConfirmedAt;
  final DateTime? phoneConfirmedAt;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? userMetadata;
  final Map<String, dynamic>? appMetadata;

  const ServerUser({
    required this.id,
    this.email,
    this.phone,
    this.emailConfirmedAt,
    this.phoneConfirmedAt,
    required this.createdAt,
    required this.updatedAt,
    this.userMetadata,
    this.appMetadata,
  });

  factory ServerUser.fromJson(Map<String, dynamic> json) {
    return ServerUser(
      id: json['id'] as String,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      emailConfirmedAt:
          json['email_confirmed_at'] != null
              ? DateTime.parse(json['email_confirmed_at'] as String)
              : null,
      phoneConfirmedAt:
          json['phone_confirmed_at'] != null
              ? DateTime.parse(json['phone_confirmed_at'] as String)
              : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      userMetadata: json['user_metadata'] as Map<String, dynamic>?,
      appMetadata: json['app_metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'phone': phone,
      'email_confirmed_at': emailConfirmedAt?.toIso8601String(),
      'phone_confirmed_at': phoneConfirmedAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'user_metadata': userMetadata,
      'app_metadata': appMetadata,
    };
  }
}

/// Result class for OAuth URL requests
class OAuthUrlResult {
  final bool isSuccess;
  final String? url;
  final String? provider;
  final String? error;

  const OAuthUrlResult._({
    required this.isSuccess,
    this.url,
    this.provider,
    this.error,
  });

  factory OAuthUrlResult.success({
    required String url,
    required String provider,
  }) {
    return OAuthUrlResult._(isSuccess: true, url: url, provider: provider);
  }

  factory OAuthUrlResult.failure(String error) {
    return OAuthUrlResult._(isSuccess: false, error: error);
  }
}

/// Custom exception for abra auth service errors
class AbraAuthException implements Exception {
  final String message;

  const AbraAuthException(this.message);

  @override
  String toString() => 'AbraAuthException: $message';
}
