import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../core/constants.dart';
import 'auth_client.dart';
import '../settings/user/model_profile.dart';

/// Enhanced ProfileService that uses abra-servers endpoints
/// Handles user profile management, preferences, and avatar operations
class ProfileService {
  static final ProfileService _instance = ProfileService._();
  factory ProfileService() => _instance;
  ProfileService._();

  final AuthClient _authClient = AuthClient();
  final http.Client _httpClient = http.Client();

  /// Get base URL for abra-servers
  String get baseUrl => AuthServiceConfig.baseUrl;

  /// Create headers with authentication
  Future<Map<String, String>> _createHeaders() async {
    final headers = <String, String>{
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    // Ensure AuthClient is initialized
    await _authClient.initialize();

    final token = _authClient.accessToken;
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
      debugPrint(
        'Using auth token for profile request (first 20 chars): ${token.substring(0, 20)}...',
      );
    } else {
      debugPrint('No auth token available for profile request');
    }

    return headers;
  }

  /// Get current user profile from server
  Future<Profile?> getUserProfile() async {
    try {
      debugPrint('Fetching user profile from server');

      final headers = await _createHeaders();
      final response = await _httpClient
          .get(Uri.parse('$baseUrl/api/user/me'), headers: headers)
          .timeout(AuthServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        debugPrint('Profile data received: $data');

        // Convert server response to Profile model
        return Profile.fromMap(data);
      } else if (response.statusCode == 401) {
        throw Exception('Unauthorized: Please sign in again');
      } else if (response.statusCode == 404) {
        debugPrint('Profile not found, user may need to complete setup');
        return null;
      } else {
        throw Exception('Server error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching profile: $e');
      throw Exception('Failed to load profile: $e');
    }
  }

  /// Update user profile on server
  Future<Profile?> updateUserProfile({
    String? fullName,
    String? username,
    String? avatarUrl,
    String? phone,
    String? gender,
    String? countryCode,
    String? region,
    String? city,
    String? timezone,
    String? locale,
    String? preferredLanguage,
    String? preferredCurrency,
    String? themeMode,
  }) async {
    try {
      debugPrint('Updating user profile on server');

      final body = <String, dynamic>{};

      // Only include non-null values
      if (fullName != null) body['fullName'] = fullName;
      if (username != null) body['username'] = username;
      if (avatarUrl != null) body['avatarUrl'] = avatarUrl;
      if (phone != null) body['phone'] = phone;
      if (gender != null) body['gender'] = gender;
      if (countryCode != null) body['countryCode'] = countryCode;
      if (region != null) body['region'] = region;
      if (city != null) body['city'] = city;
      if (timezone != null) body['timezone'] = timezone;
      if (locale != null) body['locale'] = locale;
      if (preferredLanguage != null) {
        body['preferredLanguage'] = preferredLanguage;
      }
      if (preferredCurrency != null) {
        body['preferredCurrency'] = preferredCurrency;
      }
      if (themeMode != null) body['themeMode'] = themeMode;

      final headers = await _createHeaders();
      final response = await _httpClient
          .put(
            Uri.parse('$baseUrl/api/user/me'),
            headers: headers,
            body: jsonEncode(body),
          )
          .timeout(AuthServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        debugPrint('Profile updated successfully: $data');
        return Profile.fromMap(data);
      } else if (response.statusCode == 400) {
        final error = jsonDecode(response.body);
        throw Exception(error['message'] ?? 'Invalid request');
      } else if (response.statusCode == 401) {
        throw Exception('Unauthorized: Please sign in again');
      } else if (response.statusCode == 409) {
        throw Exception('Username already exists');
      } else {
        throw Exception('Server error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error updating profile: $e');
      throw Exception('Failed to update profile: $e');
    }
  }

  /// Check if username is available
  /// Note: This would need a dedicated endpoint on the server
  /// For now, we'll try to update and catch conflicts
  Future<bool> isUsernameAvailable(String username) async {
    try {
      debugPrint('Checking username availability: $username');

      // For now, we'll assume it's available and let the update endpoint handle conflicts
      // Add dedicated username check endpoint to abra-servers
      return true;
    } catch (e) {
      debugPrint('Error checking username availability: $e');
      return false;
    }
  }

  /// Upload avatar image
  /// Note: This would need a file upload endpoint on the server
  Future<String?> uploadAvatar(File imageFile) async {
    try {
      debugPrint('Uploading avatar to server');

      // Implement actual file upload to abra-servers
      // For now, return a placeholder URL
      debugPrint('Avatar upload not yet implemented in abra-servers');
      return null;
    } catch (e) {
      debugPrint('Error uploading avatar: $e');
      return null;
    }
  }

  /// Delete old avatar
  /// Note: This would need a file deletion endpoint on the server
  Future<void> deleteAvatar(String avatarUrl) async {
    try {
      debugPrint('Deleting avatar from server: $avatarUrl');

      // Implement actual file deletion on abra-servers
      debugPrint('Avatar deletion not yet implemented in abra-servers');
    } catch (e) {
      debugPrint('Error deleting avatar: $e');
    }
  }

  /// Get user preferences (stored locally for now)
  /// Move to server-side storage
  Future<Map<String, dynamic>> getUserPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final prefsJson = prefs.getString('user_preferences');
      if (prefsJson != null) {
        return jsonDecode(prefsJson) as Map<String, dynamic>;
      }
      return {};
    } catch (e) {
      debugPrint('Error loading preferences: $e');
      return {};
    }
  }

  /// Update user preference (stored locally for now)
  /// Move to server-side storage
  Future<void> updatePreference(String key, dynamic value) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentPrefs = await getUserPreferences();
      currentPrefs[key] = value;
      await prefs.setString('user_preferences', jsonEncode(currentPrefs));
      debugPrint('Preference updated: $key = $value');
    } catch (e) {
      debugPrint('Error updating preference: $e');
      throw Exception('Failed to update preference: $e');
    }
  }

  /// Update multiple preferences (stored locally for now)
  ///  Move to server-side storage
  Future<void> updatePreferences(Map<String, dynamic> preferences) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentPrefs = await getUserPreferences();
      currentPrefs.addAll(preferences);
      await prefs.setString('user_preferences', jsonEncode(currentPrefs));
      debugPrint('Preferences updated: $preferences');
    } catch (e) {
      debugPrint('Error updating preferences: $e');
      throw Exception('Failed to update preferences: $e');
    }
  }

  /// Get profile with avatar URL
  Future<(Profile?, String?)> getProfileWithAvatar() async {
    try {
      final profile = await getUserProfile();
      final avatarUrl = profile?.avatarUrl;
      return (profile, avatarUrl);
    } catch (e) {
      debugPrint('Error getting profile with avatar: $e');
      return (null, null);
    }
  }

  /// Link broker to user profile
  Future<bool> linkBroker(String brokerId) async {
    try {
      debugPrint('Linking broker to profile: $brokerId');

      final body = {'brokerId': brokerId};

      final headers = await _createHeaders();
      final response = await _httpClient
          .post(
            Uri.parse('$baseUrl/api/auth/profile/link-broker'),
            headers: headers,
            body: jsonEncode(body),
          )
          .timeout(AuthServiceConfig.requestTimeout);

      if (response.statusCode == 200) {
        debugPrint('Broker linked successfully');
        return true;
      } else if (response.statusCode == 400) {
        final error = jsonDecode(response.body);
        throw Exception(error['message'] ?? 'Invalid broker ID');
      } else if (response.statusCode == 401) {
        throw Exception('Unauthorized: Please sign in again');
      } else {
        throw Exception('Server error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error linking broker: $e');
      throw Exception('Failed to link broker: $e');
    }
  }

  /// Dispose resources
  void dispose() {
    _httpClient.close();
  }
}
