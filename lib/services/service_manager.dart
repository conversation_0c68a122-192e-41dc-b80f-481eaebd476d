import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'market_service.dart';
import 'auth_client.dart';
import '../brokers/services/broker_service.dart';
import '../presentation/portifolio/portfolio_service.dart';
import '../presentation/symbol/symbol_service.dart';

/// Enhanced service manager that coordinates all services
/// Provides centralized initialization, configuration, and lifecycle management
class ServiceManager {
  static final ServiceManager _instance = ServiceManager._();
  factory ServiceManager() => _instance;
  ServiceManager._();

  // Service instances
  late final MarketService _marketService;
  late final SymbolService _symbolService;
  late final AuthClient _authClient;
  late final BrokerService _brokerService;
  late final PortfolioService _portfolioService;

  // State management
  ServiceManagerState _state = ServiceManagerState.uninitialized;
  final StreamController<ServiceManagerEvent> _eventController =
      StreamController.broadcast();

  // Configuration
  ServiceManagerConfig? _config;

  // Health monitoring
  Timer? _healthCheckTimer;
  final Map<String, ServiceHealthStatus> _serviceHealth = {};

  /// Get current state
  ServiceManagerState get state => _state;

  /// Get service health status
  Map<String, ServiceHealthStatus> get serviceHealth =>
      Map.from(_serviceHealth);

  /// Stream of manager events
  Stream<ServiceManagerEvent> get events => _eventController.stream;

  /// Initialize all services
  Future<void> initialize({required ServiceManagerConfig config}) async {
    try {
      _config = config;
      _setState(ServiceManagerState.initializing);
      _emitEvent(ServiceManagerEvent.initializationStarted());

      // Initialize services in order
      await _initializeMarketService();
      await _initializeSymbolService();
      await _initializeAuthClient();
      await _initializeBrokerService();
      await _initializePortfolioService();

      // Start health monitoring
      _startHealthMonitoring();

      _setState(ServiceManagerState.initialized);
      _emitEvent(ServiceManagerEvent.initializationCompleted());

      debugPrint('Enhanced Service Manager initialized successfully');
    } catch (e) {
      _setState(ServiceManagerState.error);
      _emitEvent(ServiceManagerEvent.initializationFailed(e.toString()));
      debugPrint('Service Manager initialization failed: $e');
      rethrow;
    }
  }

  /// Get market service
  MarketService get marketService {
    _ensureInitialized();
    return _marketService;
  }

  /// Get symbol service
  SymbolService get symbolService {
    _ensureInitialized();
    return _symbolService;
  }

  /// Get auth client
  AuthClient get authClient {
    _ensureInitialized();
    return _authClient;
  }

  /// Get broker service
  BrokerService get brokerService {
    _ensureInitialized();
    return _brokerService;
  }

  /// Get portfolio service
  PortfolioService get portfolioService {
    _ensureInitialized();
    return _portfolioService;
  }

  /// Check if all services are healthy
  bool get isHealthy {
    return _serviceHealth.values.every(
      (status) => status == ServiceHealthStatus.healthy,
    );
  }

  /// Get service configuration
  ServiceManagerConfig? get config => _config;

  /// Update service configuration
  Future<void> updateConfig(ServiceManagerConfig newConfig) async {
    _config = newConfig;
    _emitEvent(ServiceManagerEvent.configurationUpdated(newConfig));

    // Apply new configuration to services
    await _applyConfiguration();
  }

  /// Restart services
  Future<void> restart() async {
    try {
      _setState(ServiceManagerState.restarting);
      _emitEvent(ServiceManagerEvent.restartStarted());

      await _shutdownServices();
      await initialize(config: _config!);

      _emitEvent(ServiceManagerEvent.restartCompleted());
    } catch (e) {
      _setState(ServiceManagerState.error);
      _emitEvent(ServiceManagerEvent.restartFailed(e.toString()));
      rethrow;
    }
  }

  /// Shutdown all services
  Future<void> shutdown() async {
    try {
      _setState(ServiceManagerState.shuttingDown);
      _emitEvent(ServiceManagerEvent.shutdownStarted());

      await _shutdownServices();

      _setState(ServiceManagerState.shutdown);
      _emitEvent(ServiceManagerEvent.shutdownCompleted());

      debugPrint('Enhanced Service Manager shut down successfully');
    } catch (e) {
      _setState(ServiceManagerState.error);
      _emitEvent(ServiceManagerEvent.shutdownFailed(e.toString()));
      debugPrint('Service Manager shutdown failed: $e');
    }
  }

  /// Get service statistics
  ServiceStatistics getServiceStatistics() {
    return ServiceStatistics(
      marketService: _getMarketServiceStats(),
      authService: _getAuthServiceStats(),
      brokerService: _getBrokerServiceStats(),
      portfolioService: _getPortfolioServiceStats(),
      healthyServices:
          _serviceHealth.values
              .where((s) => s == ServiceHealthStatus.healthy)
              .length,
      totalServices: _serviceHealth.length,
      uptime: _getUptime(),
    );
  }

  /// Initialize market service
  Future<void> _initializeMarketService() async {
    _marketService = MarketService();
    _updateServiceHealth('market', ServiceHealthStatus.initializing);

    try {
      await _marketService.initialize();
      _updateServiceHealth('market', ServiceHealthStatus.healthy);
      debugPrint('Market service initialized');
    } catch (e) {
      _updateServiceHealth('market', ServiceHealthStatus.unhealthy);
      throw Exception('Market service initialization failed: $e');
    }
  }

  /// Initialize symbol service
  Future<void> _initializeSymbolService() async {
    _symbolService = SymbolService();
    _updateServiceHealth('symbol', ServiceHealthStatus.initializing);

    try {
      // SymbolService doesn't need explicit initialization
      _updateServiceHealth('symbol', ServiceHealthStatus.healthy);
      debugPrint('Symbol service initialized');
    } catch (e) {
      _updateServiceHealth('symbol', ServiceHealthStatus.unhealthy);
      throw Exception('Symbol service initialization failed: $e');
    }
  }

  /// Initialize auth client
  Future<void> _initializeAuthClient() async {
    _authClient = AuthClient();
    _updateServiceHealth('auth', ServiceHealthStatus.initializing);

    try {
      await _authClient.initialize();
      _updateServiceHealth('auth', ServiceHealthStatus.healthy);
      debugPrint('Auth client initialized');
    } catch (e) {
      _updateServiceHealth('auth', ServiceHealthStatus.unhealthy);
      throw Exception('Auth client initialization failed: $e');
    }
  }

  /// Initialize broker service
  Future<void> _initializeBrokerService() async {
    _brokerService = BrokerService();
    _updateServiceHealth('broker', ServiceHealthStatus.initializing);

    try {
      await _brokerService.initialize(
        symbolService: _symbolService,
        authClient: _authClient,
      );
      _updateServiceHealth('broker', ServiceHealthStatus.healthy);
      debugPrint('Broker service initialized');
    } catch (e) {
      _updateServiceHealth('broker', ServiceHealthStatus.unhealthy);
      throw Exception('Broker service initialization failed: $e');
    }
  }

  /// Initialize portfolio service
  Future<void> _initializePortfolioService() async {
    _portfolioService = PortfolioService();
    _updateServiceHealth('portfolio', ServiceHealthStatus.initializing);

    try {
      await _portfolioService.initialize(
        symbolService: _symbolService,
        brokerService: _brokerService,
        authClient: _authClient,
      );
      _updateServiceHealth('portfolio', ServiceHealthStatus.healthy);
      debugPrint('Portfolio service initialized');
    } catch (e) {
      _updateServiceHealth('portfolio', ServiceHealthStatus.unhealthy);
      throw Exception('Portfolio service initialization failed: $e');
    }
  }

  /// Start health monitoring
  void _startHealthMonitoring() {
    _healthCheckTimer?.cancel();
    _healthCheckTimer = Timer.periodic(
      Duration(seconds: _config?.healthCheckInterval ?? 30),
      (timer) => _performHealthCheck(),
    );
  }

  /// Perform health check on all services
  Future<void> _performHealthCheck() async {
    final futures = <Future<void>>[
      _checkMarketServiceHealth(),
      _checkAuthServiceHealth(),
      _checkBrokerServiceHealth(),
      _checkPortfolioServiceHealth(),
    ];

    await Future.wait(futures);

    if (!isHealthy) {
      _emitEvent(ServiceManagerEvent.healthCheckFailed(_serviceHealth));
    }
  }

  /// Check market service health
  Future<void> _checkMarketServiceHealth() async {
    try {
      // Simple health check - attempt to get available symbols
      await _marketService.getAvailableSymbols();
      _updateServiceHealth('market', ServiceHealthStatus.healthy);
    } catch (e) {
      _updateServiceHealth('market', ServiceHealthStatus.unhealthy);
    }
  }

  /// Check auth service health
  Future<void> _checkAuthServiceHealth() async {
    try {
      // Simple health check - check if service is responsive
      final _ = _authClient.isAuthenticated;
      _updateServiceHealth('auth', ServiceHealthStatus.healthy);
    } catch (e) {
      _updateServiceHealth('auth', ServiceHealthStatus.unhealthy);
    }
  }

  /// Check broker service health
  Future<void> _checkBrokerServiceHealth() async {
    try {
      // Simple health check - attempt to get available brokers
      await _brokerService.getAvailableBrokers();
      _updateServiceHealth('broker', ServiceHealthStatus.healthy);
    } catch (e) {
      _updateServiceHealth('broker', ServiceHealthStatus.unhealthy);
    }
  }

  /// Check portfolio service health
  Future<void> _checkPortfolioServiceHealth() async {
    try {
      // Simple health check - attempt to get portfolios
      _portfolioService.getAllPortfolios();
      _updateServiceHealth('portfolio', ServiceHealthStatus.healthy);
    } catch (e) {
      _updateServiceHealth('portfolio', ServiceHealthStatus.unhealthy);
    }
  }

  /// Apply configuration to services
  Future<void> _applyConfiguration() async {
    // Apply configuration to each service
    // This would update service-specific settings
  }

  /// Shutdown all services
  Future<void> _shutdownServices() async {
    _healthCheckTimer?.cancel();

    // Shutdown services in reverse order
    _portfolioService.dispose();
    _brokerService.dispose();
    _authClient.dispose();
    _marketService.dispose();

    _serviceHealth.clear();
  }

  /// Update service health status
  void _updateServiceHealth(String serviceName, ServiceHealthStatus status) {
    final previousStatus = _serviceHealth[serviceName];
    _serviceHealth[serviceName] = status;

    if (previousStatus != status) {
      _emitEvent(ServiceManagerEvent.serviceHealthChanged(serviceName, status));
    }
  }

  /// Get market service statistics
  Map<String, dynamic> _getMarketServiceStats() {
    return {
      'status': _serviceHealth['market']?.toString() ?? 'unknown',
      'cacheSize': _marketService.cacheSize,
      'requestCount': _marketService.requestCount,
      'lastRequestTime': _marketService.lastRequestTime?.toIso8601String(),
    };
  }

  /// Get auth service statistics
  Map<String, dynamic> _getAuthServiceStats() {
    return {
      'status': _serviceHealth['auth']?.toString() ?? 'unknown',
      'isAuthenticated': _authClient.isAuthenticated,
      'userId': _authClient.currentUser?.id,
      'accessToken': _authClient.accessToken != null ? 'present' : 'absent',
    };
  }

  /// Get broker service statistics
  Map<String, dynamic> _getBrokerServiceStats() {
    return {
      'status': _serviceHealth['broker']?.toString() ?? 'unknown',
      'connectedBrokers': _brokerService.getConnectedBrokers().length,
      'totalConnections': _brokerService.getAllBrokerConnections().length,
    };
  }

  /// Get portfolio service statistics
  Map<String, dynamic> _getPortfolioServiceStats() {
    return {
      'status': _serviceHealth['portfolio']?.toString() ?? 'unknown',
      'totalPortfolios': _portfolioService.getAllPortfolios().length,
      'totalValue': _portfolioService.getAllPortfolios().fold<double>(
        0.0,
        (sum, p) => sum + p.totalValue,
      ),
    };
  }

  /// Get uptime
  Duration _getUptime() {
    // This would calculate actual uptime
    return Duration.zero;
  }

  /// Set manager state
  void _setState(ServiceManagerState newState) {
    _state = newState;
    _emitEvent(ServiceManagerEvent.stateChanged(newState));
  }

  /// Emit event
  void _emitEvent(ServiceManagerEvent event) {
    if (!_eventController.isClosed) {
      _eventController.add(event);
    }
  }

  /// Ensure services are initialized
  void _ensureInitialized() {
    if (_state != ServiceManagerState.initialized) {
      throw Exception('Service manager not initialized');
    }
  }

  /// Dispose resources
  void dispose() {
    _eventController.close();
    _healthCheckTimer?.cancel();
  }
}

/// Service manager state
enum ServiceManagerState {
  uninitialized,
  initializing,
  initialized,
  restarting,
  shuttingDown,
  shutdown,
  error,
}

/// Service health status
enum ServiceHealthStatus { healthy, unhealthy, initializing, unknown }

/// Service manager configuration
class ServiceManagerConfig {
  final int healthCheckInterval; // seconds
  final bool enableHealthMonitoring;
  final String apiBaseUrl;
  final String supabaseUrl;
  final String supabaseAnonKey;
  final Map<String, dynamic> marketServiceConfig;
  final Map<String, dynamic> authServiceConfig;
  final Map<String, dynamic> brokerServiceConfig;
  final Map<String, dynamic> portfolioServiceConfig;

  ServiceManagerConfig({
    this.healthCheckInterval = 30,
    this.enableHealthMonitoring = true,
    required this.apiBaseUrl,
    required this.supabaseUrl,
    required this.supabaseAnonKey,
    this.marketServiceConfig = const {},
    this.authServiceConfig = const {},
    this.brokerServiceConfig = const {},
    this.portfolioServiceConfig = const {},
  });
}

/// Service statistics
class ServiceStatistics {
  final Map<String, dynamic> marketService;
  final Map<String, dynamic> authService;
  final Map<String, dynamic> brokerService;
  final Map<String, dynamic> portfolioService;
  final int healthyServices;
  final int totalServices;
  final Duration uptime;

  ServiceStatistics({
    required this.marketService,
    required this.authService,
    required this.brokerService,
    required this.portfolioService,
    required this.healthyServices,
    required this.totalServices,
    required this.uptime,
  });
}

/// Service manager event
class ServiceManagerEvent {
  final ServiceManagerEventType type;
  final DateTime timestamp;
  final Map<String, dynamic> data;

  ServiceManagerEvent._({
    required this.type,
    required this.timestamp,
    required this.data,
  });

  factory ServiceManagerEvent.initializationStarted() {
    return ServiceManagerEvent._(
      type: ServiceManagerEventType.initializationStarted,
      timestamp: DateTime.now(),
      data: {},
    );
  }

  factory ServiceManagerEvent.initializationCompleted() {
    return ServiceManagerEvent._(
      type: ServiceManagerEventType.initializationCompleted,
      timestamp: DateTime.now(),
      data: {},
    );
  }

  factory ServiceManagerEvent.initializationFailed(String error) {
    return ServiceManagerEvent._(
      type: ServiceManagerEventType.initializationFailed,
      timestamp: DateTime.now(),
      data: {'error': error},
    );
  }

  factory ServiceManagerEvent.stateChanged(ServiceManagerState state) {
    return ServiceManagerEvent._(
      type: ServiceManagerEventType.stateChanged,
      timestamp: DateTime.now(),
      data: {'state': state},
    );
  }

  factory ServiceManagerEvent.serviceHealthChanged(
    String serviceName,
    ServiceHealthStatus status,
  ) {
    return ServiceManagerEvent._(
      type: ServiceManagerEventType.serviceHealthChanged,
      timestamp: DateTime.now(),
      data: {'serviceName': serviceName, 'status': status},
    );
  }

  factory ServiceManagerEvent.healthCheckFailed(
    Map<String, ServiceHealthStatus> serviceHealth,
  ) {
    return ServiceManagerEvent._(
      type: ServiceManagerEventType.healthCheckFailed,
      timestamp: DateTime.now(),
      data: {'serviceHealth': serviceHealth},
    );
  }

  factory ServiceManagerEvent.configurationUpdated(
    ServiceManagerConfig config,
  ) {
    return ServiceManagerEvent._(
      type: ServiceManagerEventType.configurationUpdated,
      timestamp: DateTime.now(),
      data: {'config': config},
    );
  }

  factory ServiceManagerEvent.restartStarted() {
    return ServiceManagerEvent._(
      type: ServiceManagerEventType.restartStarted,
      timestamp: DateTime.now(),
      data: {},
    );
  }

  factory ServiceManagerEvent.restartCompleted() {
    return ServiceManagerEvent._(
      type: ServiceManagerEventType.restartCompleted,
      timestamp: DateTime.now(),
      data: {},
    );
  }

  factory ServiceManagerEvent.restartFailed(String error) {
    return ServiceManagerEvent._(
      type: ServiceManagerEventType.restartFailed,
      timestamp: DateTime.now(),
      data: {'error': error},
    );
  }

  factory ServiceManagerEvent.shutdownStarted() {
    return ServiceManagerEvent._(
      type: ServiceManagerEventType.shutdownStarted,
      timestamp: DateTime.now(),
      data: {},
    );
  }

  factory ServiceManagerEvent.shutdownCompleted() {
    return ServiceManagerEvent._(
      type: ServiceManagerEventType.shutdownCompleted,
      timestamp: DateTime.now(),
      data: {},
    );
  }

  factory ServiceManagerEvent.shutdownFailed(String error) {
    return ServiceManagerEvent._(
      type: ServiceManagerEventType.shutdownFailed,
      timestamp: DateTime.now(),
      data: {'error': error},
    );
  }
}

/// Service manager event types
enum ServiceManagerEventType {
  initializationStarted,
  initializationCompleted,
  initializationFailed,
  stateChanged,
  serviceHealthChanged,
  healthCheckFailed,
  configurationUpdated,
  restartStarted,
  restartCompleted,
  restartFailed,
  shutdownStarted,
  shutdownCompleted,
  shutdownFailed,
}

/// Enhanced service manager providers
final enhancedServiceManagerProvider = Provider<ServiceManager>((ref) {
  final manager = ServiceManager();
  ref.onDispose(() => manager.dispose());
  return manager;
});

/// Service manager state provider
final serviceManagerStateProvider = StreamProvider<ServiceManagerState>((ref) {
  final manager = ref.watch(enhancedServiceManagerProvider);
  return manager.events
      .where((event) => event.type == ServiceManagerEventType.stateChanged)
      .map((event) => event.data['state'] as ServiceManagerState);
});

/// Service health provider
final serviceHealthProvider = StateNotifierProvider<
  ServiceHealthNotifier,
  Map<String, ServiceHealthStatus>
>((ref) {
  return ServiceHealthNotifier(ref);
});

class ServiceHealthNotifier
    extends StateNotifier<Map<String, ServiceHealthStatus>> {
  final Ref ref;
  late final ServiceManager _manager;

  ServiceHealthNotifier(this.ref) : super({}) {
    _manager = ref.read(enhancedServiceManagerProvider);
    _setupHealthListener();
  }

  void _setupHealthListener() {
    _manager.events
        .where(
          (event) => event.type == ServiceManagerEventType.serviceHealthChanged,
        )
        .listen((event) {
          final serviceName = event.data['serviceName'] as String;
          final status = event.data['status'] as ServiceHealthStatus;

          state = {...state, serviceName: status};
        });
  }

  void refreshHealth() {
    state = _manager.serviceHealth;
  }
}

/// Service statistics provider
final serviceStatisticsProvider = Provider<ServiceStatistics>((ref) {
  final manager = ref.watch(enhancedServiceManagerProvider);
  return manager.getServiceStatistics();
});

/// Service manager events provider
final serviceManagerEventsProvider = StreamProvider<ServiceManagerEvent>((ref) {
  final manager = ref.watch(enhancedServiceManagerProvider);
  return manager.events;
});

/// Is services healthy provider
final isServicesHealthyProvider = Provider<bool>((ref) {
  final manager = ref.watch(enhancedServiceManagerProvider);
  return manager.isHealthy;
});
