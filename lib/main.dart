import 'package:abra/settings/auth_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'core/connectivity/connectivity_service.dart';
import 'core/splash_service.dart';
import 'core/theme.dart';
import 'presentation/home/<USER>';
import 'services/auth_client.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Load environment variables
  await dotenv.load(fileName: ".env");

  // Initialize Supabase
  await Supabase.initialize(
    url: dotenv.env['SUPABASE_URL']!,
    anonKey: dotenv.env['SUPABASE_ANON_KEY']!,
    authOptions: const FlutterAuthClientOptions(
      autoRefreshToken: true,
      detectSessionInUri: true,
    ),
  );

  final prefs = await SharedPreferences.getInstance();
  final savedIndex = prefs.getInt('themeIndex') ?? 1; // Default to System
  final themeModes = [ThemeMode.dark, ThemeMode.system, ThemeMode.light];
  final initialThemeMode = themeModes[savedIndex];

  // Initialize connectivity service
  ConnectivityService().initialize();

  runApp(
    ProviderScope(
      overrides: [themeProvider.overrideWith((ref) => initialThemeMode)],
      child: Abra(),
    ),
  );
}

class Abra extends ConsumerStatefulWidget {
  const Abra({super.key});

  @override
  ConsumerState<Abra> createState() => AbraState();
}

class AbraState extends ConsumerState<Abra> {
  Future<StartupDestination> _getStartupDestination() async {
    try {
      // Initialize AuthClient to load stored tokens
      final authClient = AuthClient();
      await authClient.initialize();

      debugPrint('Checking authentication state...');
      debugPrint('Is authenticated: ${authClient.isAuthenticated}');
      debugPrint('Current user: ${authClient.currentUser?.email}');

      if (authClient.isAuthenticated) {
        // User is authenticated, use SplashService to determine destination
        debugPrint('User is authenticated, checking SplashService...');
        final splashService = SplashService();
        final destination = await splashService.getStartupDestination();
        debugPrint('SplashService returned destination: $destination');
        return destination;
      } else {
        // User is not authenticated
        debugPrint('User is not authenticated, going to auth screen');
        return StartupDestination.auth;
      }
    } catch (e) {
      debugPrint('Error determining startup destination: $e');
      // On error, default to auth screen
      return StartupDestination.auth;
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeMode = ref.watch(themeProvider);
    return MaterialApp(
      theme: AppTheme.lightTheme(),
      darkTheme: AppTheme.darkTheme(),
      themeMode: themeMode,
      debugShowCheckedModeBanner: false,
      home: FutureBuilder<StartupDestination>(
        future: _getStartupDestination(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // Show loading while determining destination
            return Scaffold(
              backgroundColor: Theme.of(context).colorScheme.primary,
              body: const Center(
                child: CircularProgressIndicator(color: Colors.white),
              ),
            );
          }

          // Navigate based on startup destination
          final destination = snapshot.data ?? StartupDestination.auth;
          debugPrint('FutureBuilder: Received destination: $destination');

          switch (destination) {
            case StartupDestination.main:
              debugPrint('FutureBuilder: Navigating to MainScreen');
              return const MainScreen(initialIndex: 0);
            case StartupDestination.auth:
              debugPrint('FutureBuilder: Navigating to AuthScreen');
              return const AuthScreen();
          }
        },
      ),
      routes: {
        '/home': (_) => const MainScreen(initialIndex: 0),
        '/sign-up': (context) => const AuthScreen(),
      },
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}
