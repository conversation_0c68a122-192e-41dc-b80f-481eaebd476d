import Flutter
import Metal
import MetalKit
import CoreVideo

// Candlestick structure matching C++ and Dart
struct Candlestick {
    var open: Double
    var high: Double
    var low: Double
    var close: Double
    var startTime: Int64
    var endTime: Int64
}

class ChartRenderer: NSObject, FlutterTexture {
    private var device: MTLDevice?
    private var commandQueue: MTLCommandQueue?
    private var renderPipelineState: MTLRenderPipelineState?
    private var indicatorPipelineState: MTLRenderPipelineState?
    private var textureCache: CVMetalTextureCache?
    private var pixelBuffer: CVPixelBuffer?
    
    private var width: Int
    private var height: Int
    private var timeIntervalSeconds: Int
    private var includePriceAxis: Bool
    private var includeTimeAxis: Bool
    private var priceAxisWidth: Int
    private var timeAxisHeight: Int
    
    // Chart data
    private var candlesticks: [Candlestick] = []
    private var currentCandle: Candlestick?
    private var hasCurrentCandle = false
    
    // Chart parameters
    private var horizontalZoom: Double = 1.0
    private var verticalZoom: Double = 1.0
    private var horizontalOffset: Double = 0.0
    private var verticalOffset: Double = 0.0
    private var highPrice: Double = 0.0
    private var startTime: Int64 = 0
    
    // Rendering
    private var renderingTimer: Timer?
    private let renderQueue = DispatchQueue(label: "chart.render.queue")
    
    init(width: Int, height: Int, timeIntervalSeconds: Int, 
         includePriceAxis: Bool, includeTimeAxis: Bool,
         priceAxisWidth: Int, timeAxisHeight: Int) {
        self.width = width
        self.height = height
        self.timeIntervalSeconds = timeIntervalSeconds
        self.includePriceAxis = includePriceAxis
        self.includeTimeAxis = includeTimeAxis
        self.priceAxisWidth = priceAxisWidth
        self.timeAxisHeight = timeAxisHeight
        
        super.init()
    }
    
    func initialize() -> Bool {
        guard let device = MTLCreateSystemDefaultDevice() else {
            print("Metal is not supported on this device")
            return false
        }
        
        self.device = device
        self.commandQueue = device.makeCommandQueue()
        
        // Create texture cache
        let result = CVMetalTextureCacheCreate(kCFAllocatorDefault, nil, device, nil, &textureCache)
        guard result == kCVReturnSuccess else {
            print("Failed to create texture cache")
            return false
        }
        
        // Create pixel buffer
        createPixelBuffer()
        
        // Create render pipeline
        return createRenderPipeline()
    }
    
    private func createPixelBuffer() {
        let attributes: [String: Any] = [
            kCVPixelBufferPixelFormatTypeKey as String: kCVPixelFormatType_32BGRA,
            kCVPixelBufferWidthKey as String: width,
            kCVPixelBufferHeightKey as String: height,
            kCVPixelBufferMetalCompatibilityKey as String: true
        ]
        
        CVPixelBufferCreate(kCFAllocatorDefault, width, height, kCVPixelFormatType_32BGRA, attributes as CFDictionary, &pixelBuffer)
    }
    
    private func createRenderPipeline() -> Bool {
        guard let device = self.device else { return false }
        
        let library = device.makeDefaultLibrary()
        let vertexFunction = library?.makeFunction(name: "vertex_main")
        let fragmentFunction = library?.makeFunction(name: "fragment_main")
        
        let pipelineDescriptor = MTLRenderPipelineDescriptor()
        pipelineDescriptor.vertexFunction = vertexFunction
        pipelineDescriptor.fragmentFunction = fragmentFunction
        pipelineDescriptor.colorAttachments[0].pixelFormat = .bgra8Unorm
        
        do {
            renderPipelineState = try device.makeRenderPipelineState(descriptor: pipelineDescriptor)
            return true
        } catch {
            print("Failed to create render pipeline state: \(error)")
            return false
        }
    }
    
    func resize(width: Int, height: Int) {
        self.width = width
        self.height = height
        createPixelBuffer()
    }
    
    func feedTick(price: Double, timestamp: Int64) {
        renderQueue.async { [weak self] in
            self?.updateCurrentCandle(price: price, timestamp: timestamp)
        }
    }
    
    private func updateCurrentCandle(price: Double, timestamp: Int64) {
        let candleStart = getCandleStartTime(timestamp: timestamp)
        
        if !hasCurrentCandle || currentCandle?.startTime != candleStart {
            // Finalize previous candle
            if hasCurrentCandle, let candle = currentCandle {
                candlesticks.append(candle)
            }
            
            // Start new candle
            currentCandle = Candlestick(
                open: price,
                high: price,
                low: price,
                close: price,
                startTime: candleStart,
                endTime: candleStart + Int64(timeIntervalSeconds * 1000)
            )
            hasCurrentCandle = true
        } else {
            // Update existing candle
            currentCandle?.high = max(currentCandle?.high ?? 0, price)
            currentCandle?.low = min(currentCandle?.low ?? Double.greatestFiniteMagnitude, price)
            currentCandle?.close = price
            currentCandle?.endTime = max(currentCandle?.endTime ?? 0, timestamp)
        }
    }
    
    private func getCandleStartTime(timestamp: Int64) -> Int64 {
        let intervalMs = Int64(timeIntervalSeconds * 1000)
        return (timestamp / intervalMs) * intervalMs
    }
    
    func setHighPrice(high: Double) {
        self.highPrice = high
    }
    
    func setStartTime(timestamp: Int64) {
        self.startTime = timestamp
    }
    
    func setZoomScales(horizontalZoom: Double, verticalZoom: Double) {
        self.horizontalZoom = max(0.1, min(10.0, horizontalZoom))
        self.verticalZoom = max(0.1, min(10.0, verticalZoom))
    }
    
    func setPanOffsets(horizontalOffset: Double, verticalOffset: Double) {
        self.horizontalOffset = horizontalOffset
        self.verticalOffset = verticalOffset
    }
    
    func getCandlestickCount() -> Int {
        return candlesticks.count + (hasCurrentCandle ? 1 : 0)
    }
    
    func startRenderingLoop() {
        renderingTimer = Timer.scheduledTimer(withTimeInterval: 1.0/60.0, repeats: true) { [weak self] _ in
            self?.render()
        }
    }
    
    private func render() {
        guard let device = self.device,
              let commandQueue = self.commandQueue,
              let renderPipelineState = self.renderPipelineState,
              let pixelBuffer = self.pixelBuffer,
              let textureCache = self.textureCache else {
            return
        }
        
        // Create Metal texture from pixel buffer
        var metalTexture: CVMetalTexture?
        let result = CVMetalTextureCacheCreateTextureFromImage(
            kCFAllocatorDefault,
            textureCache,
            pixelBuffer,
            nil,
            .bgra8Unorm,
            width,
            height,
            0,
            &metalTexture
        )
        
        guard result == kCVReturnSuccess,
              let metalTexture = metalTexture,
              let texture = CVMetalTextureGetTexture(metalTexture) else {
            return
        }
        
        // Create command buffer
        guard let commandBuffer = commandQueue.makeCommandBuffer() else { return }
        
        // Create render pass descriptor
        let renderPassDescriptor = MTLRenderPassDescriptor()
        renderPassDescriptor.colorAttachments[0].texture = texture
        renderPassDescriptor.colorAttachments[0].loadAction = .clear
        renderPassDescriptor.colorAttachments[0].clearColor = MTLClearColor(red: 0.1, green: 0.1, blue: 0.15, alpha: 1.0)
        renderPassDescriptor.colorAttachments[0].storeAction = .store
        
        // Create render encoder
        guard let renderEncoder = commandBuffer.makeRenderCommandEncoder(descriptor: renderPassDescriptor) else { return }
        
        renderEncoder.setRenderPipelineState(renderPipelineState)
        
        // Render candlesticks
        renderCandlesticks(encoder: renderEncoder)
        
        renderEncoder.endEncoding()
        commandBuffer.commit()
    }
    
    private func renderCandlesticks(encoder: MTLRenderCommandEncoder) {
        let allCandlesticks = candlesticks + (hasCurrentCandle ? [currentCandle!] : [])
        
        guard !allCandlesticks.isEmpty else { return }
        
        let minPrice = allCandlesticks.map { $0.low }.min() ?? 0
        let maxPrice = allCandlesticks.map { $0.high }.max() ?? 0
        let priceRange = maxPrice - minPrice
        
        guard priceRange > 0 else { return }
        
        // Simple rendering - this would be expanded with proper vertex buffers
        // For now, this is a placeholder that demonstrates the structure
    }
    
    func dispose() {
        renderingTimer?.invalidate()
        renderingTimer = nil
        
        pixelBuffer = nil
        textureCache = nil
        renderPipelineState = nil
        commandQueue = nil
        device = nil
    }
    
    // MARK: - FlutterTexture
    
    func copyPixelBuffer() -> Unmanaged<CVPixelBuffer>? {
        guard let pixelBuffer = self.pixelBuffer else { return nil }
        return Unmanaged.passRetained(pixelBuffer)
    }
}
