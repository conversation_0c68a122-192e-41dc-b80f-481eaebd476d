import Flutter
import UIKit
import Metal
import MetalKit

public class ChartViewPlugin: NSObject, FlutterPlugin {
    private var textureRegistry: FlutterTextureRegistry?
    private var textureEntries: [Int: FlutterTextureRegistry] = [:]
    private var chartRenderers: [Int: ChartRenderer] = [:]
    private var nextTextureId = 1
    
    public static func register(with registrar: FlutterPluginRegistrar) {
        let channel = FlutterMethodChannel(name: "com.abra/chart_view", binaryMessenger: registrar.messenger())
        let instance = ChartViewPlugin()
        instance.textureRegistry = registrar.textures()
        registrar.addMethodCallDelegate(instance, channel: channel)
    }
    
    public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case "createTexture":
            createTexture(call: call, result: result)
        case "resizeTexture":
            resizeTexture(call: call, result: result)
        case "disposeTexture":
            disposeTexture(call: call, result: result)
        case "feedTick":
            feedTick(call: call, result: result)
        case "setHighPrice":
            setHighPrice(call: call, result: result)
        case "setStartTime":
            setStartTime(call: call, result: result)
        case "setZoomScales":
            setZoomScales(call: call, result: result)
        case "setPanOffsets":
            setPanOffsets(call: call, result: result)
        case "getCandlestickCount":
            getCandlestickCount(call: call, result: result)
        default:
            result(FlutterMethodNotImplemented)
        }
    }
    
    private func createTexture(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let width = args["width"] as? Int,
              let height = args["height"] as? Int else {
            result(FlutterError(code: "INVALID_ARGS", message: "Missing required arguments", details: nil))
            return
        }
        
        let timeIntervalSeconds = args["timeIntervalSeconds"] as? Int ?? 300
        let includePriceAxis = args["includePriceAxis"] as? Bool ?? false
        let includeTimeAxis = args["includeTimeAxis"] as? Bool ?? false
        let priceAxisWidth = args["priceAxisWidth"] as? Int ?? 50
        let timeAxisHeight = args["timeAxisHeight"] as? Int ?? 30
        
        guard let textureRegistry = self.textureRegistry else {
            result(FlutterError(code: "NO_TEXTURE_REGISTRY", message: "Texture registry not available", details: nil))
            return
        }
        
        let renderer = ChartRenderer(width: width, height: height, 
                                   timeIntervalSeconds: timeIntervalSeconds,
                                   includePriceAxis: includePriceAxis,
                                   includeTimeAxis: includeTimeAxis,
                                   priceAxisWidth: priceAxisWidth,
                                   timeAxisHeight: timeAxisHeight)
        
        guard renderer.initialize() else {
            result(FlutterError(code: "RENDERER_INIT_FAILED", message: "Failed to initialize renderer", details: nil))
            return
        }
        
        let textureId = nextTextureId
        nextTextureId += 1
        
        chartRenderers[textureId] = renderer
        
        // Register texture with Flutter
        let flutterTextureId = textureRegistry.register(renderer)
        
        // Start rendering loop
        renderer.startRenderingLoop()
        
        result(flutterTextureId)
    }
    
    private func resizeTexture(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let textureId = args["textureId"] as? Int,
              let width = args["width"] as? Int,
              let height = args["height"] as? Int else {
            result(FlutterError(code: "INVALID_ARGS", message: "Missing required arguments", details: nil))
            return
        }
        
        if let renderer = chartRenderers[textureId] {
            renderer.resize(width: width, height: height)
            result(nil)
        } else {
            result(FlutterError(code: "TEXTURE_NOT_FOUND", message: "Texture not found", details: nil))
        }
    }
    
    private func disposeTexture(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let textureId = args["textureId"] as? Int else {
            result(FlutterError(code: "INVALID_ARGS", message: "Missing textureId", details: nil))
            return
        }
        
        if let renderer = chartRenderers[textureId] {
            renderer.dispose()
            chartRenderers.removeValue(forKey: textureId)
        }
        
        result(nil)
    }
    
    private func feedTick(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let textureId = args["textureId"] as? Int,
              let price = args["price"] as? Double,
              let timestamp = args["timestamp"] as? Int64 else {
            result(FlutterError(code: "INVALID_ARGS", message: "Missing required arguments", details: nil))
            return
        }
        
        if let renderer = chartRenderers[textureId] {
            renderer.feedTick(price: price, timestamp: timestamp)
        }
        
        result(nil)
    }
    
    private func setHighPrice(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let textureId = args["textureId"] as? Int,
              let high = args["high"] as? Double else {
            result(FlutterError(code: "INVALID_ARGS", message: "Missing required arguments", details: nil))
            return
        }
        
        if let renderer = chartRenderers[textureId] {
            renderer.setHighPrice(high: high)
        }
        
        result(nil)
    }
    
    private func setStartTime(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let textureId = args["textureId"] as? Int,
              let timestamp = args["timestamp"] as? Int64 else {
            result(FlutterError(code: "INVALID_ARGS", message: "Missing required arguments", details: nil))
            return
        }
        
        if let renderer = chartRenderers[textureId] {
            renderer.setStartTime(timestamp: timestamp)
        }
        
        result(nil)
    }
    
    private func setZoomScales(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let textureId = args["textureId"] as? Int,
              let horizontalZoom = args["horizontalZoom"] as? Double,
              let verticalZoom = args["verticalZoom"] as? Double else {
            result(FlutterError(code: "INVALID_ARGS", message: "Missing required arguments", details: nil))
            return
        }
        
        if let renderer = chartRenderers[textureId] {
            renderer.setZoomScales(horizontalZoom: horizontalZoom, verticalZoom: verticalZoom)
        }
        
        result(nil)
    }
    
    private func setPanOffsets(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let textureId = args["textureId"] as? Int,
              let horizontalOffset = args["horizontalOffset"] as? Double,
              let verticalOffset = args["verticalOffset"] as? Double else {
            result(FlutterError(code: "INVALID_ARGS", message: "Missing required arguments", details: nil))
            return
        }
        
        if let renderer = chartRenderers[textureId] {
            renderer.setPanOffsets(horizontalOffset: horizontalOffset, verticalOffset: verticalOffset)
        }
        
        result(nil)
    }
    
    private func getCandlestickCount(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let textureId = args["textureId"] as? Int else {
            result(FlutterError(code: "INVALID_ARGS", message: "Missing textureId", details: nil))
            return
        }
        
        if let renderer = chartRenderers[textureId] {
            let count = renderer.getCandlestickCount()
            result(count)
        } else {
            result(0)
        }
    }
}
