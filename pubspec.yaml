name: abra
description: "A new Flutter project."
publish_to: 'none'
version: 0.1.0

environment:
  sdk: ^3.7.2

dependencies:
  app_links: ^6.4.0
  ffi: ^2.1.4
  flutter:
    sdk: flutter
  flutter_bloc: ^9.1.1
  flutter_dotenv: ^5.2.1
  flutter_riverpod: ^2.6.1
  riverpod_annotation: ^2.6.1
  google_sign_in: ^7.0.0
  http: ^1.4.0
  image_picker: ^1.1.2
  shared_preferences: ^2.5.3
  supabase_flutter: ^2.9.0
  syncfusion_flutter_charts: ^29.2.8
  uuid: ^4.5.1
  web_socket_channel: ^3.0.3
  flutter_launcher_icons: ^0.14.3
  cupertino_icons: ^1.0.8
  rxdart: ^0.28.0
  connectivity_plus: ^6.1.4
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.1.5
  country_code_picker: ^3.0.0
  dropdown_search: ^6.0.2
  url_launcher: ^6.3.1
  country_codes: ^3.3.0
  country_flags: ^3.3.0
  sms_autofill: ^2.4.1
  flutter_multi_formatter: ^2.13.7
  country_picker: ^2.0.27
  jwt_decoder: ^2.0.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  flutter_native_splash: ^2.4.6
  test: ^1.25.15
  mockito: ^5.4.6
  build_runner: ^2.4.13

flutter:
  uses-material-design: true
  assets: 
    - assets/gsignin/gdark.png
    - assets/gsignin/glight.png
    - assets/icons/watchlist_outlined.png
    - assets/icons/watchlist_rounded.png
    - assets/icons/portfolio_outlined.png
    - assets/icons/portfolio_rounded.png
    - assets/icons/candles_outlined.png
    - assets/icons/candles_rounded.png
    - assets/icons/no_internet.png
    - assets/icons/anonymous.png
    - assets/icons/shilling.png
    - assets/icons/genders.png
    - assets/ai/alim_bot.png
    - assets/courses/beginner_module1-3.json
    - assets/courses/beginner_module4-10.json
    - assets/courses/intermediate_module1-3.json
    - assets/courses/intermediate_module4-7.json
    - assets/courses/advanced_module1-4.json
    - .env

flutter_launcher_icons:
  android: true
  ios: true
  remove_alpha_ios: true
  image_path: "assets/app_logo/abra_splash3D.png"
  min_sdk_android: 21
  adaptive_icon_background: "#2b2d3d"
  adaptive_icon_foreground: "assets/app_logo/abra_splash3D.png"
  # filename: "custom_icon"

flutter_native_splash:
  color: "#09121e"        # Background color
  background_image: assets/app_logo/abra_splash3D.png # Path to your splash image
  android: true
  ios: true
  web: false              # Splash only works on mobile platforms

  android_12:
    background_image: assets/app_logo/abra_splash.png
    icon_background_color: "#000000"