#!/bin/bash

# Build script for iOS native library
set -e

# Configuration
BUILD_TYPE=${1:-Release}
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

echo "Building iOS native library..."
echo "Build type: $BUILD_TYPE"
echo "Project root: $PROJECT_ROOT"

# Check if Xcode is available
if ! command -v xcodebuild &> /dev/null; then
    echo "Error: Xcode not found. Please install Xcode and command line tools."
    exit 1
fi

# Create build directory
BUILD_DIR="$PROJECT_ROOT/build/ios"
mkdir -p "$BUILD_DIR"

# iOS deployment target
IOS_DEPLOYMENT_TARGET="11.0"

# Build for iOS device (arm64)
echo "Building for iOS device (arm64)..."
DEVICE_BUILD_DIR="$BUILD_DIR/device"
mkdir -p "$DEVICE_BUILD_DIR"
cd "$DEVICE_BUILD_DIR"

cmake \
    -DCMAKE_SYSTEM_NAME=iOS \
    -DCMAKE_OSX_DEPLOYMENT_TARGET="$IOS_DEPLOYMENT_TARGET" \
    -DCMAKE_OSX_ARCHITECTURES=arm64 \
    -DCMAKE_BUILD_TYPE="$BUILD_TYPE" \
    -DIOS=ON \
    -DCMAKE_XCODE_ATTRIBUTE_DEVELOPMENT_TEAM="" \
    "$PROJECT_ROOT"

cmake --build . --config "$BUILD_TYPE" -j$(sysctl -n hw.ncpu)

# Build for iOS simulator (x86_64 and arm64)
echo "Building for iOS simulator..."
SIMULATOR_BUILD_DIR="$BUILD_DIR/simulator"
mkdir -p "$SIMULATOR_BUILD_DIR"
cd "$SIMULATOR_BUILD_DIR"

cmake \
    -DCMAKE_SYSTEM_NAME=iOS \
    -DCMAKE_OSX_DEPLOYMENT_TARGET="$IOS_DEPLOYMENT_TARGET" \
    -DCMAKE_OSX_ARCHITECTURES="x86_64;arm64" \
    -DCMAKE_BUILD_TYPE="$BUILD_TYPE" \
    -DIOS=ON \
    -DCMAKE_OSX_SYSROOT=iphonesimulator \
    "$PROJECT_ROOT"

cmake --build . --config "$BUILD_TYPE" -j$(sysctl -n hw.ncpu)

# Create universal library using lipo
echo "Creating universal library..."
FRAMEWORKS_DIR="$PROJECT_ROOT/ios/Frameworks"
mkdir -p "$FRAMEWORKS_DIR"

DEVICE_LIB="$DEVICE_BUILD_DIR/lib/libnative-lib.dylib"
SIMULATOR_LIB="$SIMULATOR_BUILD_DIR/lib/libnative-lib.dylib"
UNIVERSAL_LIB="$FRAMEWORKS_DIR/libnative-lib.dylib"

if [ -f "$DEVICE_LIB" ] && [ -f "$SIMULATOR_LIB" ]; then
    lipo -create "$DEVICE_LIB" "$SIMULATOR_LIB" -output "$UNIVERSAL_LIB"
    echo "Created universal library: $UNIVERSAL_LIB"
elif [ -f "$DEVICE_LIB" ]; then
    cp "$DEVICE_LIB" "$UNIVERSAL_LIB"
    echo "Copied device library: $UNIVERSAL_LIB"
elif [ -f "$SIMULATOR_LIB" ]; then
    cp "$SIMULATOR_LIB" "$UNIVERSAL_LIB"
    echo "Copied simulator library: $UNIVERSAL_LIB"
else
    echo "Error: No libraries found to create universal library"
    exit 1
fi

# Verify the universal library
echo "Library architectures:"
lipo -info "$UNIVERSAL_LIB"

echo "iOS build completed successfully!"
echo "Universal library is available at: $UNIVERSAL_LIB"
