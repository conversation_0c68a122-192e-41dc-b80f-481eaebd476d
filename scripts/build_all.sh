#!/bin/bash

# Build script for all platforms
set -e

BUILD_TYPE=${1:-Release}
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

echo "Building native libraries for all platforms..."
echo "Build type: $BUILD_TYPE"
echo "Project root: $PROJECT_ROOT"

# Make scripts executable
chmod +x "$PROJECT_ROOT/scripts/build_android.sh"
chmod +x "$PROJECT_ROOT/scripts/build_ios.sh"

# Detect platform and build accordingly
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "macOS detected - building for iOS and Android"
    
    # Build iOS
    echo "=== Building iOS ==="
    "$PROJECT_ROOT/scripts/build_ios.sh" "$BUILD_TYPE"
    
    # Build Android if NDK is available
    if [ -n "$ANDROID_NDK_ROOT" ] || [ -n "$ANDROID_HOME" ]; then
        echo "=== Building Android ==="
        "$PROJECT_ROOT/scripts/build_android.sh" "$BUILD_TYPE"
    else
        echo "Android NDK not found - skipping Android build"
        echo "Set ANDROID_NDK_ROOT or ANDROID_HOME to build for Android"
    fi
    
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "Linux detected - building for Android"
    
    # Build Android
    echo "=== Building Android ==="
    "$PROJECT_ROOT/scripts/build_android.sh" "$BUILD_TYPE"
    
else
    echo "Unsupported platform: $OSTYPE"
    echo "Supported platforms: macOS (for iOS + Android), Linux (for Android)"
    exit 1
fi

echo ""
echo "=== Build Summary ==="
echo "Build completed for available platforms!"

# Check what was built
if [ -d "$PROJECT_ROOT/android/app/src/main/jniLibs" ]; then
    echo "Android libraries:"
    find "$PROJECT_ROOT/android/app/src/main/jniLibs" -name "*.so" -type f
fi

if [ -d "$PROJECT_ROOT/ios/Frameworks" ]; then
    echo "iOS libraries:"
    find "$PROJECT_ROOT/ios/Frameworks" -name "*.dylib" -type f
fi

echo ""
echo "Next steps:"
echo "1. Run 'flutter clean' to clear any cached builds"
echo "2. Run 'flutter pub get' to ensure dependencies are up to date"
echo "3. Build your Flutter app with 'flutter build android' or 'flutter build ios'"
