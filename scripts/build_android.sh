#!/bin/bash

#!/bin/bash

# Build script for Android native library
set -e

# Configuration
BUILD_TYPE=${1:-Release}
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

# Prefer ANDROID_NDK_ROOT, fallback to ANDROID_HOME/ndk/* if available
if [ -n "$ANDROID_NDK_ROOT" ] && [ -d "$ANDROID_NDK_ROOT" ]; then
    ANDROID_NDK_PATH="$ANDROID_NDK_ROOT"
elif [ -n "$ANDROID_HOME" ] && [ -d "$ANDROID_HOME/ndk/28.1.13356709" ]; then
    ANDROID_NDK_PATH="$ANDROID_HOME/ndk/28.1.13356709"
else
    echo "Error: ANDROID_NDK_ROOT is not set or does not point to a valid directory."
    echo "Please set ANDROID_NDK_ROOT to your NDK path."
    exit 1
fi

ANDROID_API_LEVEL=21

echo "Building Android native library..."
echo "Build type: $BUILD_TYPE"
echo "NDK path: $ANDROID_NDK_PATH"
echo "Project root: $PROJECT_ROOT"

# Check if NDK exists
if [ ! -d "$ANDROID_NDK_PATH" ]; then
    echo "Error: Android NDK not found at $ANDROID_NDK_PATH"
    echo "Please set ANDROID_NDK_ROOT environment variable or install NDK"
    exit 1
fi

# Create build directories
BUILD_DIR="$PROJECT_ROOT/build/android"
mkdir -p "$BUILD_DIR"

# Android ABIs to build
ABIS=("arm64-v8a" "armeabi-v7a" "x86_64")

for ABI in "${ABIS[@]}"; do
    echo "Building for ABI: $ABI"
    
    ABI_BUILD_DIR="$BUILD_DIR/$ABI"
    mkdir -p "$ABI_BUILD_DIR"
    
    cd "$ABI_BUILD_DIR"
    
    # Configure with CMake
    cmake \
        -DCMAKE_TOOLCHAIN_FILE="$ANDROID_NDK_PATH/build/cmake/android.toolchain.cmake" \
        -DANDROID_ABI="$ABI" \
        -DANDROID_PLATFORM="android-$ANDROID_API_LEVEL" \
        -DANDROID_STL=c++_shared \
        -DCMAKE_BUILD_TYPE="$BUILD_TYPE" \
        -DANDROID=ON \
        "$PROJECT_ROOT"
    
    # Build
    cmake --build . --config "$BUILD_TYPE" -j$(nproc)
    
    # Copy to Android jniLibs directory
    JNI_LIBS_DIR="$PROJECT_ROOT/android/app/src/main/jniLibs/$ABI"
    mkdir -p "$JNI_LIBS_DIR"
    
    if [ -f "lib/libnative-lib.so" ]; then
        cp "lib/libnative-lib.so" "$JNI_LIBS_DIR/"
        echo "Copied libnative-lib.so to $JNI_LIBS_DIR"
    else
        echo "Warning: libnative-lib.so not found for $ABI"
    fi
done

echo "Android build completed successfully!"
echo "Native libraries are available in: $PROJECT_ROOT/android/app/src/main/jniLibs/"
