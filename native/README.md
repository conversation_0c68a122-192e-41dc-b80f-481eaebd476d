# Native Chart Engine

This directory contains the native C++ chart engine implementation for the Abra Flutter app, providing high-performance real-time candlestick chart rendering.

## Architecture

The native chart engine consists of:

- **C++ Core Engine** (`chart_engine/`): Core chart data processing and management
- **Android Integration** (`android/app/src/main/cpp/`): JNI bindings and OpenGL ES rendering
- **iOS Integration** (`ios/Runner/`): Swift plugin and Metal rendering
- **Build System** (`scripts/`): Cross-platform build scripts

## Features

- Real-time tick data processing and candlestick aggregation
- High-performance OpenGL/Metal rendering
- Zoom and pan gesture support
- Configurable time intervals (1min, 5min, 15min, etc.)
- Memory-efficient data structures
- Cross-platform compatibility (Android/iOS)

## Building

### Prerequisites

**For Android:**
- Android NDK (version 28.1.13356709 or later)
- CMake 3.22.1 or later
- Set `ANDROID_NDK_ROOT` or `ANDROID_HOME` environment variable

**For iOS:**
- Xcode with command line tools
- macOS development environment
- iOS deployment target 11.0+

### Build Commands

```bash
# Build for all available platforms
./scripts/build_all.sh

# Build for Android only
./scripts/build_android.sh

# Build for iOS only (macOS required)
./scripts/build_ios.sh

# Build in Debug mode
./scripts/build_all.sh Debug
```

### Output Locations

- **Android**: `android/app/src/main/jniLibs/{abi}/libnative-lib.so`
- **iOS**: `ios/Frameworks/libnative-lib.dylib`

## Integration

### Flutter FFI Bindings

The chart engine is accessed from Flutter through FFI bindings defined in:
- `lib/ffi/chart_engine_bindings.dart`

### Method Channels

Platform-specific rendering is handled through method channels:
- **Android**: `com.abra/chart_view` (ChartViewPlugin.kt)
- **iOS**: `com.abra/chart_view` (ChartViewPlugin.swift)

### Usage Example

```dart
// Create chart widget
final chartWidget = NativeChartWidget(
  symbol: 'AAPL',
  timeInterval: 300, // 5 minutes
  width: 800,
  height: 600,
);

// Feed real-time data
chartEngine.feedTick(price: 150.25, timestamp: DateTime.now().millisecondsSinceEpoch);
```

## API Reference

### Core Functions

```cpp
// Create chart engine instance
ChartEngine* create_chart_engine();

// Feed real-time tick data
void chart_engine_feed_tick(ChartEngine* engine, double price, int64_t timestamp);

// Get candlestick data
int chart_engine_get_candlestick_count(ChartEngine* engine);
void chart_engine_get_candlestick(ChartEngine* engine, int index, Candlestick* candle);

// Chart parameters
void chart_engine_set_time_interval(ChartEngine* engine, int seconds);
void chart_engine_set_zoom_scales(ChartEngine* engine, double horizontal, double vertical);
void chart_engine_set_pan_offsets(ChartEngine* engine, double horizontal, double vertical);

// Cleanup
void destroy_chart_engine(ChartEngine* engine);
```

### Data Structures

```cpp
struct Candlestick {
    double open;
    double high;
    double low;
    double close;
    int64_t start_time;
    int64_t end_time;
};
```

## Performance Considerations

- **Memory Management**: Uses RAII patterns and smart pointers
- **Data Structures**: Pre-allocated vectors with reserved capacity
- **Rendering**: 60 FPS target with efficient OpenGL/Metal usage
- **Threading**: Separate rendering thread to avoid blocking UI

## Troubleshooting

### Build Issues

1. **Android NDK not found**: Set `ANDROID_NDK_ROOT` environment variable
2. **CMake version**: Ensure CMake 3.22.1+ is installed
3. **iOS build fails**: Verify Xcode and command line tools are installed

### Runtime Issues

1. **Library not found**: Ensure native libraries are built and copied to correct locations
2. **Method channel errors**: Verify plugins are registered in MainActivity.kt/AppDelegate.swift
3. **Rendering issues**: Check OpenGL/Metal context creation and shader compilation

### Debug Mode

Build in debug mode for additional logging:
```bash
./scripts/build_all.sh Debug
```

## Contributing

When modifying the native engine:

1. Update both C++ core and platform-specific implementations
2. Maintain API compatibility with Flutter FFI bindings
3. Test on both Android and iOS platforms
4. Update documentation for any API changes

## License

This native chart engine is part of the Abra Flutter application.
