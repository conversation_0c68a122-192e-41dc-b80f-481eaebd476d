//! Core types and data structures for the chart engine

use serde::{Deserialize, Serialize};

/// Represents a price tick with timestamp
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Serialize, Deserialize)]
pub struct Tick {
    pub price: f64,
    pub timestamp: i64,
}

impl Tick {
    pub fn new(price: f64, timestamp: i64) -> Self {
        Self { price, timestamp }
    }
}

/// Chart configuration parameters
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ChartConfig {
    pub time_interval_seconds: i32,
    pub max_candlesticks: usize,
    pub high_price: Option<f64>,
    pub start_time: Option<i64>,
    pub horizontal_zoom: f64,
    pub vertical_zoom: f64,
    pub horizontal_offset: f64,
    pub vertical_offset: f64,
}

impl Default for ChartConfig {
    fn default() -> Self {
        Self {
            time_interval_seconds: 300, // 5 minutes
            max_candlesticks: 1000,
            high_price: None,
            start_time: None,
            horizontal_zoom: 1.0,
            vertical_zoom: 1.0,
            horizontal_offset: 0.0,
            vertical_offset: 0.0,
        }
    }
}

/// Indicator types supported by the engine
#[derive(Debug, <PERSON><PERSON>, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum IndicatorType {
    SMA,
    EMA,
    RSI,
    MACD,
    BollingerBands,
    ATR,
}

/// Indicator configuration parameters
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IndicatorConfig {
    pub indicator_type: IndicatorType,
    pub period: i32,
    pub fast_period: Option<i32>,
    pub slow_period: Option<i32>,
    pub signal_period: Option<i32>,
    pub std_dev_multiplier: Option<f64>,
    pub use_wilders_smoothing: Option<bool>,
}

impl IndicatorConfig {
    pub fn sma(period: i32) -> Self {
        Self {
            indicator_type: IndicatorType::SMA,
            period,
            fast_period: None,
            slow_period: None,
            signal_period: None,
            std_dev_multiplier: None,
            use_wilders_smoothing: None,
        }
    }

    pub fn ema(period: i32) -> Self {
        Self {
            indicator_type: IndicatorType::EMA,
            period,
            fast_period: None,
            slow_period: None,
            signal_period: None,
            std_dev_multiplier: None,
            use_wilders_smoothing: None,
        }
    }

    pub fn rsi(period: i32) -> Self {
        Self {
            indicator_type: IndicatorType::RSI,
            period,
            fast_period: None,
            slow_period: None,
            signal_period: None,
            std_dev_multiplier: None,
            use_wilders_smoothing: None,
        }
    }

    pub fn macd(fast_period: i32, slow_period: i32, signal_period: i32) -> Self {
        Self {
            indicator_type: IndicatorType::MACD,
            period: 0, // Not used for MACD
            fast_period: Some(fast_period),
            slow_period: Some(slow_period),
            signal_period: Some(signal_period),
            std_dev_multiplier: None,
            use_wilders_smoothing: None,
        }
    }

    pub fn bollinger_bands(period: i32, std_dev_multiplier: f64) -> Self {
        Self {
            indicator_type: IndicatorType::BollingerBands,
            period,
            fast_period: None,
            slow_period: None,
            signal_period: None,
            std_dev_multiplier: Some(std_dev_multiplier),
            use_wilders_smoothing: None,
        }
    }

    pub fn atr(period: i32, use_wilders_smoothing: bool) -> Self {
        Self {
            indicator_type: IndicatorType::ATR,
            period,
            fast_period: None,
            slow_period: None,
            signal_period: None,
            std_dev_multiplier: None,
            use_wilders_smoothing: Some(use_wilders_smoothing),
        }
    }
}

/// Indicator data point
#[derive(Debug, Clone, Copy, PartialEq, Serialize, Deserialize)]
pub struct IndicatorDataPoint {
    pub value: f64,
    pub timestamp: i64,
    pub is_valid: bool,
}

impl IndicatorDataPoint {
    pub fn new(value: f64, timestamp: i64, is_valid: bool) -> Self {
        Self {
            value,
            timestamp,
            is_valid,
        }
    }

    pub fn invalid(timestamp: i64) -> Self {
        Self {
            value: 0.0,
            timestamp,
            is_valid: false,
        }
    }
}

/// Error types for the chart engine
#[derive(Debug, thiserror::Error)]
pub enum ChartError {
    #[error("Invalid time interval: {0}")]
    InvalidTimeInterval(i32),
    
    #[error("Invalid candlestick index: {0}")]
    InvalidCandlestickIndex(usize),
    
    #[error("Invalid indicator index: {0}")]
    InvalidIndicatorIndex(usize),
    
    #[error("Invalid indicator configuration: {0}")]
    InvalidIndicatorConfig(String),
    
    #[error("Insufficient data for calculation")]
    InsufficientData,
    
    #[error("Math error: {0}")]
    MathError(String),
}

pub type ChartResult<T> = Result<T, ChartError>;
