//! Utility functions for the chart engine

use crate::types::ChartResult;

/// Mathematical utilities
pub mod math {

    /// Calculate simple moving average
    pub fn simple_moving_average(values: &[f64]) -> Option<f64> {
        if values.is_empty() {
            return None;
        }
        
        let sum: f64 = values.iter().sum();
        Some(sum / values.len() as f64)
    }

    /// Calculate exponential moving average
    pub fn exponential_moving_average(values: &[f64], alpha: f64) -> Option<f64> {
        if values.is_empty() {
            return None;
        }
        
        let mut ema = values[0];
        for &value in &values[1..] {
            ema = alpha * value + (1.0 - alpha) * ema;
        }
        
        Some(ema)
    }

    /// Calculate standard deviation
    pub fn standard_deviation(values: &[f64]) -> Option<f64> {
        if values.len() < 2 {
            return None;
        }
        
        let mean = simple_moving_average(values)?;
        let variance: f64 = values.iter()
            .map(|&x| {
                let diff = x - mean;
                diff * diff
            })
            .sum::<f64>() / values.len() as f64;
        
        Some(variance.sqrt())
    }

    /// Calculate variance
    pub fn variance(values: &[f64]) -> Option<f64> {
        if values.len() < 2 {
            return None;
        }
        
        let mean = simple_moving_average(values)?;
        let variance: f64 = values.iter()
            .map(|&x| {
                let diff = x - mean;
                diff * diff
            })
            .sum::<f64>() / values.len() as f64;
        
        Some(variance)
    }

    /// Calculate correlation coefficient between two series
    pub fn correlation(x: &[f64], y: &[f64]) -> Option<f64> {
        if x.len() != y.len() || x.len() < 2 {
            return None;
        }
        
        let mean_x = simple_moving_average(x)?;
        let mean_y = simple_moving_average(y)?;
        
        let mut numerator = 0.0;
        let mut sum_sq_x = 0.0;
        let mut sum_sq_y = 0.0;
        
        for i in 0..x.len() {
            let diff_x = x[i] - mean_x;
            let diff_y = y[i] - mean_y;
            
            numerator += diff_x * diff_y;
            sum_sq_x += diff_x * diff_x;
            sum_sq_y += diff_y * diff_y;
        }
        
        let denominator = (sum_sq_x * sum_sq_y).sqrt();
        if denominator == 0.0 {
            return None;
        }
        
        Some(numerator / denominator)
    }

    /// Linear regression slope
    pub fn linear_regression_slope(x: &[f64], y: &[f64]) -> Option<f64> {
        if x.len() != y.len() || x.len() < 2 {
            return None;
        }
        
        let n = x.len() as f64;
        let sum_x: f64 = x.iter().sum();
        let sum_y: f64 = y.iter().sum();
        let sum_xy: f64 = x.iter().zip(y.iter()).map(|(&xi, &yi)| xi * yi).sum();
        let sum_x_sq: f64 = x.iter().map(|&xi| xi * xi).sum();
        
        let denominator = n * sum_x_sq - sum_x * sum_x;
        if denominator == 0.0 {
            return None;
        }
        
        Some((n * sum_xy - sum_x * sum_y) / denominator)
    }

    /// Check if a value is approximately equal to another within a tolerance
    pub fn approx_equal(a: f64, b: f64, tolerance: f64) -> bool {
        (a - b).abs() <= tolerance
    }

    /// Clamp a value between min and max
    pub fn clamp(value: f64, min: f64, max: f64) -> f64 {
        if value < min {
            min
        } else if value > max {
            max
        } else {
            value
        }
    }
}

/// Time utilities
pub mod time {
    use chrono::{DateTime, Utc, TimeZone};

    /// Convert timestamp (milliseconds since epoch) to DateTime
    pub fn timestamp_to_datetime(timestamp: i64) -> DateTime<Utc> {
        Utc.timestamp_millis_opt(timestamp).single().unwrap_or_else(|| Utc::now())
    }

    /// Convert DateTime to timestamp (milliseconds since epoch)
    pub fn datetime_to_timestamp(datetime: DateTime<Utc>) -> i64 {
        datetime.timestamp_millis()
    }

    /// Align timestamp to interval boundary
    pub fn align_timestamp(timestamp: i64, interval_seconds: i32) -> i64 {
        let interval_ms = interval_seconds as i64 * 1000;
        (timestamp / interval_ms) * interval_ms
    }

    /// Check if two timestamps are in the same interval
    pub fn same_interval(timestamp1: i64, timestamp2: i64, interval_seconds: i32) -> bool {
        let aligned1 = align_timestamp(timestamp1, interval_seconds);
        let aligned2 = align_timestamp(timestamp2, interval_seconds);
        aligned1 == aligned2
    }

    /// Get the start of the next interval
    pub fn next_interval_start(timestamp: i64, interval_seconds: i32) -> i64 {
        let aligned = align_timestamp(timestamp, interval_seconds);
        aligned + (interval_seconds as i64 * 1000)
    }
}

/// Validation utilities
pub mod validation {
    use super::*;

    /// Validate that a price is positive
    pub fn validate_price(price: f64) -> ChartResult<()> {
        if price <= 0.0 || !price.is_finite() {
            Err(crate::types::ChartError::MathError("Price must be positive and finite".to_string()))
        } else {
            Ok(())
        }
    }

    /// Validate that a timestamp is reasonable
    pub fn validate_timestamp(timestamp: i64) -> ChartResult<()> {
        // Check for reasonable timestamp range (year 1970 to 2100)
        if timestamp < 0 || timestamp > 4_102_444_800_000 {
            Err(crate::types::ChartError::MathError("Timestamp out of reasonable range".to_string()))
        } else {
            Ok(())
        }
    }

    /// Validate that a period is positive
    pub fn validate_period(period: i32) -> ChartResult<()> {
        if period <= 0 {
            Err(crate::types::ChartError::InvalidIndicatorConfig("Period must be positive".to_string()))
        } else {
            Ok(())
        }
    }

    /// Validate OHLC values
    pub fn validate_ohlc(open: f64, high: f64, low: f64, close: f64) -> ChartResult<()> {
        validate_price(open)?;
        validate_price(high)?;
        validate_price(low)?;
        validate_price(close)?;

        if high < low {
            return Err(crate::types::ChartError::MathError("High must be >= low".to_string()));
        }

        if high < open || high < close {
            return Err(crate::types::ChartError::MathError("High must be >= open and close".to_string()));
        }

        if low > open || low > close {
            return Err(crate::types::ChartError::MathError("Low must be <= open and close".to_string()));
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_simple_moving_average() {
        let values = vec![1.0, 2.0, 3.0, 4.0, 5.0];
        let sma = math::simple_moving_average(&values).unwrap();
        assert_eq!(sma, 3.0);

        let empty: Vec<f64> = vec![];
        assert!(math::simple_moving_average(&empty).is_none());
    }

    #[test]
    fn test_standard_deviation() {
        let values = vec![2.0, 4.0, 4.0, 4.0, 5.0, 5.0, 7.0, 9.0];
        let std_dev = math::standard_deviation(&values).unwrap();
        assert!((std_dev - 2.0).abs() < 0.01);
    }

    #[test]
    fn test_timestamp_alignment() {
        let timestamp = 1234567890123; // Some timestamp
        let aligned = time::align_timestamp(timestamp, 300); // 5 minutes
        
        // Should be aligned to 5-minute boundary
        assert_eq!(aligned % (300 * 1000), 0);
        assert!(aligned <= timestamp);
    }

    #[test]
    fn test_validation() {
        assert!(validation::validate_price(100.0).is_ok());
        assert!(validation::validate_price(-1.0).is_err());
        assert!(validation::validate_price(f64::NAN).is_err());
        
        assert!(validation::validate_period(10).is_ok());
        assert!(validation::validate_period(-1).is_err());
        
        assert!(validation::validate_ohlc(100.0, 105.0, 95.0, 102.0).is_ok());
        assert!(validation::validate_ohlc(100.0, 95.0, 105.0, 102.0).is_err()); // high < low
    }

    #[test]
    fn test_math_utilities() {
        assert!(math::approx_equal(1.0, 1.0001, 0.001));
        assert!(!math::approx_equal(1.0, 1.1, 0.001));
        
        assert_eq!(math::clamp(5.0, 0.0, 10.0), 5.0);
        assert_eq!(math::clamp(-1.0, 0.0, 10.0), 0.0);
        assert_eq!(math::clamp(15.0, 0.0, 10.0), 10.0);
    }
}
