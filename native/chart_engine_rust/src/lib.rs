//! # Chart Engine Rust
//! 
//! A high-performance native chart engine written in Rust for Flutter applications.
//! Provides real-time candlestick chart rendering with technical indicators.

pub mod chart_engine;
pub mod candlestick;
pub mod indicators;
pub mod ffi;
pub mod types;
pub mod utils;

// Re-export main types for easier access
pub use chart_engine::ChartEngine;
pub use candlestick::Candlestick;
pub use types::*;

// FFI exports - these match the C++ API exactly
pub use ffi::*;

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_chart_engine_creation() {
        let engine = ChartEngine::new();
        assert_eq!(engine.get_candlestick_count(), 0);
    }

    #[test]
    fn test_candlestick_creation() {
        let candlestick = Candlestick::new(100.0, 105.0, 95.0, 102.0, 1000, 1300);
        assert_eq!(candlestick.open, 100.0);
        assert_eq!(candlestick.high, 105.0);
        assert_eq!(candlestick.low, 95.0);
        assert_eq!(candlestick.close, 102.0);
    }

    #[test]
    fn test_tick_processing() {
        let mut engine = ChartEngine::new();
        let _ = engine.set_time_interval(300); // 5 minutes = 300 seconds = 300,000 ms

        // Feed some ticks within the first interval (0-300,000 ms)
        engine.feed_tick(100.0, 1000).unwrap();
        engine.feed_tick(101.0, 1100).unwrap();
        engine.feed_tick(99.0, 1200).unwrap();
        engine.feed_tick(102.0, 1300).unwrap();

        // No completed candlesticks yet (all ticks in same interval)
        assert_eq!(engine.get_candlestick_count(), 0);

        // Add a tick in the next interval to complete the first candlestick
        engine.feed_tick(103.0, 300_001).unwrap(); // Next interval

        assert_eq!(engine.get_candlestick_count(), 1);

        let candlestick = engine.get_candlestick(0).unwrap();
        assert_eq!(candlestick.open, 100.0);
        assert_eq!(candlestick.high, 102.0);
        assert_eq!(candlestick.low, 99.0);
        assert_eq!(candlestick.close, 102.0);
    }
}
