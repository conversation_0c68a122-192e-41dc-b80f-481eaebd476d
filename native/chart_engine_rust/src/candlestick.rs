//! Candlestick data structure and operations

use crate::types::{Tick, ChartResult, ChartError};
use serde::{Deserialize, Serialize};

/// Represents a candlestick (OHLC) data point
/// This struct matches the C++ Candlestick struct exactly for FFI compatibility
#[repr(C)]
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Serialize, Deserialize)]
pub struct Candlestick {
    pub open: f64,
    pub high: f64,
    pub low: f64,
    pub close: f64,
    pub start_time: i64,
    pub end_time: i64,
}

impl Candlestick {
    /// Create a new candlestick
    pub fn new(open: f64, high: f64, low: f64, close: f64, start_time: i64, end_time: i64) -> Self {
        Self {
            open,
            high,
            low,
            close,
            start_time,
            end_time,
        }
    }

    /// Create a candlestick from the first tick
    pub fn from_tick(tick: Tick, interval_seconds: i32) -> Self {
        let start_time = Self::align_timestamp(tick.timestamp, interval_seconds);
        let end_time = start_time + (interval_seconds as i64 * 1000);
        
        Self {
            open: tick.price,
            high: tick.price,
            low: tick.price,
            close: tick.price,
            start_time,
            end_time,
        }
    }

    /// Update candlestick with a new tick
    pub fn update_with_tick(&mut self, tick: Tick) -> ChartResult<()> {
        // Verify tick is within this candlestick's time range
        if tick.timestamp < self.start_time || tick.timestamp >= self.end_time {
            return Err(ChartError::InvalidTimeInterval(
                (tick.timestamp - self.start_time) as i32
            ));
        }

        // Update OHLC values
        if tick.price > self.high {
            self.high = tick.price;
        }
        if tick.price < self.low {
            self.low = tick.price;
        }
        self.close = tick.price;

        Ok(())
    }

    /// Check if a tick belongs to this candlestick
    pub fn contains_tick(&self, tick: Tick) -> bool {
        tick.timestamp >= self.start_time && tick.timestamp < self.end_time
    }

    /// Get the typical price (HLC/3)
    pub fn typical_price(&self) -> f64 {
        (self.high + self.low + self.close) / 3.0
    }

    /// Get the median price (HL/2)
    pub fn median_price(&self) -> f64 {
        (self.high + self.low) / 2.0
    }

    /// Get the weighted close price (HLCC/4)
    pub fn weighted_close(&self) -> f64 {
        (self.high + self.low + self.close + self.close) / 4.0
    }

    /// Get the true range for ATR calculation
    pub fn true_range(&self, previous_close: Option<f64>) -> f64 {
        match previous_close {
            Some(prev_close) => {
                let hl = self.high - self.low;
                let hc = (self.high - prev_close).abs();
                let lc = (self.low - prev_close).abs();
                hl.max(hc).max(lc)
            }
            None => self.high - self.low,
        }
    }

    /// Check if this is a bullish candlestick
    pub fn is_bullish(&self) -> bool {
        self.close > self.open
    }

    /// Check if this is a bearish candlestick
    pub fn is_bearish(&self) -> bool {
        self.close < self.open
    }

    /// Check if this is a doji candlestick (open ≈ close)
    pub fn is_doji(&self, threshold: f64) -> bool {
        (self.close - self.open).abs() <= threshold
    }

    /// Get the body size (absolute difference between open and close)
    pub fn body_size(&self) -> f64 {
        (self.close - self.open).abs()
    }

    /// Get the upper shadow size
    pub fn upper_shadow(&self) -> f64 {
        self.high - self.open.max(self.close)
    }

    /// Get the lower shadow size
    pub fn lower_shadow(&self) -> f64 {
        self.open.min(self.close) - self.low
    }

    /// Align timestamp to interval boundary
    /// This ensures candlesticks start at regular intervals
    fn align_timestamp(timestamp: i64, interval_seconds: i32) -> i64 {
        let interval_ms = interval_seconds as i64 * 1000;
        (timestamp / interval_ms) * interval_ms
    }

    /// Validate candlestick data integrity
    pub fn validate(&self) -> ChartResult<()> {
        if self.high < self.low {
            return Err(ChartError::MathError("High price cannot be less than low price".to_string()));
        }
        
        if self.high < self.open || self.high < self.close {
            return Err(ChartError::MathError("High price must be >= open and close prices".to_string()));
        }
        
        if self.low > self.open || self.low > self.close {
            return Err(ChartError::MathError("Low price must be <= open and close prices".to_string()));
        }
        
        if self.start_time >= self.end_time {
            return Err(ChartError::MathError("Start time must be before end time".to_string()));
        }

        Ok(())
    }
}

impl Default for Candlestick {
    fn default() -> Self {
        Self {
            open: 0.0,
            high: 0.0,
            low: 0.0,
            close: 0.0,
            start_time: 0,
            end_time: 0,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_candlestick_creation() {
        let candle = Candlestick::new(100.0, 105.0, 95.0, 102.0, 1000, 1300);
        assert_eq!(candle.open, 100.0);
        assert_eq!(candle.high, 105.0);
        assert_eq!(candle.low, 95.0);
        assert_eq!(candle.close, 102.0);
        assert!(candle.validate().is_ok());
    }

    #[test]
    fn test_candlestick_from_tick() {
        let tick = Tick::new(100.0, 1234567890);
        let candle = Candlestick::from_tick(tick, 300);
        
        assert_eq!(candle.open, 100.0);
        assert_eq!(candle.high, 100.0);
        assert_eq!(candle.low, 100.0);
        assert_eq!(candle.close, 100.0);
    }

    #[test]
    fn test_candlestick_update() {
        let tick1 = Tick::new(100.0, 1000);
        let mut candle = Candlestick::from_tick(tick1, 300);
        
        let tick2 = Tick::new(105.0, 1100);
        candle.update_with_tick(tick2).unwrap();
        
        let tick3 = Tick::new(95.0, 1200);
        candle.update_with_tick(tick3).unwrap();
        
        assert_eq!(candle.open, 100.0);
        assert_eq!(candle.high, 105.0);
        assert_eq!(candle.low, 95.0);
        assert_eq!(candle.close, 95.0);
    }

    #[test]
    fn test_candlestick_properties() {
        let candle = Candlestick::new(100.0, 105.0, 95.0, 102.0, 1000, 1300);
        
        assert!(candle.is_bullish());
        assert!(!candle.is_bearish());
        assert_eq!(candle.body_size(), 2.0);
        assert_eq!(candle.upper_shadow(), 3.0);
        assert_eq!(candle.lower_shadow(), 5.0);
        assert_eq!(candle.typical_price(), (105.0 + 95.0 + 102.0) / 3.0);
    }
}
