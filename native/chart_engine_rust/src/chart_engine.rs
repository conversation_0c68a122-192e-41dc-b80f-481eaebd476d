//! Core chart engine implementation

use crate::candlestick::Candlestick;
use crate::indicators::IndicatorManager;
use crate::types::{Tick, ChartConfig, ChartResult, ChartError, IndicatorConfig, IndicatorDataPoint};
use parking_lot::RwLock;
use std::collections::VecDeque;
use std::sync::Arc;

/// Main chart engine that manages candlestick data and indicators
pub struct ChartEngine {
    /// Chart configuration
    config: Arc<RwLock<ChartConfig>>,
    
    /// Candlestick data storage
    candlesticks: Arc<RwLock<VecDeque<Candlestick>>>,
    
    /// Current incomplete candlestick being built
    current_candlestick: Arc<RwLock<Option<Candlestick>>>,
    
    /// Indicator manager
    indicators: Arc<RwLock<IndicatorManager>>,
    
    /// Last processed tick timestamp
    last_tick_timestamp: Arc<RwLock<Option<i64>>>,
}

impl ChartEngine {
    /// Create a new chart engine instance
    pub fn new() -> Self {
        Self {
            config: Arc::new(RwLock::new(ChartConfig::default())),
            candlesticks: Arc::new(RwLock::new(VecDeque::new())),
            current_candlestick: Arc::new(RwLock::new(None)),
            indicators: Arc::new(RwLock::new(IndicatorManager::new())),
            last_tick_timestamp: Arc::new(RwLock::new(None)),
        }
    }

    /// Feed a new price tick to the engine
    pub fn feed_tick(&mut self, price: f64, timestamp: i64) -> ChartResult<()> {
        let tick = Tick::new(price, timestamp);
        
        // Validate tick
        if price <= 0.0 {
            return Err(ChartError::MathError("Price must be positive".to_string()));
        }

        // Check if tick is newer than last processed tick
        {
            let last_timestamp = self.last_tick_timestamp.read();
            if let Some(last) = *last_timestamp {
                if timestamp <= last {
                    return Err(ChartError::MathError("Tick timestamp must be newer than previous tick".to_string()));
                }
            }
        }

        let config = self.config.read();
        let interval_seconds = config.time_interval_seconds;
        drop(config);

        // Process the tick
        self.process_tick(tick, interval_seconds)?;
        
        // Update last tick timestamp
        *self.last_tick_timestamp.write() = Some(timestamp);

        Ok(())
    }

    /// Process a tick and update candlesticks
    fn process_tick(&mut self, tick: Tick, interval_seconds: i32) -> ChartResult<()> {
        let mut current_candle = self.current_candlestick.write();
        
        match current_candle.as_mut() {
            Some(candle) => {
                // Check if tick belongs to current candlestick
                if candle.contains_tick(tick) {
                    candle.update_with_tick(tick)?;
                } else {
                    // Finalize current candlestick and start a new one
                    let completed_candle = *candle;
                    self.add_completed_candlestick(completed_candle);
                    
                    // Start new candlestick
                    *candle = Candlestick::from_tick(tick, interval_seconds);
                }
            }
            None => {
                // Start first candlestick
                *current_candle = Some(Candlestick::from_tick(tick, interval_seconds));
            }
        }

        Ok(())
    }

    /// Add a completed candlestick to the collection
    fn add_completed_candlestick(&self, candlestick: Candlestick) {
        let mut candlesticks = self.candlesticks.write();
        let config = self.config.read();
        
        candlesticks.push_back(candlestick);
        
        // Limit the number of candlesticks to prevent memory growth
        while candlesticks.len() > config.max_candlesticks {
            candlesticks.pop_front();
        }
        
        drop(config);
        
        // Update indicators with new candlestick
        let mut indicators = self.indicators.write();
        indicators.update_with_candlestick(candlestick);
    }

    /// Get the number of completed candlesticks
    pub fn get_candlestick_count(&self) -> usize {
        self.candlesticks.read().len()
    }

    /// Get a candlestick by index
    pub fn get_candlestick(&self, index: usize) -> ChartResult<Candlestick> {
        let candlesticks = self.candlesticks.read();
        
        candlesticks.get(index)
            .copied()
            .ok_or(ChartError::InvalidCandlestickIndex(index))
    }

    /// Get all candlesticks
    pub fn get_all_candlesticks(&self) -> Vec<Candlestick> {
        self.candlesticks.read().iter().copied().collect()
    }

    /// Set the time interval for candlesticks
    pub fn set_time_interval(&mut self, seconds: i32) -> ChartResult<()> {
        if seconds <= 0 {
            return Err(ChartError::InvalidTimeInterval(seconds));
        }

        {
            let mut config = self.config.write();
            config.time_interval_seconds = seconds;
        }

        // Clear current data when interval changes
        self.clear_data();
        
        Ok(())
    }

    /// Get the current time interval
    pub fn get_time_interval(&self) -> i32 {
        self.config.read().time_interval_seconds
    }

    /// Set high price for scaling
    pub fn set_high_price(&mut self, high: f64) {
        let mut config = self.config.write();
        config.high_price = Some(high);
    }

    /// Set start time
    pub fn set_start_time(&mut self, timestamp: i64) {
        let mut config = self.config.write();
        config.start_time = Some(timestamp);
    }

    /// Set zoom scales
    pub fn set_zoom_scales(&mut self, horizontal_zoom: f64, vertical_zoom: f64) {
        let mut config = self.config.write();
        config.horizontal_zoom = horizontal_zoom;
        config.vertical_zoom = vertical_zoom;
    }

    /// Set pan offsets
    pub fn set_pan_offsets(&mut self, horizontal_offset: f64, vertical_offset: f64) {
        let mut config = self.config.write();
        config.horizontal_offset = horizontal_offset;
        config.vertical_offset = vertical_offset;
    }

    /// Get chart configuration
    pub fn get_config(&self) -> ChartConfig {
        self.config.read().clone()
    }

    /// Clear all data
    pub fn clear_data(&mut self) {
        self.candlesticks.write().clear();
        *self.current_candlestick.write() = None;
        self.indicators.write().clear();
        *self.last_tick_timestamp.write() = None;
    }

    /// Add an indicator
    pub fn add_indicator(&mut self, config: IndicatorConfig) -> ChartResult<usize> {
        let mut indicators = self.indicators.write();
        let indicator_id = indicators.add_indicator(config)?;
        
        // Update indicator with existing candlesticks
        let candlesticks = self.candlesticks.read();
        for candlestick in candlesticks.iter() {
            indicators.update_with_candlestick(*candlestick);
        }
        
        Ok(indicator_id)
    }

    /// Remove an indicator
    pub fn remove_indicator(&mut self, indicator_id: usize) -> ChartResult<bool> {
        let mut indicators = self.indicators.write();
        Ok(indicators.remove_indicator(indicator_id))
    }

    /// Clear all indicators
    pub fn clear_all_indicators(&mut self) {
        self.indicators.write().clear();
    }

    /// Get number of indicators
    pub fn get_indicator_count(&self) -> usize {
        self.indicators.read().get_indicator_count()
    }

    /// Get indicator name by index
    pub fn get_indicator_name(&self, index: usize) -> Option<String> {
        self.indicators.read().get_indicator_name(index)
    }

    /// Get indicator data count
    pub fn get_indicator_data_count(&self, indicator_index: usize) -> usize {
        self.indicators.read().get_indicator_data_count(indicator_index)
    }

    /// Get indicator data point
    pub fn get_indicator_data_point(&self, indicator_index: usize, data_index: usize) -> Option<IndicatorDataPoint> {
        self.indicators.read().get_indicator_data_point(indicator_index, data_index)
    }
}

impl Default for ChartEngine {
    fn default() -> Self {
        Self::new()
    }
}

// Thread-safe implementation
unsafe impl Send for ChartEngine {}
unsafe impl Sync for ChartEngine {}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_chart_engine_creation() {
        let engine = ChartEngine::new();
        assert_eq!(engine.get_candlestick_count(), 0);
        assert_eq!(engine.get_time_interval(), 300);
    }

    #[test]
    fn test_feed_tick() {
        let mut engine = ChartEngine::new();
        
        assert!(engine.feed_tick(100.0, 1000).is_ok());
        assert!(engine.feed_tick(101.0, 1100).is_ok());
        assert!(engine.feed_tick(99.0, 1200).is_ok());
        
        // Should reject older timestamp
        assert!(engine.feed_tick(98.0, 1100).is_err());
        
        // Should reject negative price
        assert!(engine.feed_tick(-1.0, 1300).is_err());
    }

    #[test]
    fn test_candlestick_completion() {
        let mut engine = ChartEngine::new();
        engine.set_time_interval(1).unwrap(); // 1 second intervals

        // First candlestick (0-1000ms)
        engine.feed_tick(100.0, 500).unwrap();
        engine.feed_tick(101.0, 800).unwrap();
        assert_eq!(engine.get_candlestick_count(), 0); // Still building

        // Complete first candlestick and start second (1000ms+)
        engine.feed_tick(102.0, 1500).unwrap();
        assert_eq!(engine.get_candlestick_count(), 1);

        let candle = engine.get_candlestick(0).unwrap();
        assert_eq!(candle.open, 100.0);
        assert_eq!(candle.high, 101.0);
        assert_eq!(candle.close, 101.0);
    }
}
