//! Exponential Moving Average (EMA) indicator

use super::Indicator;
use crate::candlestick::Candlestick;
use crate::types::IndicatorDataPoint;

/// Exponential Moving Average indicator
pub struct EMA {
    #[allow(dead_code)]
    period: usize,
    name: String,
    alpha: f64,
    current_ema: Option<f64>,
    data_points: Vec<IndicatorDataPoint>,
    initialized: bool,
}

impl EMA {
    /// Create a new EMA indicator
    pub fn new(period: usize) -> Self {
        let alpha = 2.0 / (period as f64 + 1.0);
        
        Self {
            period,
            name: format!("EMA({})", period),
            alpha,
            current_ema: None,
            data_points: Vec::new(),
            initialized: false,
        }
    }

    /// Initialize EMA with the first price (use as initial EMA value)
    fn initialize(&mut self, price: f64) {
        self.current_ema = Some(price);
        self.initialized = true;
    }

    /// Update EMA with new price
    fn update_ema(&mut self, price: f64) {
        match self.current_ema {
            Some(prev_ema) => {
                // EMA = α * current_price + (1 - α) * previous_EMA
                let new_ema = self.alpha * price + (1.0 - self.alpha) * prev_ema;
                self.current_ema = Some(new_ema);
            }
            None => {
                self.initialize(price);
            }
        }
    }
}

impl Indicator for EMA {
    fn update(&mut self, candlestick: Candlestick) {
        // Use closing price for EMA calculation
        let price = candlestick.close;
        
        // Update EMA
        self.update_ema(price);
        
        // Create data point
        let data_point = match self.current_ema {
            Some(value) => IndicatorDataPoint::new(value, candlestick.end_time, self.initialized),
            None => IndicatorDataPoint::invalid(candlestick.end_time),
        };
        
        self.data_points.push(data_point);
    }

    fn name(&self) -> &str {
        &self.name
    }

    fn data_count(&self) -> usize {
        self.data_points.len()
    }

    fn get_data_point(&self, index: usize) -> Option<IndicatorDataPoint> {
        self.data_points.get(index).copied()
    }

    fn get_all_data(&self) -> Vec<IndicatorDataPoint> {
        self.data_points.clone()
    }

    fn clear(&mut self) {
        self.current_ema = None;
        self.data_points.clear();
        self.initialized = false;
    }

    fn is_ready(&self) -> bool {
        self.initialized
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_ema_creation() {
        let ema = EMA::new(10);
        assert_eq!(ema.name(), "EMA(10)");
        assert_eq!(ema.data_count(), 0);
        assert!(!ema.is_ready());
        
        // Check alpha calculation: 2 / (10 + 1) = 0.1818...
        assert!((ema.alpha - 0.18181818181818182).abs() < 1e-10);
    }

    #[test]
    fn test_ema_calculation() {
        let mut ema = EMA::new(3);
        // Alpha for period 3: 2 / (3 + 1) = 0.5
        
        // Add first candlestick - should initialize EMA
        let candle1 = Candlestick::new(100.0, 105.0, 95.0, 102.0, 1000, 1300);
        ema.update(candle1);
        assert_eq!(ema.data_count(), 1);
        assert!(ema.is_ready());
        
        let data_point1 = ema.get_data_point(0).unwrap();
        assert!(data_point1.is_valid);
        assert_eq!(data_point1.value, 102.0); // First value becomes initial EMA
        
        // Add second candlestick
        let candle2 = Candlestick::new(102.0, 108.0, 98.0, 104.0, 1300, 1600);
        ema.update(candle2);
        assert_eq!(ema.data_count(), 2);
        
        // EMA = 0.5 * 104 + 0.5 * 102 = 103
        let data_point2 = ema.get_data_point(1).unwrap();
        assert!(data_point2.is_valid);
        assert_eq!(data_point2.value, 103.0);
        
        // Add third candlestick
        let candle3 = Candlestick::new(104.0, 110.0, 100.0, 106.0, 1600, 1900);
        ema.update(candle3);
        assert_eq!(ema.data_count(), 3);
        
        // EMA = 0.5 * 106 + 0.5 * 103 = 104.5
        let data_point3 = ema.get_data_point(2).unwrap();
        assert!(data_point3.is_valid);
        assert_eq!(data_point3.value, 104.5);
    }

    #[test]
    fn test_ema_alpha_calculation() {
        let ema2 = EMA::new(2);
        assert!((ema2.alpha - 2.0/3.0).abs() < 1e-10);
        
        let ema10 = EMA::new(10);
        assert!((ema10.alpha - 2.0/11.0).abs() < 1e-10);
        
        let ema20 = EMA::new(20);
        assert!((ema20.alpha - 2.0/21.0).abs() < 1e-10);
    }

    #[test]
    fn test_ema_clear() {
        let mut ema = EMA::new(5);
        
        let candle = Candlestick::new(100.0, 105.0, 95.0, 102.0, 1000, 1300);
        ema.update(candle);
        assert_eq!(ema.data_count(), 1);
        assert!(ema.is_ready());
        
        ema.clear();
        assert_eq!(ema.data_count(), 0);
        assert!(!ema.is_ready());
        assert!(ema.current_ema.is_none());
    }

    #[test]
    fn test_ema_continuous_calculation() {
        let mut ema = EMA::new(2); // Alpha = 2/3 ≈ 0.6667
        
        let prices = vec![100.0, 102.0, 104.0, 103.0, 105.0];
        let mut expected_emas = vec![100.0]; // First price
        
        // Calculate expected EMAs manually
        let alpha = 2.0 / 3.0;
        let mut current_ema = 100.0;
        
        for &price in &prices[1..] {
            current_ema = alpha * price + (1.0 - alpha) * current_ema;
            expected_emas.push(current_ema);
        }
        
        // Test with actual EMA
        for (i, &price) in prices.iter().enumerate() {
            let candle = Candlestick::new(price, price + 2.0, price - 2.0, price, 1000 + i as i64 * 300, 1300 + i as i64 * 300);
            ema.update(candle);
            
            let data_point = ema.get_data_point(i).unwrap();
            assert!(data_point.is_valid);
            assert!((data_point.value - expected_emas[i]).abs() < 1e-10, 
                   "EMA mismatch at index {}: expected {}, got {}", i, expected_emas[i], data_point.value);
        }
    }
}
