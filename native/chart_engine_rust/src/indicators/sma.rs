//! Simple Moving Average (SMA) indicator

use super::Indicator;
use crate::candlestick::Candlestick;
use crate::types::IndicatorDataPoint;
use std::collections::VecDeque;

/// Simple Moving Average indicator
pub struct SMA {
    period: usize,
    name: String,
    prices: VecDeque<f64>,
    data_points: Vec<IndicatorDataPoint>,
}

impl SMA {
    /// Create a new SMA indicator
    pub fn new(period: usize) -> Self {
        Self {
            period,
            name: format!("SMA({})", period),
            prices: VecDeque::with_capacity(period),
            data_points: Vec::new(),
        }
    }

    /// Calculate the current SMA value
    fn calculate_sma(&self) -> Option<f64> {
        if self.prices.len() < self.period {
            return None;
        }

        let sum: f64 = self.prices.iter().sum();
        Some(sum / self.period as f64)
    }
}

impl Indicator for SMA {
    fn update(&mut self, candlestick: Candlestick) {
        // Use closing price for SMA calculation
        let price = candlestick.close;
        
        // Add new price
        self.prices.push_back(price);
        
        // Remove oldest price if we exceed the period
        if self.prices.len() > self.period {
            self.prices.pop_front();
        }
        
        // Calculate SMA value
        let sma_value = self.calculate_sma();
        
        // Create data point
        let data_point = match sma_value {
            Some(value) => IndicatorDataPoint::new(value, candlestick.end_time, true),
            None => IndicatorDataPoint::invalid(candlestick.end_time),
        };
        
        self.data_points.push(data_point);
    }

    fn name(&self) -> &str {
        &self.name
    }

    fn data_count(&self) -> usize {
        self.data_points.len()
    }

    fn get_data_point(&self, index: usize) -> Option<IndicatorDataPoint> {
        self.data_points.get(index).copied()
    }

    fn get_all_data(&self) -> Vec<IndicatorDataPoint> {
        self.data_points.clone()
    }

    fn clear(&mut self) {
        self.prices.clear();
        self.data_points.clear();
    }

    fn is_ready(&self) -> bool {
        self.prices.len() >= self.period
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_sma_creation() {
        let sma = SMA::new(10);
        assert_eq!(sma.name(), "SMA(10)");
        assert_eq!(sma.data_count(), 0);
        assert!(!sma.is_ready());
    }

    #[test]
    fn test_sma_calculation() {
        let mut sma = SMA::new(3);
        
        // Add first candlestick
        let candle1 = Candlestick::new(100.0, 105.0, 95.0, 102.0, 1000, 1300);
        sma.update(candle1);
        assert_eq!(sma.data_count(), 1);
        assert!(!sma.is_ready());
        
        // Add second candlestick
        let candle2 = Candlestick::new(102.0, 108.0, 98.0, 104.0, 1300, 1600);
        sma.update(candle2);
        assert_eq!(sma.data_count(), 2);
        assert!(!sma.is_ready());
        
        // Add third candlestick - now SMA should be ready
        let candle3 = Candlestick::new(104.0, 110.0, 100.0, 106.0, 1600, 1900);
        sma.update(candle3);
        assert_eq!(sma.data_count(), 3);
        assert!(sma.is_ready());
        
        // Check SMA value: (102 + 104 + 106) / 3 = 104
        let data_point = sma.get_data_point(2).unwrap();
        assert!(data_point.is_valid);
        assert_eq!(data_point.value, 104.0);
        
        // Add fourth candlestick - should maintain period of 3
        let candle4 = Candlestick::new(106.0, 112.0, 102.0, 108.0, 1900, 2200);
        sma.update(candle4);
        assert_eq!(sma.data_count(), 4);
        
        // Check new SMA value: (104 + 106 + 108) / 3 = 106
        let data_point = sma.get_data_point(3).unwrap();
        assert!(data_point.is_valid);
        assert_eq!(data_point.value, 106.0);
    }

    #[test]
    fn test_sma_invalid_data_points() {
        let mut sma = SMA::new(5);
        
        // First few data points should be invalid
        let candle1 = Candlestick::new(100.0, 105.0, 95.0, 102.0, 1000, 1300);
        sma.update(candle1);
        
        let data_point = sma.get_data_point(0).unwrap();
        assert!(!data_point.is_valid);
        assert_eq!(data_point.timestamp, 1300);
    }

    #[test]
    fn test_sma_clear() {
        let mut sma = SMA::new(3);
        
        let candle = Candlestick::new(100.0, 105.0, 95.0, 102.0, 1000, 1300);
        sma.update(candle);
        assert_eq!(sma.data_count(), 1);
        
        sma.clear();
        assert_eq!(sma.data_count(), 0);
        assert!(!sma.is_ready());
    }
}
