//! Bollinger Bands indicator

use super::{Indicator, sma::SMA};
use crate::candlestick::Candlestick;
use crate::types::IndicatorDataPoint;
use std::collections::VecDeque;

/// Bollinger Bands indicator with upper band, middle band (SMA), and lower band
pub struct BollingerBands {
    period: usize,
    std_dev_multiplier: f64,
    name: String,
    sma: SMA,
    prices: VecDeque<f64>,
    upper_band: Vec<IndicatorDataPoint>,
    middle_band: Vec<IndicatorDataPoint>,
    lower_band: Vec<IndicatorDataPoint>,
}

impl BollingerBands {
    /// Create a new Bollinger Bands indicator
    pub fn new(period: usize, std_dev_multiplier: f64) -> Self {
        Self {
            period,
            std_dev_multiplier,
            name: format!("BB({},{})", period, std_dev_multiplier),
            sma: SMA::new(period),
            prices: VecDeque::with_capacity(period),
            upper_band: Vec::new(),
            middle_band: Vec::new(),
            lower_band: Vec::new(),
        }
    }

    /// Calculate standard deviation of prices
    fn calculate_std_dev(&self, mean: f64) -> Option<f64> {
        if self.prices.len() < self.period {
            return None;
        }

        let variance: f64 = self.prices.iter()
            .map(|&price| {
                let diff = price - mean;
                diff * diff
            })
            .sum::<f64>() / self.period as f64;

        Some(variance.sqrt())
    }

    /// Get upper band data points
    pub fn get_upper_band(&self) -> &Vec<IndicatorDataPoint> {
        &self.upper_band
    }

    /// Get middle band (SMA) data points
    pub fn get_middle_band(&self) -> &Vec<IndicatorDataPoint> {
        &self.middle_band
    }

    /// Get lower band data points
    pub fn get_lower_band(&self) -> &Vec<IndicatorDataPoint> {
        &self.lower_band
    }
}

impl Indicator for BollingerBands {
    fn update(&mut self, candlestick: Candlestick) {
        let price = candlestick.close;
        let timestamp = candlestick.end_time;
        
        // Update SMA
        self.sma.update(candlestick);
        
        // Add price to our collection
        self.prices.push_back(price);
        if self.prices.len() > self.period {
            self.prices.pop_front();
        }
        
        // Get SMA value (middle band)
        let sma_data_point = self.sma.get_data_point(self.sma.data_count() - 1).unwrap();
        self.middle_band.push(sma_data_point);
        
        if sma_data_point.is_valid {
            let sma_value = sma_data_point.value;
            
            // Calculate standard deviation
            if let Some(std_dev) = self.calculate_std_dev(sma_value) {
                let band_width = self.std_dev_multiplier * std_dev;
                
                // Calculate bands
                let upper_value = sma_value + band_width;
                let lower_value = sma_value - band_width;
                
                self.upper_band.push(IndicatorDataPoint::new(upper_value, timestamp, true));
                self.lower_band.push(IndicatorDataPoint::new(lower_value, timestamp, true));
            } else {
                self.upper_band.push(IndicatorDataPoint::invalid(timestamp));
                self.lower_band.push(IndicatorDataPoint::invalid(timestamp));
            }
        } else {
            self.upper_band.push(IndicatorDataPoint::invalid(timestamp));
            self.lower_band.push(IndicatorDataPoint::invalid(timestamp));
        }
    }

    fn name(&self) -> &str {
        &self.name
    }

    fn data_count(&self) -> usize {
        self.middle_band.len()
    }

    fn get_data_point(&self, index: usize) -> Option<IndicatorDataPoint> {
        // Return middle band (SMA) by default
        self.middle_band.get(index).copied()
    }

    fn get_all_data(&self) -> Vec<IndicatorDataPoint> {
        self.middle_band.clone()
    }

    fn clear(&mut self) {
        self.sma.clear();
        self.prices.clear();
        self.upper_band.clear();
        self.middle_band.clear();
        self.lower_band.clear();
    }

    fn is_ready(&self) -> bool {
        self.sma.is_ready()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_bollinger_bands_creation() {
        let bb = BollingerBands::new(20, 2.0);
        assert_eq!(bb.name(), "BB(20,2)");
        assert_eq!(bb.data_count(), 0);
        assert!(!bb.is_ready());
    }

    #[test]
    fn test_bollinger_bands_calculation() {
        let mut bb = BollingerBands::new(3, 2.0);
        
        // Use prices with known standard deviation
        let prices = vec![100.0, 102.0, 104.0, 103.0, 105.0];
        
        for (i, &price) in prices.iter().enumerate() {
            let candle = Candlestick::new(price, price + 1.0, price - 1.0, price, 
                                        1000 + i as i64 * 300, 1300 + i as i64 * 300);
            bb.update(candle);
        }
        
        assert_eq!(bb.data_count(), 5);
        assert_eq!(bb.get_upper_band().len(), 5);
        assert_eq!(bb.get_middle_band().len(), 5);
        assert_eq!(bb.get_lower_band().len(), 5);
        
        // Check that bands are calculated correctly after enough data
        let final_upper = bb.get_upper_band().last().unwrap();
        let final_middle = bb.get_middle_band().last().unwrap();
        let final_lower = bb.get_lower_band().last().unwrap();
        
        assert!(final_upper.is_valid);
        assert!(final_middle.is_valid);
        assert!(final_lower.is_valid);
        
        // Upper band should be greater than middle, middle greater than lower
        assert!(final_upper.value > final_middle.value);
        assert!(final_middle.value > final_lower.value);
        
        // Bands should be symmetric around the middle
        let upper_distance = final_upper.value - final_middle.value;
        let lower_distance = final_middle.value - final_lower.value;
        assert!((upper_distance - lower_distance).abs() < 1e-10);
    }

    #[test]
    fn test_bollinger_bands_std_dev() {
        let mut bb = BollingerBands::new(3, 1.0); // Use multiplier of 1 for easier testing
        
        // Use prices: 100, 101, 102 (mean = 101, std_dev = 1)
        let prices = vec![100.0, 101.0, 102.0];
        
        for (i, &price) in prices.iter().enumerate() {
            let candle = Candlestick::new(price, price + 1.0, price - 1.0, price, 
                                        1000 + i as i64 * 300, 1300 + i as i64 * 300);
            bb.update(candle);
        }
        
        let final_upper = bb.get_upper_band().last().unwrap();
        let final_middle = bb.get_middle_band().last().unwrap();
        let final_lower = bb.get_lower_band().last().unwrap();
        
        // Mean should be 101
        assert!((final_middle.value - 101.0).abs() < 1e-10);
        
        // Standard deviation should be sqrt(2/3) ≈ 0.8165
        let expected_std_dev = (2.0_f64 / 3.0).sqrt();
        let expected_upper = 101.0 + expected_std_dev;
        let expected_lower = 101.0 - expected_std_dev;
        
        assert!((final_upper.value - expected_upper).abs() < 1e-10);
        assert!((final_lower.value - expected_lower).abs() < 1e-10);
    }

    #[test]
    fn test_bollinger_bands_clear() {
        let mut bb = BollingerBands::new(3, 2.0);
        
        let candle = Candlestick::new(100.0, 105.0, 95.0, 102.0, 1000, 1300);
        bb.update(candle);
        assert_eq!(bb.data_count(), 1);
        
        bb.clear();
        assert_eq!(bb.data_count(), 0);
        assert_eq!(bb.get_upper_band().len(), 0);
        assert_eq!(bb.get_middle_band().len(), 0);
        assert_eq!(bb.get_lower_band().len(), 0);
        assert!(!bb.is_ready());
    }
}
