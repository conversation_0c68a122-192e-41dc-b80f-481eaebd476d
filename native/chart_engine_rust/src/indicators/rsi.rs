//! Relative Strength Index (RSI) indicator

use super::Indicator;
use crate::candlestick::Candlestick;
use crate::types::IndicatorDataPoint;
use std::collections::VecDeque;

/// Relative Strength Index indicator
pub struct RSI {
    period: usize,
    name: String,
    prices: VecDeque<f64>,
    gains: VecDeque<f64>,
    losses: VecDeque<f64>,
    avg_gain: Option<f64>,
    avg_loss: Option<f64>,
    data_points: Vec<IndicatorDataPoint>,
}

impl RSI {
    /// Create a new RSI indicator
    pub fn new(period: usize) -> Self {
        Self {
            period,
            name: format!("RSI({})", period),
            prices: VecDeque::with_capacity(period + 1),
            gains: VecDeque::with_capacity(period),
            losses: VecDeque::with_capacity(period),
            avg_gain: None,
            avg_loss: None,
            data_points: Vec::new(),
        }
    }

    /// Calculate RSI value
    fn calculate_rsi(&self) -> Option<f64> {
        match (self.avg_gain, self.avg_loss) {
            (Some(avg_gain), Some(avg_loss)) => {
                if avg_loss == 0.0 {
                    Some(100.0) // All gains, no losses
                } else {
                    let rs = avg_gain / avg_loss;
                    Some(100.0 - (100.0 / (1.0 + rs)))
                }
            }
            _ => None,
        }
    }

    /// Update averages using Wilder's smoothing method
    fn update_averages(&mut self, gain: f64, loss: f64) {
        match (self.avg_gain, self.avg_loss) {
            (Some(prev_avg_gain), Some(prev_avg_loss)) => {
                // Wilder's smoothing: new_avg = (prev_avg * (period - 1) + current_value) / period
                self.avg_gain = Some((prev_avg_gain * (self.period - 1) as f64 + gain) / self.period as f64);
                self.avg_loss = Some((prev_avg_loss * (self.period - 1) as f64 + loss) / self.period as f64);
            }
            _ => {
                // Initialize with simple average of first period
                if self.gains.len() >= self.period {
                    let sum_gains: f64 = self.gains.iter().sum();
                    let sum_losses: f64 = self.losses.iter().sum();
                    self.avg_gain = Some(sum_gains / self.period as f64);
                    self.avg_loss = Some(sum_losses / self.period as f64);
                }
            }
        }
    }
}

impl Indicator for RSI {
    fn update(&mut self, candlestick: Candlestick) {
        let price = candlestick.close;
        
        // Add current price
        self.prices.push_back(price);
        
        // Calculate gain/loss if we have a previous price
        if self.prices.len() >= 2 {
            let prev_price = self.prices[self.prices.len() - 2];
            let change = price - prev_price;
            
            let gain = if change > 0.0 { change } else { 0.0 };
            let loss = if change < 0.0 { -change } else { 0.0 };
            
            self.gains.push_back(gain);
            self.losses.push_back(loss);
            
            // Maintain period length
            if self.gains.len() > self.period {
                self.gains.pop_front();
                self.losses.pop_front();
            }
            
            // Update averages
            self.update_averages(gain, loss);
        }
        
        // Remove excess prices (we only need current and previous)
        if self.prices.len() > self.period + 1 {
            self.prices.pop_front();
        }
        
        // Calculate RSI
        let rsi_value = self.calculate_rsi();
        
        // Create data point
        let data_point = match rsi_value {
            Some(value) => IndicatorDataPoint::new(value, candlestick.end_time, true),
            None => IndicatorDataPoint::invalid(candlestick.end_time),
        };
        
        self.data_points.push(data_point);
    }

    fn name(&self) -> &str {
        &self.name
    }

    fn data_count(&self) -> usize {
        self.data_points.len()
    }

    fn get_data_point(&self, index: usize) -> Option<IndicatorDataPoint> {
        self.data_points.get(index).copied()
    }

    fn get_all_data(&self) -> Vec<IndicatorDataPoint> {
        self.data_points.clone()
    }

    fn clear(&mut self) {
        self.prices.clear();
        self.gains.clear();
        self.losses.clear();
        self.avg_gain = None;
        self.avg_loss = None;
        self.data_points.clear();
    }

    fn is_ready(&self) -> bool {
        self.avg_gain.is_some() && self.avg_loss.is_some()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_rsi_creation() {
        let rsi = RSI::new(14);
        assert_eq!(rsi.name(), "RSI(14)");
        assert_eq!(rsi.data_count(), 0);
        assert!(!rsi.is_ready());
    }

    #[test]
    fn test_rsi_calculation() {
        let mut rsi = RSI::new(3);
        
        // Test data: prices that should give predictable RSI values
        let prices = vec![100.0, 102.0, 101.0, 103.0, 104.0];
        
        for (i, &price) in prices.iter().enumerate() {
            let candle = Candlestick::new(price, price + 1.0, price - 1.0, price, 
                                        1000 + i as i64 * 300, 1300 + i as i64 * 300);
            rsi.update(candle);
        }
        
        assert_eq!(rsi.data_count(), 5);
        
        // First data point should be invalid (no previous price)
        let data_point0 = rsi.get_data_point(0).unwrap();
        assert!(!data_point0.is_valid);
        
        // Check that RSI is calculated after enough data
        // After 4 updates (3 price changes), RSI should be ready
        let data_point4 = rsi.get_data_point(4).unwrap();
        assert!(data_point4.is_valid);
        assert!(data_point4.value >= 0.0 && data_point4.value <= 100.0);
    }

    #[test]
    fn test_rsi_extreme_values() {
        let mut rsi = RSI::new(2);
        
        // Test all gains (should approach 100)
        let prices = vec![100.0, 101.0, 102.0, 103.0];
        
        for (i, &price) in prices.iter().enumerate() {
            let candle = Candlestick::new(price, price + 1.0, price - 1.0, price, 
                                        1000 + i as i64 * 300, 1300 + i as i64 * 300);
            rsi.update(candle);
        }
        
        let final_rsi = rsi.get_data_point(3).unwrap();
        assert!(final_rsi.is_valid);
        assert!(final_rsi.value > 50.0); // Should be high due to consistent gains
        
        // Clear and test all losses
        rsi.clear();
        let prices = vec![100.0, 99.0, 98.0, 97.0];
        
        for (i, &price) in prices.iter().enumerate() {
            let candle = Candlestick::new(price, price + 1.0, price - 1.0, price, 
                                        1000 + i as i64 * 300, 1300 + i as i64 * 300);
            rsi.update(candle);
        }
        
        let final_rsi = rsi.get_data_point(3).unwrap();
        assert!(final_rsi.is_valid);
        assert!(final_rsi.value < 50.0); // Should be low due to consistent losses
    }

    #[test]
    fn test_rsi_no_change() {
        let mut rsi = RSI::new(2);
        
        // Test no price changes (should give RSI around 50)
        let prices = vec![100.0, 100.0, 100.0, 100.0];
        
        for (i, &price) in prices.iter().enumerate() {
            let candle = Candlestick::new(price, price + 1.0, price - 1.0, price, 
                                        1000 + i as i64 * 300, 1300 + i as i64 * 300);
            rsi.update(candle);
        }
        
        // With no gains or losses, RSI calculation should handle division by zero
        let final_rsi = rsi.get_data_point(3).unwrap();
        assert!(final_rsi.is_valid);
        // When there are no losses (avg_loss = 0), RSI should be 100
        assert_eq!(final_rsi.value, 100.0);
    }

    #[test]
    fn test_rsi_clear() {
        let mut rsi = RSI::new(3);
        
        let candle = Candlestick::new(100.0, 105.0, 95.0, 102.0, 1000, 1300);
        rsi.update(candle);
        assert_eq!(rsi.data_count(), 1);
        
        rsi.clear();
        assert_eq!(rsi.data_count(), 0);
        assert!(!rsi.is_ready());
        assert!(rsi.avg_gain.is_none());
        assert!(rsi.avg_loss.is_none());
    }
}
