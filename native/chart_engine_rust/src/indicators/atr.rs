//! Average True Range (ATR) indicator

use super::Indicator;
use crate::candlestick::Candlestick;
use crate::types::IndicatorDataPoint;
use std::collections::VecDeque;

/// Average True Range indicator
pub struct ATR {
    period: usize,
    use_wilders_smoothing: bool,
    name: String,
    true_ranges: VecDeque<f64>,
    current_atr: Option<f64>,
    previous_close: Option<f64>,
    data_points: Vec<IndicatorDataPoint>,
}

impl ATR {
    /// Create a new ATR indicator
    pub fn new(period: usize, use_wilders_smoothing: bool) -> Self {
        let smoothing_type = if use_wilders_smoothing { "W" } else { "S" };
        
        Self {
            period,
            use_wilders_smoothing,
            name: format!("ATR({},{})", period, smoothing_type),
            true_ranges: VecDeque::with_capacity(period),
            current_atr: None,
            previous_close: None,
            data_points: Vec::new(),
        }
    }

    /// Calculate ATR using either <PERSON>'s smoothing or simple moving average
    fn calculate_atr(&mut self, true_range: f64) -> Option<f64> {
        self.true_ranges.push_back(true_range);
        
        if self.true_ranges.len() > self.period {
            self.true_ranges.pop_front();
        }
        
        if self.true_ranges.len() < self.period {
            return None;
        }
        
        if self.use_wilders_smoothing {
            match self.current_atr {
                Some(prev_atr) => {
                    // Wilder's smoothing: ATR = (prev_ATR * (period - 1) + current_TR) / period
                    let new_atr = (prev_atr * (self.period - 1) as f64 + true_range) / self.period as f64;
                    self.current_atr = Some(new_atr);
                    Some(new_atr)
                }
                None => {
                    // Initialize with simple average
                    let sum: f64 = self.true_ranges.iter().sum();
                    let initial_atr = sum / self.period as f64;
                    self.current_atr = Some(initial_atr);
                    Some(initial_atr)
                }
            }
        } else {
            // Simple moving average of true ranges
            let sum: f64 = self.true_ranges.iter().sum();
            Some(sum / self.period as f64)
        }
    }
}

impl Indicator for ATR {
    fn update(&mut self, candlestick: Candlestick) {
        let timestamp = candlestick.end_time;
        
        // Calculate true range
        let true_range = candlestick.true_range(self.previous_close);
        
        // Update previous close for next calculation
        self.previous_close = Some(candlestick.close);
        
        // Calculate ATR
        let atr_value = self.calculate_atr(true_range);
        
        // Create data point
        let data_point = match atr_value {
            Some(value) => IndicatorDataPoint::new(value, timestamp, true),
            None => IndicatorDataPoint::invalid(timestamp),
        };
        
        self.data_points.push(data_point);
    }

    fn name(&self) -> &str {
        &self.name
    }

    fn data_count(&self) -> usize {
        self.data_points.len()
    }

    fn get_data_point(&self, index: usize) -> Option<IndicatorDataPoint> {
        self.data_points.get(index).copied()
    }

    fn get_all_data(&self) -> Vec<IndicatorDataPoint> {
        self.data_points.clone()
    }

    fn clear(&mut self) {
        self.true_ranges.clear();
        self.current_atr = None;
        self.previous_close = None;
        self.data_points.clear();
    }

    fn is_ready(&self) -> bool {
        self.true_ranges.len() >= self.period
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_atr_creation() {
        let atr_wilders = ATR::new(14, true);
        assert_eq!(atr_wilders.name(), "ATR(14,W)");
        assert_eq!(atr_wilders.data_count(), 0);
        assert!(!atr_wilders.is_ready());
        
        let atr_simple = ATR::new(14, false);
        assert_eq!(atr_simple.name(), "ATR(14,S)");
    }

    #[test]
    fn test_atr_calculation_simple() {
        let mut atr = ATR::new(3, false); // Simple moving average
        
        // Create candlesticks with known true ranges
        let candlesticks = vec![
            Candlestick::new(100.0, 105.0, 95.0, 102.0, 1000, 1300), // TR = 10 (H-L)
            Candlestick::new(102.0, 108.0, 98.0, 104.0, 1300, 1600), // TR = max(108-98, 108-102, 102-98) = 10
            Candlestick::new(104.0, 110.0, 100.0, 106.0, 1600, 1900), // TR = max(110-100, 110-104, 104-100) = 10
        ];
        
        for candle in candlesticks {
            atr.update(candle);
        }
        
        assert_eq!(atr.data_count(), 3);
        assert!(atr.is_ready());
        
        // ATR should be (10 + 10 + 10) / 3 = 10
        let final_atr = atr.get_data_point(2).unwrap();
        assert!(final_atr.is_valid);
        assert_eq!(final_atr.value, 10.0);
    }

    #[test]
    fn test_atr_calculation_wilders() {
        let mut atr = ATR::new(2, true); // Wilder's smoothing
        
        let candlesticks = vec![
            Candlestick::new(100.0, 105.0, 95.0, 102.0, 1000, 1300), // TR = 10
            Candlestick::new(102.0, 108.0, 98.0, 104.0, 1300, 1600), // TR = 10
            Candlestick::new(104.0, 112.0, 100.0, 108.0, 1600, 1900), // TR = 12
        ];
        
        for candle in candlesticks {
            atr.update(candle);
        }
        
        assert_eq!(atr.data_count(), 3);
        
        // First ATR (after 2 periods): (10 + 10) / 2 = 10
        let second_atr = atr.get_data_point(1).unwrap();
        assert!(second_atr.is_valid);
        assert_eq!(second_atr.value, 10.0);
        
        // Third ATR with Wilder's: (10 * 1 + 12) / 2 = 11
        let third_atr = atr.get_data_point(2).unwrap();
        assert!(third_atr.is_valid);
        assert_eq!(third_atr.value, 11.0);
    }

    #[test]
    fn test_atr_true_range_calculation() {
        let mut atr = ATR::new(1, false);
        
        // First candlestick - no previous close
        let candle1 = Candlestick::new(100.0, 105.0, 95.0, 102.0, 1000, 1300);
        atr.update(candle1);
        
        let first_atr = atr.get_data_point(0).unwrap();
        assert!(first_atr.is_valid);
        assert_eq!(first_atr.value, 10.0); // H - L = 105 - 95
        
        // Second candlestick - with previous close
        let candle2 = Candlestick::new(98.0, 103.0, 96.0, 100.0, 1300, 1600);
        atr.update(candle2);
        
        let second_atr = atr.get_data_point(1).unwrap();
        assert!(second_atr.is_valid);
        // TR = max(103-96, 103-102, 102-96) = max(7, 1, 6) = 7
        assert_eq!(second_atr.value, 7.0);
    }

    #[test]
    fn test_atr_clear() {
        let mut atr = ATR::new(3, true);
        
        let candle = Candlestick::new(100.0, 105.0, 95.0, 102.0, 1000, 1300);
        atr.update(candle);
        assert_eq!(atr.data_count(), 1);
        
        atr.clear();
        assert_eq!(atr.data_count(), 0);
        assert!(!atr.is_ready());
        assert!(atr.current_atr.is_none());
        assert!(atr.previous_close.is_none());
    }
}
