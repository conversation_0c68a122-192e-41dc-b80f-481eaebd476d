//! MACD (Moving Average Convergence Divergence) indicator

use super::{Indicator, ema::EMA};
use crate::candlestick::Candlestick;
use crate::types::IndicatorDataPoint;

/// MACD indicator with three lines: MACD line, Signal line, and Histogram
pub struct MACD {
    #[allow(dead_code)]
    fast_period: usize,
    #[allow(dead_code)]
    slow_period: usize,
    #[allow(dead_code)]
    signal_period: usize,
    name: String,
    fast_ema: EMA,
    slow_ema: EMA,
    signal_ema: EMA,
    macd_line: Vec<IndicatorDataPoint>,
    signal_line: Vec<IndicatorDataPoint>,
    histogram: Vec<IndicatorDataPoint>,
}

impl MACD {
    /// Create a new MACD indicator
    pub fn new(fast_period: usize, slow_period: usize, signal_period: usize) -> Self {
        Self {
            fast_period,
            slow_period,
            signal_period,
            name: format!("MACD({},{},{})", fast_period, slow_period, signal_period),
            fast_ema: EMA::new(fast_period),
            slow_ema: EMA::new(slow_period),
            signal_ema: EMA::new(signal_period),
            macd_line: Vec::new(),
            signal_line: Vec::new(),
            histogram: Vec::new(),
        }
    }

    /// Get MACD line data points
    pub fn get_macd_line(&self) -> &Vec<IndicatorDataPoint> {
        &self.macd_line
    }

    /// Get Signal line data points
    pub fn get_signal_line(&self) -> &Vec<IndicatorDataPoint> {
        &self.signal_line
    }

    /// Get Histogram data points
    pub fn get_histogram(&self) -> &Vec<IndicatorDataPoint> {
        &self.histogram
    }
}

impl Indicator for MACD {
    fn update(&mut self, candlestick: Candlestick) {
        // Update both EMAs
        self.fast_ema.update(candlestick);
        self.slow_ema.update(candlestick);
        
        let timestamp = candlestick.end_time;
        
        // Calculate MACD line (Fast EMA - Slow EMA)
        let macd_value = if self.fast_ema.is_ready() && self.slow_ema.is_ready() {
            let fast_value = self.fast_ema.get_data_point(self.fast_ema.data_count() - 1).unwrap().value;
            let slow_value = self.slow_ema.get_data_point(self.slow_ema.data_count() - 1).unwrap().value;
            Some(fast_value - slow_value)
        } else {
            None
        };
        
        // Store MACD line data point
        let macd_data_point = match macd_value {
            Some(value) => IndicatorDataPoint::new(value, timestamp, true),
            None => IndicatorDataPoint::invalid(timestamp),
        };
        self.macd_line.push(macd_data_point);
        
        // Calculate Signal line (EMA of MACD line)
        if let Some(macd_val) = macd_value {
            // Create a temporary candlestick with MACD value as close price for signal EMA
            let macd_candle = Candlestick::new(macd_val, macd_val, macd_val, macd_val, 
                                             candlestick.start_time, timestamp);
            self.signal_ema.update(macd_candle);
            
            let signal_value = if self.signal_ema.is_ready() {
                Some(self.signal_ema.get_data_point(self.signal_ema.data_count() - 1).unwrap().value)
            } else {
                None
            };
            
            // Store Signal line data point
            let signal_data_point = match signal_value {
                Some(value) => IndicatorDataPoint::new(value, timestamp, true),
                None => IndicatorDataPoint::invalid(timestamp),
            };
            self.signal_line.push(signal_data_point);
            
            // Calculate Histogram (MACD - Signal)
            let histogram_value = match signal_value {
                Some(signal_val) => Some(macd_val - signal_val),
                None => None,
            };
            
            let histogram_data_point = match histogram_value {
                Some(value) => IndicatorDataPoint::new(value, timestamp, true),
                None => IndicatorDataPoint::invalid(timestamp),
            };
            self.histogram.push(histogram_data_point);
        } else {
            // Add invalid data points for signal and histogram
            self.signal_line.push(IndicatorDataPoint::invalid(timestamp));
            self.histogram.push(IndicatorDataPoint::invalid(timestamp));
        }
    }

    fn name(&self) -> &str {
        &self.name
    }

    fn data_count(&self) -> usize {
        self.macd_line.len()
    }

    fn get_data_point(&self, index: usize) -> Option<IndicatorDataPoint> {
        // Return MACD line data point by default
        self.macd_line.get(index).copied()
    }

    fn get_all_data(&self) -> Vec<IndicatorDataPoint> {
        self.macd_line.clone()
    }

    fn clear(&mut self) {
        self.fast_ema.clear();
        self.slow_ema.clear();
        self.signal_ema.clear();
        self.macd_line.clear();
        self.signal_line.clear();
        self.histogram.clear();
    }

    fn is_ready(&self) -> bool {
        self.fast_ema.is_ready() && self.slow_ema.is_ready()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_macd_creation() {
        let macd = MACD::new(12, 26, 9);
        assert_eq!(macd.name(), "MACD(12,26,9)");
        assert_eq!(macd.data_count(), 0);
        assert!(!macd.is_ready());
    }

    #[test]
    fn test_macd_calculation() {
        let mut macd = MACD::new(2, 4, 2); // Shorter periods for testing
        
        let prices = vec![100.0, 102.0, 104.0, 103.0, 105.0, 107.0];
        
        for (i, &price) in prices.iter().enumerate() {
            let candle = Candlestick::new(price, price + 1.0, price - 1.0, price, 
                                        1000 + i as i64 * 300, 1300 + i as i64 * 300);
            macd.update(candle);
        }
        
        assert_eq!(macd.data_count(), 6);
        assert_eq!(macd.get_macd_line().len(), 6);
        assert_eq!(macd.get_signal_line().len(), 6);
        assert_eq!(macd.get_histogram().len(), 6);
        
        // MACD should be ready after both EMAs are ready
        assert!(macd.is_ready());
        
        // Check that final values are valid
        let final_macd = macd.get_data_point(5).unwrap();
        assert!(final_macd.is_valid);
    }

    #[test]
    fn test_macd_signal_calculation() {
        let mut macd = MACD::new(2, 3, 2);
        
        // Add enough data points to get valid signal
        for i in 0..10 {
            let price = 100.0 + i as f64;
            let candle = Candlestick::new(price, price + 1.0, price - 1.0, price, 
                                        1000 + i * 300, 1300 + i * 300);
            macd.update(candle);
        }
        
        // Check that we have signal line data
        let signal_line = macd.get_signal_line();
        assert_eq!(signal_line.len(), 10);
        
        // Later data points should have valid signal values
        let final_signal = signal_line.last().unwrap();
        assert!(final_signal.is_valid);
        
        // Check histogram calculation
        let histogram = macd.get_histogram();
        let final_histogram = histogram.last().unwrap();
        assert!(final_histogram.is_valid);
        
        // Histogram should be MACD - Signal
        let final_macd_value = macd.get_data_point(9).unwrap().value;
        let expected_histogram = final_macd_value - final_signal.value;
        assert!((final_histogram.value - expected_histogram).abs() < 1e-10);
    }

    #[test]
    fn test_macd_clear() {
        let mut macd = MACD::new(2, 3, 2);
        
        let candle = Candlestick::new(100.0, 105.0, 95.0, 102.0, 1000, 1300);
        macd.update(candle);
        assert_eq!(macd.data_count(), 1);
        
        macd.clear();
        assert_eq!(macd.data_count(), 0);
        assert_eq!(macd.get_macd_line().len(), 0);
        assert_eq!(macd.get_signal_line().len(), 0);
        assert_eq!(macd.get_histogram().len(), 0);
        assert!(!macd.is_ready());
    }
}
