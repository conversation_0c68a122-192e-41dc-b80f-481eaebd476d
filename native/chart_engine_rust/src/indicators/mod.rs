//! Technical indicators module

pub mod sma;
pub mod ema;
pub mod rsi;
pub mod macd;
pub mod bollinger_bands;
pub mod atr;

use crate::candlestick::Candlestick;
use crate::types::{IndicatorConfig, IndicatorDataPoint, IndicatorType, ChartResult, ChartError};
use std::collections::HashMap;

/// Trait for all technical indicators
pub trait Indicator: Send + Sync {
    /// Update the indicator with a new candlestick
    fn update(&mut self, candlestick: Candlestick);
    
    /// Get the indicator name
    fn name(&self) -> &str;
    
    /// Get the number of data points
    fn data_count(&self) -> usize;
    
    /// Get a data point by index
    fn get_data_point(&self, index: usize) -> Option<IndicatorDataPoint>;
    
    /// Get all data points
    fn get_all_data(&self) -> Vec<IndicatorDataPoint>;
    
    /// Clear all data
    fn clear(&mut self);
    
    /// Check if the indicator has enough data to produce valid values
    fn is_ready(&self) -> bool;
}

/// Factory function to create indicators
pub fn create_indicator(config: IndicatorConfig) -> ChartResult<Box<dyn Indicator>> {
    match config.indicator_type {
        IndicatorType::SMA => {
            if config.period <= 0 {
                return Err(ChartError::InvalidIndicatorConfig("SMA period must be positive".to_string()));
            }
            Ok(Box::new(sma::SMA::new(config.period as usize)))
        }
        
        IndicatorType::EMA => {
            if config.period <= 0 {
                return Err(ChartError::InvalidIndicatorConfig("EMA period must be positive".to_string()));
            }
            Ok(Box::new(ema::EMA::new(config.period as usize)))
        }
        
        IndicatorType::RSI => {
            if config.period <= 0 {
                return Err(ChartError::InvalidIndicatorConfig("RSI period must be positive".to_string()));
            }
            Ok(Box::new(rsi::RSI::new(config.period as usize)))
        }
        
        IndicatorType::MACD => {
            let fast_period = config.fast_period.ok_or_else(|| 
                ChartError::InvalidIndicatorConfig("MACD fast period is required".to_string()))?;
            let slow_period = config.slow_period.ok_or_else(|| 
                ChartError::InvalidIndicatorConfig("MACD slow period is required".to_string()))?;
            let signal_period = config.signal_period.ok_or_else(|| 
                ChartError::InvalidIndicatorConfig("MACD signal period is required".to_string()))?;
            
            if fast_period <= 0 || slow_period <= 0 || signal_period <= 0 {
                return Err(ChartError::InvalidIndicatorConfig("MACD periods must be positive".to_string()));
            }
            
            if fast_period >= slow_period {
                return Err(ChartError::InvalidIndicatorConfig("MACD fast period must be less than slow period".to_string()));
            }
            
            Ok(Box::new(macd::MACD::new(
                fast_period as usize,
                slow_period as usize,
                signal_period as usize,
            )))
        }
        
        IndicatorType::BollingerBands => {
            if config.period <= 0 {
                return Err(ChartError::InvalidIndicatorConfig("Bollinger Bands period must be positive".to_string()));
            }
            
            let std_dev_multiplier = config.std_dev_multiplier.unwrap_or(2.0);
            if std_dev_multiplier <= 0.0 {
                return Err(ChartError::InvalidIndicatorConfig("Bollinger Bands standard deviation multiplier must be positive".to_string()));
            }
            
            Ok(Box::new(bollinger_bands::BollingerBands::new(
                config.period as usize,
                std_dev_multiplier,
            )))
        }
        
        IndicatorType::ATR => {
            if config.period <= 0 {
                return Err(ChartError::InvalidIndicatorConfig("ATR period must be positive".to_string()));
            }
            
            let use_wilders_smoothing = config.use_wilders_smoothing.unwrap_or(true);
            Ok(Box::new(atr::ATR::new(config.period as usize, use_wilders_smoothing)))
        }
    }
}

/// Manages multiple indicators
pub struct IndicatorManager {
    indicators: HashMap<usize, Box<dyn Indicator>>,
    next_id: usize,
}

impl IndicatorManager {
    pub fn new() -> Self {
        Self {
            indicators: HashMap::new(),
            next_id: 0,
        }
    }

    /// Add a new indicator
    pub fn add_indicator(&mut self, config: IndicatorConfig) -> ChartResult<usize> {
        let indicator = create_indicator(config)?;
        let id = self.next_id;
        self.indicators.insert(id, indicator);
        self.next_id += 1;
        Ok(id)
    }

    /// Remove an indicator
    pub fn remove_indicator(&mut self, id: usize) -> bool {
        self.indicators.remove(&id).is_some()
    }

    /// Update all indicators with a new candlestick
    pub fn update_with_candlestick(&mut self, candlestick: Candlestick) {
        for indicator in self.indicators.values_mut() {
            indicator.update(candlestick);
        }
    }

    /// Clear all indicators
    pub fn clear(&mut self) {
        self.indicators.clear();
        self.next_id = 0;
    }

    /// Get indicator count
    pub fn get_indicator_count(&self) -> usize {
        self.indicators.len()
    }

    /// Get indicator name by index (ordered by ID)
    pub fn get_indicator_name(&self, index: usize) -> Option<String> {
        let mut ids: Vec<_> = self.indicators.keys().collect();
        ids.sort();
        
        if let Some(&id) = ids.get(index) {
            self.indicators.get(id).map(|indicator| indicator.name().to_string())
        } else {
            None
        }
    }

    /// Get indicator data count by index
    pub fn get_indicator_data_count(&self, index: usize) -> usize {
        let mut ids: Vec<_> = self.indicators.keys().collect();
        ids.sort();
        
        if let Some(&id) = ids.get(index) {
            self.indicators.get(id).map(|indicator| indicator.data_count()).unwrap_or(0)
        } else {
            0
        }
    }

    /// Get indicator data point by index
    pub fn get_indicator_data_point(&self, indicator_index: usize, data_index: usize) -> Option<IndicatorDataPoint> {
        let mut ids: Vec<_> = self.indicators.keys().collect();
        ids.sort();
        
        if let Some(&id) = ids.get(indicator_index) {
            self.indicators.get(id)?.get_data_point(data_index)
        } else {
            None
        }
    }
}

impl Default for IndicatorManager {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::IndicatorConfig;

    #[test]
    fn test_indicator_manager() {
        let mut manager = IndicatorManager::new();
        
        // Add SMA indicator
        let sma_config = IndicatorConfig::sma(10);
        let sma_id = manager.add_indicator(sma_config).unwrap();
        
        // Add EMA indicator
        let ema_config = IndicatorConfig::ema(20);
        let ema_id = manager.add_indicator(ema_config).unwrap();
        
        assert_eq!(manager.get_indicator_count(), 2);
        assert_ne!(sma_id, ema_id);
        
        // Remove SMA
        assert!(manager.remove_indicator(sma_id));
        assert_eq!(manager.get_indicator_count(), 1);
        
        // Try to remove non-existent indicator
        assert!(!manager.remove_indicator(999));
    }

    #[test]
    fn test_create_indicators() {
        // Test SMA creation
        let sma_config = IndicatorConfig::sma(10);
        let sma = create_indicator(sma_config).unwrap();
        assert_eq!(sma.name(), "SMA(10)");
        
        // Test invalid SMA period
        let invalid_sma = IndicatorConfig::sma(-1);
        assert!(create_indicator(invalid_sma).is_err());
        
        // Test MACD creation
        let macd_config = IndicatorConfig::macd(12, 26, 9);
        let macd = create_indicator(macd_config).unwrap();
        assert_eq!(macd.name(), "MACD(12,26,9)");
        
        // Test invalid MACD (fast >= slow)
        let invalid_macd = IndicatorConfig::macd(26, 12, 9);
        assert!(create_indicator(invalid_macd).is_err());
    }
}
