//! FFI interface for the Rust chart engine
//! This module provides C-compatible functions that match the existing C++ API

use crate::chart_engine::ChartEngine;
use crate::candlestick::Candlestick;
use crate::types::IndicatorConfig;
use std::ffi::CString;
use std::os::raw::{c_char, c_double, c_int, c_long};
use std::ptr;
use std::collections::HashMap;
use std::sync::OnceLock;
use std::sync::atomic::{AtomicUsize, Ordering};
use parking_lot::Mutex;

// Global storage for chart engine instances
static CHART_ENGINES: OnceLock<Mutex<HashMap<usize, ChartEngine>>> = OnceLock::new();
static NEXT_ENGINE_ID: AtomicUsize = AtomicUsize::new(1);

/// Initialize the global storage (call once at startup)
fn ensure_global_storage() {
    CHART_ENGINES.get_or_init(|| Mutex::new(HashMap::new()));
}

/// Get a mutable reference to a chart engine by ID
fn with_engine<F, R>(engine_id: usize, f: F) -> Option<R>
where
    F: FnOnce(&mut ChartEngine) -> R,
{
    let engines = CHART_ENGINES.get()?;
    let mut engines_guard = engines.lock();
    if let Some(engine) = engines_guard.get_mut(&engine_id) {
        Some(f(engine))
    } else {
        None
    }
}

/// Create a new chart engine instance
/// Returns: Engine ID (> 0) on success, 0 on failure
#[no_mangle]
pub extern "C" fn create_chart_engine() -> usize {
    ensure_global_storage();

    let engine = ChartEngine::new();
    let engine_id = NEXT_ENGINE_ID.fetch_add(1, Ordering::SeqCst);

    if let Some(engines) = CHART_ENGINES.get() {
        let mut engines_guard = engines.lock();
        engines_guard.insert(engine_id, engine);
        engine_id
    } else {
        0
    }
}

/// Destroy a chart engine instance
#[no_mangle]
pub extern "C" fn destroy_chart_engine(engine_id: usize) {
    if let Some(engines) = CHART_ENGINES.get() {
        let mut engines_guard = engines.lock();
        engines_guard.remove(&engine_id);
    }
}

/// Feed a price tick to the chart engine
#[no_mangle]
pub extern "C" fn chart_engine_feed_tick(engine_id: usize, price: c_double, timestamp: c_long) -> c_int {
    match with_engine(engine_id, |engine| {
        engine.feed_tick(price, timestamp as i64)
    }) {
        Some(Ok(())) => 1, // Success
        Some(Err(_)) => -1, // Error
        None => 0, // Engine not found
    }
}

/// Get the number of candlesticks
#[no_mangle]
pub extern "C" fn chart_engine_get_candlestick_count(engine_id: usize) -> c_int {
    with_engine(engine_id, |engine| {
        engine.get_candlestick_count() as c_int
    }).unwrap_or(0)
}

/// Get a candlestick by index
/// Returns: 1 on success, 0 on failure
#[no_mangle]
pub extern "C" fn chart_engine_get_candlestick(
    engine_id: usize,
    index: c_int,
    output: *mut Candlestick,
) -> c_int {
    if output.is_null() {
        return 0;
    }
    
    match with_engine(engine_id, |engine| {
        engine.get_candlestick(index as usize)
    }) {
        Some(Ok(candlestick)) => {
            unsafe {
                *output = candlestick;
            }
            1
        }
        _ => 0,
    }
}

/// Set the time interval for candlesticks
#[no_mangle]
pub extern "C" fn chart_engine_set_time_interval(engine_id: usize, seconds: c_int) -> c_int {
    match with_engine(engine_id, |engine| {
        engine.set_time_interval(seconds)
    }) {
        Some(Ok(())) => 1,
        Some(Err(_)) => -1,
        None => 0,
    }
}

/// Get the current time interval
#[no_mangle]
pub extern "C" fn chart_engine_get_time_interval(engine_id: usize) -> c_int {
    with_engine(engine_id, |engine| {
        engine.get_time_interval()
    }).unwrap_or(0)
}

/// Set high price for scaling
#[no_mangle]
pub extern "C" fn chart_engine_set_high_price(engine_id: usize, high: c_double) {
    with_engine(engine_id, |engine| {
        engine.set_high_price(high);
    });
}

/// Set start time
#[no_mangle]
pub extern "C" fn chart_engine_set_start_time(engine_id: usize, timestamp: c_long) {
    with_engine(engine_id, |engine| {
        engine.set_start_time(timestamp as i64);
    });
}

/// Set zoom scales
#[no_mangle]
pub extern "C" fn chart_engine_set_zoom_scales(
    engine_id: usize,
    horizontal_zoom: c_double,
    vertical_zoom: c_double,
) {
    with_engine(engine_id, |engine| {
        engine.set_zoom_scales(horizontal_zoom, vertical_zoom);
    });
}

/// Set pan offsets
#[no_mangle]
pub extern "C" fn chart_engine_set_pan_offsets(
    engine_id: usize,
    horizontal_offset: c_double,
    vertical_offset: c_double,
) {
    with_engine(engine_id, |engine| {
        engine.set_pan_offsets(horizontal_offset, vertical_offset);
    });
}

/// Clear all data
#[no_mangle]
pub extern "C" fn chart_engine_clear_data(engine_id: usize) {
    with_engine(engine_id, |engine| {
        engine.clear_data();
    });
}

/// Add an SMA indicator
#[no_mangle]
pub extern "C" fn chart_engine_add_sma(engine_id: usize, period: c_int) -> c_int {
    if period <= 0 {
        return -1;
    }
    
    let config = IndicatorConfig::sma(period);
    match with_engine(engine_id, |engine| {
        engine.add_indicator(config)
    }) {
        Some(Ok(indicator_id)) => indicator_id as c_int,
        _ => -1,
    }
}

/// Add an EMA indicator
#[no_mangle]
pub extern "C" fn chart_engine_add_ema(engine_id: usize, period: c_int) -> c_int {
    if period <= 0 {
        return -1;
    }
    
    let config = IndicatorConfig::ema(period);
    match with_engine(engine_id, |engine| {
        engine.add_indicator(config)
    }) {
        Some(Ok(indicator_id)) => indicator_id as c_int,
        _ => -1,
    }
}

/// Add an RSI indicator
#[no_mangle]
pub extern "C" fn chart_engine_add_rsi(engine_id: usize, period: c_int) -> c_int {
    if period <= 0 {
        return -1;
    }
    
    let config = IndicatorConfig::rsi(period);
    match with_engine(engine_id, |engine| {
        engine.add_indicator(config)
    }) {
        Some(Ok(indicator_id)) => indicator_id as c_int,
        _ => -1,
    }
}

/// Add a MACD indicator
#[no_mangle]
pub extern "C" fn chart_engine_add_macd(
    engine_id: usize,
    fast_period: c_int,
    slow_period: c_int,
    signal_period: c_int,
) -> c_int {
    if fast_period <= 0 || slow_period <= 0 || signal_period <= 0 || fast_period >= slow_period {
        return -1;
    }
    
    let config = IndicatorConfig::macd(fast_period, slow_period, signal_period);
    match with_engine(engine_id, |engine| {
        engine.add_indicator(config)
    }) {
        Some(Ok(indicator_id)) => indicator_id as c_int,
        _ => -1,
    }
}

/// Add a Bollinger Bands indicator
#[no_mangle]
pub extern "C" fn chart_engine_add_bollinger_bands(
    engine_id: usize,
    period: c_int,
    std_dev_multiplier: c_double,
) -> c_int {
    if period <= 0 || std_dev_multiplier <= 0.0 {
        return -1;
    }
    
    let config = IndicatorConfig::bollinger_bands(period, std_dev_multiplier);
    match with_engine(engine_id, |engine| {
        engine.add_indicator(config)
    }) {
        Some(Ok(indicator_id)) => indicator_id as c_int,
        _ => -1,
    }
}

/// Add an ATR indicator
#[no_mangle]
pub extern "C" fn chart_engine_add_atr(engine_id: usize, period: c_int, use_wilders_smoothing: c_int) -> c_int {
    if period <= 0 {
        return -1;
    }
    
    let config = IndicatorConfig::atr(period, use_wilders_smoothing != 0);
    match with_engine(engine_id, |engine| {
        engine.add_indicator(config)
    }) {
        Some(Ok(indicator_id)) => indicator_id as c_int,
        _ => -1,
    }
}

/// Remove an indicator
#[no_mangle]
pub extern "C" fn chart_engine_remove_indicator(engine_id: usize, indicator_id: c_int) -> c_int {
    match with_engine(engine_id, |engine| {
        engine.remove_indicator(indicator_id as usize)
    }) {
        Some(Ok(removed)) => if removed { 1 } else { 0 },
        _ => 0,
    }
}

/// Clear all indicators
#[no_mangle]
pub extern "C" fn chart_engine_clear_all_indicators(engine_id: usize) {
    with_engine(engine_id, |engine| {
        engine.clear_all_indicators();
    });
}

/// Get the number of indicators
#[no_mangle]
pub extern "C" fn chart_engine_get_indicator_count(engine_id: usize) -> c_int {
    with_engine(engine_id, |engine| {
        engine.get_indicator_count() as c_int
    }).unwrap_or(0)
}

/// Get indicator name by index
/// Returns: Pointer to C string (caller must free), or null on failure
#[no_mangle]
pub extern "C" fn chart_engine_get_indicator_name(engine_id: usize, index: c_int) -> *mut c_char {
    match with_engine(engine_id, |engine| {
        engine.get_indicator_name(index as usize)
    }) {
        Some(Some(name)) => {
            match CString::new(name) {
                Ok(c_string) => c_string.into_raw(),
                Err(_) => ptr::null_mut(),
            }
        }
        _ => ptr::null_mut(),
    }
}

/// Free a string returned by chart_engine_get_indicator_name
#[no_mangle]
pub extern "C" fn chart_engine_free_string(s: *mut c_char) {
    if !s.is_null() {
        unsafe {
            let _ = CString::from_raw(s);
        }
    }
}

/// Get indicator data count
#[no_mangle]
pub extern "C" fn chart_engine_get_indicator_data_count(engine_id: usize, indicator_index: c_int) -> c_int {
    with_engine(engine_id, |engine| {
        engine.get_indicator_data_count(indicator_index as usize) as c_int
    }).unwrap_or(0)
}

/// Get indicator data point value
#[no_mangle]
pub extern "C" fn chart_engine_get_indicator_data_point(
    engine_id: usize,
    indicator_index: c_int,
    data_index: c_int,
) -> c_double {
    match with_engine(engine_id, |engine| {
        engine.get_indicator_data_point(indicator_index as usize, data_index as usize)
    }) {
        Some(Some(data_point)) if data_point.is_valid => data_point.value,
        _ => f64::NAN,
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_ffi_engine_lifecycle() {
        let engine_id = create_chart_engine();
        assert!(engine_id > 0);
        
        // Test basic operations
        assert_eq!(chart_engine_get_candlestick_count(engine_id), 0);
        assert_eq!(chart_engine_get_time_interval(engine_id), 300);
        
        // Feed a tick
        assert_eq!(chart_engine_feed_tick(engine_id, 100.0, 1000), 1);
        
        // Clean up
        destroy_chart_engine(engine_id);
    }

    #[test]
    fn test_ffi_indicators() {
        let engine_id = create_chart_engine();
        assert!(engine_id > 0);
        
        // Add indicators
        let sma_id = chart_engine_add_sma(engine_id, 10);
        assert!(sma_id >= 0);
        
        let ema_id = chart_engine_add_ema(engine_id, 20);
        assert!(ema_id >= 0);
        
        assert_eq!(chart_engine_get_indicator_count(engine_id), 2);
        
        // Clean up
        destroy_chart_engine(engine_id);
    }
}

/// Get the minimum price from all candlesticks
#[no_mangle]
pub extern "C" fn chart_engine_get_min_price(engine_id: usize) -> f64 {
    with_engine(engine_id, |engine| {
        engine.get_all_candlesticks()
            .iter()
            .map(|c| c.low)
            .fold(f64::INFINITY, f64::min)
    }).unwrap_or(0.0)
}

/// Get the maximum price from all candlesticks
#[no_mangle]
pub extern "C" fn chart_engine_get_max_price(engine_id: usize) -> f64 {
    with_engine(engine_id, |engine| {
        engine.get_all_candlesticks()
            .iter()
            .map(|c| c.high)
            .fold(f64::NEG_INFINITY, f64::max)
    }).unwrap_or(0.0)
}
