[package]
name = "chart_engine_rust"
version = "0.1.0"
edition = "2021"

[lib]
name = "chart_engine_rust"
crate-type = ["cdylib", "staticlib"]

[dependencies]
# Core dependencies
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Math and statistics
nalgebra = "0.31"
statrs = "0.16"

# Time handling
chrono = { version = "0.4", features = ["serde"] }

# FFI support
libc = "0.2"

# Concurrency
parking_lot = "0.12"
crossbeam = "0.8"

# Optional: for better error handling
anyhow = "1.0"
thiserror = "1.0"

# Optional: for logging (can be useful for debugging)
log = { version = "0.4", optional = true }

[dev-dependencies]
criterion = "0.5"

[profile.release]
# Optimize for performance
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"

[profile.dev]
# Faster compilation during development
opt-level = 0
debug = true

# Android-specific configuration
[target.'cfg(target_os = "android")']
[target.'cfg(target_os = "android")'.dependencies]
jni = "0.21"
android_logger = "0.13"

# iOS-specific configuration  
[target.'cfg(target_os = "ios")']
[target.'cfg(target_os = "ios")'.dependencies]
objc = "0.2"

[features]
default = ["indicators"]
indicators = []
debug_logging = ["log"]
