#ifndef CHART_ENGINE_RUST_H
#define CHART_ENGINE_RUST_H

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// Candlestick structure (must match Rust definition)
typedef struct {
    double open;
    double high;
    double low;
    double close;
    int64_t start_time;
    int64_t end_time;
} Candlestick;

// Chart engine functions
uintptr_t create_chart_engine(void);
void destroy_chart_engine(uintptr_t engine_id);
int chart_engine_feed_tick(uintptr_t engine_id, double price, int64_t timestamp);
int chart_engine_get_candlestick_count(uintptr_t engine_id);
int chart_engine_get_candlestick(uintptr_t engine_id, int index, Candlestick* output);
int chart_engine_set_time_interval(uintptr_t engine_id, int seconds);
int chart_engine_get_time_interval(uintptr_t engine_id);
void chart_engine_set_high_price(uintptr_t engine_id, double high);
void chart_engine_set_start_time(uintptr_t engine_id, int64_t timestamp);
void chart_engine_set_zoom_scales(uintptr_t engine_id, double horizontal_zoom, double vertical_zoom);
void chart_engine_set_pan_offsets(uintptr_t engine_id, double horizontal_offset, double vertical_offset);
void chart_engine_clear_data(uintptr_t engine_id);
double chart_engine_get_min_price(uintptr_t engine_id);
double chart_engine_get_max_price(uintptr_t engine_id);

// Indicator functions
int chart_engine_add_sma(uintptr_t engine_id, int period);
int chart_engine_add_ema(uintptr_t engine_id, int period);
int chart_engine_add_rsi(uintptr_t engine_id, int period);
int chart_engine_add_macd(uintptr_t engine_id, int fast_period, int slow_period, int signal_period);
int chart_engine_add_bollinger_bands(uintptr_t engine_id, int period, double std_dev_multiplier);
int chart_engine_add_atr(uintptr_t engine_id, int period, int use_wilders_smoothing);
int chart_engine_remove_indicator(uintptr_t engine_id, int indicator_id);
void chart_engine_clear_all_indicators(uintptr_t engine_id);
int chart_engine_get_indicator_count(uintptr_t engine_id);
char* chart_engine_get_indicator_name(uintptr_t engine_id, int index);
void chart_engine_free_string(char* s);
int chart_engine_get_indicator_data_count(uintptr_t engine_id, int indicator_index);
double chart_engine_get_indicator_data_point(uintptr_t engine_id, int indicator_index, int data_index);

#ifdef __cplusplus
}
#endif

#endif // CHART_ENGINE_RUST_H
