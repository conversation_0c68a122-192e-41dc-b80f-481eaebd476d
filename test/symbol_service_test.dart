import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/foundation.dart';
import 'package:abra/presentation/symbol/symbol_service.dart';
import 'package:abra/presentation/symbol/models/symbol_models.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('SymbolService Integration Tests', () {
    late SymbolService symbolService;

    setUp(() {
      symbolService = SymbolService();
    });

    group('Model Tests', () {
      test('SymbolMeta.fromJson should parse correctly', () {
        // Arrange
        final json = {
          'description': 'Apple Inc.',
          'bp': '150.25',
          'ap': '150.50',
          'p': '150.30',
          'v': '1000000',
          't': 1640995200000, // 2022-01-01 00:00:00 UTC
          'broker': 'test-broker',
        };

        // Act
        final symbolMeta = SymbolMeta.fromJson('AAPL', json);

        // Assert
        expect(symbolMeta.symbol, equals('AAPL'));
        expect(symbolMeta.description, equals('Apple Inc.'));
        expect(symbolMeta.bid, equals(150.25));
        expect(symbolMeta.ask, equals(150.50));
        expect(symbolMeta.price, equals(150.30));
        expect(symbolMeta.volume, equals(1000000));
        expect(symbolMeta.broker, equals('test-broker'));
        expect(symbolMeta.timestamp, isA<DateTime>());
      });

      test('SymbolMeta.fromServerResponse should parse correctly', () {
        // Arrange
        final json = {
          'symbol': 'GOOGL',
          'description': 'Alphabet Inc.',
          'currentPrice': 2800.50,
          'change': -25.75,
          'volume': 500000,
          'timestamp': '2024-01-01T12:00:00Z',
          'broker': 'market-service',
        };

        // Act
        final symbolMeta = SymbolMeta.fromServerResponse(json);

        // Assert
        expect(symbolMeta.symbol, equals('GOOGL'));
        expect(symbolMeta.description, equals('Alphabet Inc.'));
        expect(symbolMeta.price, equals(2800.50));
        expect(symbolMeta.change, equals(-25.75));
        expect(symbolMeta.volume, equals(500000));
        expect(symbolMeta.broker, equals('market-service'));
        expect(symbolMeta.timestamp, isA<DateTime>());
      });

      test('BrokerInfo.fromJson should parse correctly', () {
        // Arrange
        final json = {
          'id': 'broker-1',
          'name': 'Test Broker',
          'description': 'A test broker',
          'isActive': true,
        };

        // Act
        final brokerInfo = BrokerInfo.fromJson(json);

        // Assert
        expect(brokerInfo.id, equals('broker-1'));
        expect(brokerInfo.name, equals('Test Broker'));
        expect(brokerInfo.description, equals('A test broker'));
        expect(brokerInfo.isActive, isTrue);
      });
    });

    group('SymbolPriceData Tests', () {
      test('SymbolPriceData.fromJson should parse correctly', () {
        // Arrange
        final json = {
          'symbol': 'AAPL',
          'description': 'Apple Inc.',
          'currentPrice': 150.25,
          'change': 2.50,
          'changePercent': 1.69,
          'volume': 1000000,
          'high': 152.00,
          'low': 148.50,
          'open': 149.00,
          'timestamp': '2024-01-01T12:00:00Z',
        };

        // Act
        final priceData = SymbolPriceData.fromJson(json);

        // Assert
        expect(priceData.symbol, equals('AAPL'));
        expect(priceData.description, equals('Apple Inc.'));
        expect(priceData.currentPrice, equals(150.25));
        expect(priceData.change, equals(2.50));
        expect(priceData.changePercent, equals(1.69));
        expect(priceData.volume, equals(1000000));
        expect(priceData.high, equals(152.00));
        expect(priceData.low, equals(148.50));
        expect(priceData.open, equals(149.00));
        expect(priceData.timestamp, isA<DateTime>());
      });

      test('SymbolPriceData.toJson should serialize correctly', () {
        // Arrange
        final priceData = SymbolPriceData(
          symbol: 'GOOGL',
          description: 'Alphabet Inc.',
          currentPrice: 2800.50,
          change: -25.75,
          changePercent: -0.91,
          volume: 500000,
          timestamp: DateTime.parse('2024-01-01T12:00:00Z'),
        );

        // Act
        final json = priceData.toJson();

        // Assert
        expect(json['symbol'], equals('GOOGL'));
        expect(json['description'], equals('Alphabet Inc.'));
        expect(json['currentPrice'], equals(2800.50));
        expect(json['change'], equals(-25.75));
        expect(json['changePercent'], equals(-0.91));
        expect(json['volume'], equals(500000));
        expect(json['timestamp'], equals('2024-01-01T12:00:00.000Z'));
      });
    });

    group('BulkPriceResponse Tests', () {
      test('BulkPriceResponse.fromJson should parse correctly', () {
        // Arrange
        final json = {
          'prices': [
            {
              'symbol': 'AAPL',
              'currentPrice': 150.25,
              'change': 2.50,
              'timestamp': '2024-01-01T12:00:00Z',
            },
            {
              'symbol': 'GOOGL',
              'currentPrice': 2800.50,
              'change': -10.25,
              'timestamp': '2024-01-01T12:00:00Z',
            },
          ],
          'successCount': 2,
          'failureCount': 1,
          'timestamp': '2024-01-01T12:00:00Z',
        };

        // Act
        final bulkResponse = BulkPriceResponse.fromJson(json);

        // Assert
        expect(bulkResponse.prices, hasLength(2));
        expect(bulkResponse.successCount, equals(2));
        expect(bulkResponse.failureCount, equals(1));
        expect(bulkResponse.timestamp, isA<DateTime>());
        expect(bulkResponse.prices[0].symbol, equals('AAPL'));
        expect(bulkResponse.prices[1].symbol, equals('GOOGL'));
      });

      test('should handle empty symbols list correctly', () async {
        // Act - Empty list should not require authentication
        final result = await symbolService.getBulkPrices([]);

        // Assert
        expect(result.prices, isEmpty);
        expect(result.successCount, equals(0));
        expect(result.failureCount, equals(0));
      });

      test('should throw exception for too many symbols', () async {
        // Arrange
        final tooManySymbols = List.generate(101, (i) => 'SYMBOL$i');

        // Act & Assert
        expect(
          () => symbolService.getBulkPrices(tooManySymbols),
          throwsException,
        );
      });
    });

    group('HistoricalPriceData Tests', () {
      test('HistoricalPriceData.fromJson should parse correctly', () {
        // Arrange
        final json = {
          'symbol': 'AAPL',
          'timestamp': '2024-01-01T00:00:00Z',
          'open': 148.50,
          'high': 152.00,
          'low': 147.25,
          'close': 150.25,
          'volume': 1000000,
        };

        // Act
        final historicalData = HistoricalPriceData.fromJson(json);

        // Assert
        expect(historicalData.symbol, equals('AAPL'));
        expect(historicalData.timestamp, isA<DateTime>());
        expect(historicalData.open, equals(148.50));
        expect(historicalData.high, equals(152.00));
        expect(historicalData.low, equals(147.25));
        expect(historicalData.close, equals(150.25));
        expect(historicalData.volume, equals(1000000));
      });

      test('HistoricalPriceData.toJson should serialize correctly', () {
        // Arrange
        final historicalData = HistoricalPriceData(
          symbol: 'GOOGL',
          timestamp: DateTime.parse('2024-01-01T00:00:00Z'),
          open: 2750.00,
          high: 2850.00,
          low: 2700.00,
          close: 2800.50,
          volume: 500000,
        );

        // Act
        final json = historicalData.toJson();

        // Assert
        expect(json['symbol'], equals('GOOGL'));
        expect(json['timestamp'], equals('2024-01-01T00:00:00.000Z'));
        expect(json['open'], equals(2750.00));
        expect(json['high'], equals(2850.00));
        expect(json['low'], equals(2700.00));
        expect(json['close'], equals(2800.50));
        expect(json['volume'], equals(500000));
      });
    });

    group('SymbolSearchResult Tests', () {
      test('SymbolSearchResult.fromJson should parse correctly', () {
        // Arrange
        final json = {
          'symbol': 'AAPL',
          'name': 'Apple Inc.',
          'exchange': 'NASDAQ',
          'type': 'stock',
          'currency': 'USD',
        };

        // Act
        final searchResult = SymbolSearchResult.fromJson(json);

        // Assert
        expect(searchResult.symbol, equals('AAPL'));
        expect(searchResult.name, equals('Apple Inc.'));
        expect(searchResult.exchange, equals('NASDAQ'));
        expect(searchResult.type, equals('stock'));
        expect(searchResult.currency, equals('USD'));
      });

      test('SymbolSearchResult.toJson should serialize correctly', () {
        // Arrange
        final searchResult = SymbolSearchResult(
          symbol: 'GOOGL',
          name: 'Alphabet Inc.',
          exchange: 'NASDAQ',
          type: 'stock',
          currency: 'USD',
        );

        // Act
        final json = searchResult.toJson();

        // Assert
        expect(json['symbol'], equals('GOOGL'));
        expect(json['name'], equals('Alphabet Inc.'));
        expect(json['exchange'], equals('NASDAQ'));
        expect(json['type'], equals('stock'));
        expect(json['currency'], equals('USD'));
      });
    });

    group('Service Validation Tests', () {
      test('should validate symbol limits correctly', () {
        // Test bulk price limits
        final tooManySymbolsForBulk = List.generate(101, (i) => 'SYMBOL$i');
        expect(
          () => symbolService.getBulkPrices(tooManySymbolsForBulk),
          throwsException,
        );
      });

      test('should handle empty symbol lists correctly', () async {
        // Test empty bulk prices
        final bulkResult = await symbolService.getBulkPrices([]);
        expect(bulkResult.prices, isEmpty);
        expect(bulkResult.successCount, equals(0));
        expect(bulkResult.failureCount, equals(0));
      });

      test('should have singleton instance', () {
        // Act
        final instance1 = SymbolService();
        final instance2 = SymbolService();

        // Assert
        expect(identical(instance1, instance2), isTrue);
      });

      test('should validate authentication requirements', () {
        // Test that methods requiring authentication throw appropriate errors
        expect(
          () => symbolService.fetchLivePrice('AAPL'),
          throwsA(isA<Exception>()),
        );

        expect(
          () => symbolService.getHistoricalPrices(
            'AAPL',
            from: DateTime.now().subtract(const Duration(days: 7)),
            to: DateTime.now(),
          ),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('Integration Scenarios', () {
      test('should demonstrate typical usage flow', () async {
        // Test the typical flow without requiring authentication

        // 1. Test empty list handling (no auth required)
        final emptyBulkResult = await symbolService.getBulkPrices([]);
        expect(emptyBulkResult.prices, isEmpty);

        // 2. Test validation limits
        final tooManySymbols = List.generate(101, (i) => 'SYMBOL$i');
        expect(
          () => symbolService.getBulkPrices(tooManySymbols),
          throwsException,
        );

        // 3. Test singleton behavior
        final service1 = SymbolService();
        final service2 = SymbolService();
        expect(identical(service1, service2), isTrue);

        // 4. Test new methods
        try {
          final brokers = await symbolService.getSymbolBrokers();
          expect(brokers, isA<List<String>>());
        } catch (e) {
          // Expected in test environment without authentication
          expect(e.toString(), contains('User not authenticated'));
        }

        try {
          final activeSymbols = await symbolService.getActiveSymbols(limit: 5);
          expect(activeSymbols, isA<List<SymbolMeta>>());
          expect(activeSymbols.length, lessThanOrEqualTo(5));
        } catch (e) {
          // Expected in test environment without authentication
          expect(e.toString(), contains('User not authenticated'));
        }
      });

      test('should handle broker symbol fetching gracefully', () async {
        // Test that getPopularSymbols works even if broker endpoint fails
        // This will likely use the fallback mechanism since we're not authenticated

        try {
          final popularSymbols = await symbolService.getPopularSymbols();

          // Should return some symbols (either from broker or fallback)
          expect(popularSymbols, isA<List<SymbolMeta>>());

          // Should return at most 10 symbols as per the new implementation
          expect(popularSymbols.length, lessThanOrEqualTo(10));

          // Each symbol should have required fields
          for (final symbol in popularSymbols) {
            expect(symbol.symbol, isNotEmpty);
            expect(symbol.broker, isNotNull);
          }
        } catch (e) {
          // If it fails due to authentication, that's expected in test environment
          expect(e.toString(), contains('User not authenticated'));
        }
      });

      test('should handle model serialization correctly', () {
        // Test complete serialization/deserialization cycle
        final originalSymbol = SymbolMeta(
          symbol: 'TEST',
          description: 'Test Symbol',
          price: 100.50,
          change: 2.25,
          volume: 1000000,
          timestamp: DateTime.parse('2024-01-01T12:00:00Z'),
          broker: 'test-broker',
        );

        // Test toJson
        final json = originalSymbol.toJson(price: originalSymbol.price!);
        expect(json['symbol'], equals('TEST'));
        expect(json['price'], equals(100.50));

        // Test copyWith
        final updatedSymbol = originalSymbol.copyWith(price: 105.75);
        expect(updatedSymbol.price, equals(105.75));
        expect(updatedSymbol.symbol, equals('TEST')); // Other fields unchanged
      });

      test('should demonstrate improved popular symbols logic', () async {
        // Test that the new getPopularSymbols method follows the expected flow:
        // 1. Try to fetch from broker
        // 2. Fall back to hardcoded list if broker fails
        // 3. Sort by volume to get most active symbols
        // 4. Return top 10

        try {
          final popularSymbols = await symbolService.getPopularSymbols();

          // Should handle the flow gracefully
          expect(popularSymbols, isA<List<SymbolMeta>>());

          // The new implementation should return at most 10 symbols
          expect(popularSymbols.length, lessThanOrEqualTo(10));

          // Verify that symbols have the expected structure
          if (popularSymbols.isNotEmpty) {
            final firstSymbol = popularSymbols.first;
            expect(firstSymbol.symbol, isNotEmpty);
            expect(firstSymbol.broker, isNotNull);

            // Should indicate if it's using fallback
            final isUsingFallback = firstSymbol.broker!.contains('Fallback');
            debugPrint('Using fallback symbols: $isUsingFallback');
          }
        } catch (e) {
          // Expected in test environment without authentication
          expect(e.toString(), contains('User not authenticated'));
        }
      });

      test('should test new API endpoints', () async {
        // Test the new API endpoints that were added

        try {
          // Test getSymbolsByMarketType
          final stockSymbols = await symbolService.getSymbolsByMarketType(
            'stocks',
          );
          expect(stockSymbols, isA<List<SymbolMeta>>());

          final forexSymbols = await symbolService.getSymbolsByMarketType(
            'forex',
          );
          expect(forexSymbols, isA<List<SymbolMeta>>());

          final cryptoSymbols = await symbolService.getSymbolsByMarketType(
            'crypto',
          );
          expect(cryptoSymbols, isA<List<SymbolMeta>>());
        } catch (e) {
          // Expected in test environment without authentication
          expect(e.toString(), contains('User not authenticated'));
        }

        try {
          // Test getAllSymbols
          final allSymbols = await symbolService.getAllSymbols();
          expect(allSymbols, isA<List<SymbolMeta>>());
        } catch (e) {
          // Expected in test environment without authentication
          expect(e.toString(), contains('User not authenticated'));
        }

        try {
          // Test bulkSymbolSearch
          final searchResults = await symbolService.bulkSymbolSearch([
            'AAPL',
            'GOOGL',
          ]);
          expect(searchResults, isA<List<SymbolMeta>>());
        } catch (e) {
          // Expected in test environment without authentication
          expect(e.toString(), contains('User not authenticated'));
        }

        try {
          // Test bulkGetActiveSymbols
          final activeSymbols = await symbolService.bulkGetActiveSymbols([
            'finnhub',
            'polygon',
          ]);
          expect(activeSymbols, isA<List<SymbolMeta>>());
        } catch (e) {
          // Expected in test environment without authentication
          expect(e.toString(), contains('User not authenticated'));
        }
      });

      test('should test broker-related methods', () async {
        try {
          // Test getAvailableBrokers
          final brokers = await symbolService.getAvailableBrokers();
          expect(brokers, isA<List<BrokerInfo>>());

          for (final broker in brokers) {
            expect(broker.id, isNotEmpty);
            expect(broker.name, isNotEmpty);
            expect(broker.isActive, isA<bool>());
          }
        } catch (e) {
          // Expected in test environment without authentication
          expect(e.toString(), contains('User not authenticated'));
        }

        try {
          // Test getSymbolBrokers
          final brokerNames = await symbolService.getSymbolBrokers();
          expect(brokerNames, isA<List<String>>());

          // Should return broker names like ["finnhub","polygon"]
          for (final brokerName in brokerNames) {
            expect(brokerName, isNotEmpty);
          }
        } catch (e) {
          // Expected in test environment without authentication
          expect(e.toString(), contains('User not authenticated'));
        }
      });

      test('should test historical price functionality', () async {
        try {
          final from = DateTime.now().subtract(const Duration(days: 7));
          final to = DateTime.now();

          final historicalPrices = await symbolService.getHistoricalPrices(
            'AAPL',
            from: from,
            to: to,
            interval: '1d',
          );

          expect(historicalPrices, isA<List<HistoricalPriceData>>());

          for (final priceData in historicalPrices) {
            expect(priceData.symbol, equals('AAPL'));
            expect(priceData.timestamp, isA<DateTime>());
            expect(priceData.open, isA<double>());
            expect(priceData.high, isA<double>());
            expect(priceData.low, isA<double>());
            expect(priceData.close, isA<double>());
            expect(priceData.volume, isA<double>());
          }
        } catch (e) {
          // Expected in test environment without authentication
          expect(e.toString(), contains('User not authenticated'));
        }
      });
    });

    group('Enhanced Search Functionality', () {
      test('should handle empty search query', () async {
        // Act
        final results = await symbolService.searchSymbols('');

        // Assert
        expect(results, isEmpty);
      });

      test('should handle whitespace-only search query', () async {
        // Act
        final results = await symbolService.searchSymbols('   ');

        // Assert
        expect(results, isEmpty);
      });

      test('should demonstrate search flow with fallback', () async {
        // Test the enhanced search functionality:
        // 1. Try broker search endpoint
        // 2. Fall back to popular symbols search if broker fails
        // 3. Return empty list if all fails

        try {
          final searchResults = await symbolService.searchSymbols('AAPL');

          // Should return results (either from broker or fallback)
          expect(searchResults, isA<List<SymbolMeta>>());

          // Results should be relevant to search query
          if (searchResults.isNotEmpty) {
            final hasRelevantResults = searchResults.any(
              (symbol) =>
                  symbol.symbol.toLowerCase().contains('aapl') ||
                  (symbol.description?.toLowerCase().contains('aapl') ?? false),
            );

            // At least some results should be relevant (if any results returned)
            if (searchResults.isNotEmpty) {
              expect(hasRelevantResults, isTrue);
            }
          }
        } catch (e) {
          // Expected in test environment without authentication
          expect(e.toString(), contains('User not authenticated'));
        }
      });

      test('should handle search with price data integration', () async {
        // Test that search results can include price data when available

        try {
          final searchResults = await symbolService.searchSymbols('GOOGL');

          expect(searchResults, isA<List<SymbolMeta>>());

          // Check if any results have price data (when available)
          if (searchResults.isNotEmpty) {
            for (final symbol in searchResults) {
              expect(symbol.symbol, isNotEmpty);
              expect(symbol.description, isNotNull);
              expect(symbol.timestamp, isNotNull);
              // Price data may or may not be available depending on authentication
            }
          }
        } catch (e) {
          // Expected in test environment without authentication
          expect(e.toString(), contains('User not authenticated'));
        }
      });

      test('should demonstrate search result structure', () async {
        // Test that search results have the expected SymbolMeta structure

        try {
          final searchResults = await symbolService.searchSymbols('MSFT');

          expect(searchResults, isA<List<SymbolMeta>>());

          if (searchResults.isNotEmpty) {
            final firstResult = searchResults.first;

            // Required fields
            expect(firstResult.symbol, isNotEmpty);
            expect(firstResult.timestamp, isNotNull);

            // Optional fields that should be present from search
            expect(firstResult.description, isNotNull);
            expect(firstResult.broker, isNotNull);

            // Price fields may be null if not fetched
            // but should be properly typed
            expect(firstResult.price, anyOf(isNull, isA<double>()));
            expect(firstResult.change, anyOf(isNull, isA<double>()));
            expect(firstResult.volume, anyOf(isNull, isA<double>()));
          }
        } catch (e) {
          // Expected in test environment without authentication
          expect(e.toString(), contains('User not authenticated'));
        }
      });
    });
  });
}
