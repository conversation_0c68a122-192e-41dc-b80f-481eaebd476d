/// Integration test to verify the assistant service works with the backend
/// Run this after starting the assistant-service to test the integration
/// 
/// Usage: dart run test/integration_test.dart
library;

import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:abra/hub/assistant/assistant_service.dart';
import 'package:abra/hub/assistant/models/chat_models.dart';
import 'package:abra/core/constants.dart';

Future<void> main() async {
  debugPrint('🚀 Starting Assistant Service Integration Test');
  debugPrint('📍 Testing against: ${AssistantConfig.baseUrl}');
  debugPrint('');

  final service = AssistantService();

  try {
    // Test 1: Basic chat functionality
    debugPrint('📝 Test 1: Basic Chat Functionality');
    await testBasicChat(service);
    debugPrint('✅ Basic chat test passed\n');

    // Test 2: Streaming functionality
    debugPrint('📝 Test 2: Streaming Chat Functionality');
    await testStreamingChat(service);
    debugPrint('✅ Streaming chat test passed\n');

    // Test 3: Conversation history
    debugPrint('📝 Test 3: Conversation History');
    await testConversationHistory(service);
    debugPrint('✅ Conversation history test passed\n');

    // Test 4: Error handling
    debugPrint('📝 Test 4: Error Handling');
    await testErrorHandling(service);
    debugPrint('✅ Error handling test passed\n');

    debugPrint('🎉 All integration tests passed!');
    debugPrint('✅ The Dart client is successfully integrated with the assistant-service');

  } catch (e) {
    debugPrint('❌ Integration test failed: $e');
    debugPrint('');
    debugPrint('💡 Make sure the assistant-service is running on ${AssistantConfig.baseUrl}');
    debugPrint('   You can start it with: dotnet run --project assistant-service');
    exit(1);
  } finally {
    service.dispose();
  }
}

Future<void> testBasicChat(AssistantService service) async {
  final response = await service.chat('What is 2+2?');
  
  if (response.isEmpty) {
    throw Exception('Empty response from chat endpoint');
  }
  
  if (response.startsWith('🚫') || response.startsWith('❌')) {
    throw Exception('Error response: $response');
  }
  
  debugPrint('   📤 Sent: "What is 2+2?"');
  debugPrint('   📥 Received: "${response.substring(0, response.length > 50 ? 50 : response.length)}${response.length > 50 ? '...' : ''}"');
}

Future<void> testStreamingChat(AssistantService service) async {
  final chunks = <String>[];
  
  await for (final chunk in service.chatStream('Tell me a very short joke')) {
    chunks.add(chunk);
    
    if (chunk.startsWith('🚫') || chunk.startsWith('❌')) {
      throw Exception('Error in streaming: $chunk');
    }
    
    // Break after reasonable number of chunks to avoid infinite loops
    if (chunks.length > 100) break;
  }
  
  if (chunks.isEmpty) {
    throw Exception('No chunks received from streaming endpoint');
  }
  
  final fullResponse = chunks.join('');
  debugPrint('   📤 Sent: "Tell me a very short joke"');
  debugPrint('   📥 Streamed ${chunks.length} chunks, total: "${fullResponse.substring(0, fullResponse.length > 50 ? 50 : fullResponse.length)}${fullResponse.length > 50 ? '...' : ''}"');
}

Future<void> testConversationHistory(AssistantService service) async {
  final conversationHistory = [
    ChatMessage(role: 'user', content: 'My name is Alice'),
    ChatMessage(role: 'assistant', content: 'Nice to meet you, Alice!'),
  ];
  
  final response = await service.chat(
    'What is my name?',
    conversationHistory: conversationHistory,
  );
  
  if (response.isEmpty) {
    throw Exception('Empty response with conversation history');
  }
  
  if (response.startsWith('🚫') || response.startsWith('❌')) {
    throw Exception('Error response with history: $response');
  }
  
  debugPrint('   📤 Sent conversation with history (3 messages total)');
  debugPrint('   📥 Received: "${response.substring(0, response.length > 50 ? 50 : response.length)}${response.length > 50 ? '...' : ''}"');
}

Future<void> testErrorHandling(AssistantService service) async {
  // Test empty message validation
  try {
    await service.chat('');
    throw Exception('Should have thrown error for empty message');
  } catch (e) {
    if (e is! ArgumentError) {
      throw Exception('Expected ArgumentError for empty message, got: $e');
    }
    debugPrint('   ✅ Empty message validation works');
  }
  
  // Test message too long validation
  try {
    final longMessage = 'a' * (AssistantConfig.maxMessageLength + 1);
    await service.chat(longMessage);
    throw Exception('Should have thrown error for message too long');
  } catch (e) {
    if (e is! ArgumentError) {
      throw Exception('Expected ArgumentError for long message, got: $e');
    }
    debugPrint('   ✅ Message length validation works');
  }
  
  // Test friendly error messages
  final friendlyError = AssistantConfig.getFriendlyErrorMessage(Exception('test'));
  if (friendlyError.isEmpty) {
    throw Exception('Friendly error message should not be empty');
  }
  debugPrint('   ✅ Friendly error messages work');
}
