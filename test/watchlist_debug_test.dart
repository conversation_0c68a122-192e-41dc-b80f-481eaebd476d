import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:abra/services/auth_client.dart';
import 'package:abra/services/market_service.dart';

/// Debug test to check authentication and watchlist loading issues
void main() {
  group('Watchlist Debug Tests', () {
    test('Check authentication state', () async {
      final authClient = AuthClient();
      await authClient.initialize();

      debugPrint('=== Authentication Debug ===');
      debugPrint(
        'Access Token: ${authClient.accessToken != null ? 'Present (${authClient.accessToken!.length} chars)' : 'NULL'}',
      );
      debugPrint(
        'Current User: ${authClient.currentUser != null ? 'Present (${authClient.currentUser!.email})' : 'NULL'}',
      );
      debugPrint('Is Authenticated: ${authClient.isAuthenticated}');

      if (authClient.accessToken != null) {
        debugPrint(
          'Token preview: ${authClient.accessToken!.substring(0, 20)}...',
        );
      }
    });

    test(
      'Check MarketService authentication and token expiration handling',
      () async {
        final marketService = MarketService();
        await marketService.initialize();

        debugPrint('=== MarketService Debug ===');
        debugPrint('Is Authenticated: ${marketService.isAuthenticated}');
        debugPrint('Service Status: ${marketService.serviceStatus.toJson()}');

        // Try to get watchlists and catch the specific error
        try {
          final watchlists = await marketService.getWatchlists();
          debugPrint(
            'Watchlists loaded successfully: ${watchlists.length} items',
          );
          for (final watchlist in watchlists) {
            debugPrint('  - ${watchlist.name} (ID: ${watchlist.id})');
          }
        } catch (e) {
          debugPrint('Error loading watchlists: $e');
          debugPrint('Error type: ${e.runtimeType}');

          // Check if the token was cleared after the error
          debugPrint(
            'Is Authenticated after error: ${marketService.isAuthenticated}',
          );

          // Check AuthClient state
          final authClient = AuthClient();
          await authClient.initialize();
          debugPrint(
            'AuthClient authenticated after error: ${authClient.isAuthenticated}',
          );
          debugPrint(
            'AuthClient access token after error: ${authClient.accessToken != null ? 'Present' : 'NULL'}',
          );
        }
      },
    );

    test('Test watchlist endpoints directly', () async {
      final authClient = AuthClient();
      await authClient.initialize();

      if (authClient.accessToken == null) {
        debugPrint('No access token available for testing');
        return;
      }

      final token = authClient.accessToken!;
      debugPrint('=== Testing Watchlist Endpoints ===');
      debugPrint('Using token: ${token.substring(0, 20)}...');

      // Test basic watchlist endpoint
      try {
        final marketService = MarketService();
        await marketService.initialize();

        debugPrint('Testing getWatchlists()...');
        final watchlists = await marketService.getWatchlists();
        debugPrint(
          '✅ getWatchlists() success: ${watchlists.length} watchlists',
        );

        if (watchlists.isNotEmpty) {
          final firstWatchlist = watchlists.first;
          debugPrint(
            'First watchlist: ${firstWatchlist.name} (ID: ${firstWatchlist.id})',
          );

          debugPrint('Testing getWatchlistWithPrices(${firstWatchlist.id})...');
          try {
            final watchlistWithPrices = await marketService
                .getWatchlistWithPrices(firstWatchlist.id);
            debugPrint(
              '✅ getWatchlistWithPrices() success: ${watchlistWithPrices.items.length} symbols',
            );

            for (final item in watchlistWithPrices.items) {
              debugPrint('  - ${item.symbol}: \$${item.currentPrice ?? 'N/A'}');
            }
          } catch (e) {
            debugPrint('❌ getWatchlistWithPrices() failed: $e');
          }
        }
      } catch (e) {
        debugPrint('❌ Watchlist test failed: $e');
      }
    });

    test('Check stored tokens in SharedPreferences', () async {
      // This will help us see what's actually stored
      final authClient = AuthClient();
      await authClient.initialize();

      debugPrint('=== Stored Tokens Debug ===');
      // We can't directly access SharedPreferences here, but we can check the loaded values
      debugPrint('Loaded access token: ${authClient.accessToken != null}');
      debugPrint('Loaded user: ${authClient.currentUser != null}');

      if (authClient.currentUser != null) {
        final user = authClient.currentUser!;
        debugPrint('User details:');
        debugPrint('  - ID: ${user.id}');
        debugPrint('  - Email: ${user.email}');
        debugPrint('  - Phone: ${user.phone}');
        debugPrint('  - Created: ${user.createdAt}');
      }
    });
  });
}
