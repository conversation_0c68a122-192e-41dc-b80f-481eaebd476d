import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/services.dart';
import 'dart:ffi';
import 'package:abra/ffi/chart_engine_bindings.dart';
import 'package:abra/ffi/chart_engine_wrapper.dart';

void main() {
  group('Native Chart Integration Tests', () {
    late ChartEngineWrapper chartEngine;

    setUp(() {
      // Initialize Flutter binding for testing
      TestWidgetsFlutterBinding.ensureInitialized();

      // Mock the method channel for testing
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
            const MethodChannel('com.abra/chart_view'),
            (MethodCall methodCall) async {
              switch (methodCall.method) {
                case 'createTexture':
                  return 1; // Mock texture ID
                case 'resizeTexture':
                case 'disposeTexture':
                case 'feedTick':
                case 'setHighPrice':
                case 'setStartTime':
                case 'setZoomScales':
                case 'setPanOffsets':
                  return null;
                case 'getCandlestickCount':
                  return 5; // Mock candlestick count
                default:
                  throw PlatformException(
                    code: 'UNIMPLEMENTED',
                    message: 'Method ${methodCall.method} not implemented',
                  );
              }
            },
          );

      chartEngine = ChartEngineWrapper();
    });

    tearDown(() {
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
            const MethodChannel('com.abra/chart_view'),
            null,
          );
    });

    test('Chart engine wrapper can be created', () {
      expect(chartEngine, isNotNull);
    });

    test('Chart engine can set high price', () {
      expect(() => chartEngine.setHighPrice(200.0), returnsNormally);
    });

    test('Chart engine can feed tick data', () {
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      expect(() => chartEngine.feedTick(150.25, timestamp), returnsNormally);
    });

    test('Chart engine can set zoom parameters', () {
      expect(() => chartEngine.setZoomScales(1.5, 2.0), returnsNormally);
    });

    test('Chart engine can set pan offsets', () {
      expect(() => chartEngine.setPanOffsets(10.0, 20.0), returnsNormally);
    });

    test('Chart engine can get candlestick count', () async {
      // This would normally call the native implementation
      // For testing, we're using the mocked method channel
      expect(chartEngine.getCandlestickCount(), equals(0));
    });

    test('Chart engine handles multiple tick feeds correctly', () {
      final baseTime = DateTime.now().millisecondsSinceEpoch;

      // Feed multiple ticks
      for (int i = 0; i < 10; i++) {
        final price = 150.0 + (i * 0.1);
        final timestamp = baseTime + (i * 1000); // 1 second apart
        expect(() => chartEngine.feedTick(price, timestamp), returnsNormally);
      }
    });

    test('Chart engine validates input parameters', () {
      // Test zoom scales (these should work without validation in current implementation)
      expect(() => chartEngine.setZoomScales(1.5, 2.0), returnsNormally);
      expect(() => chartEngine.setZoomScales(0.5, 0.8), returnsNormally);
    });
  });

  group('FFI Bindings Tests', () {
    test('FFI bindings can be loaded', () {
      // Test that the native library can be loaded
      // This is a basic smoke test to ensure the structure is correct
      expect(() => nativeLib, returnsNormally);
    });

    test('Chart engine can be created and disposed', () {
      // Test basic chart engine lifecycle
      final engine = createChartEngine();
      expect(engine, isNot(equals(nullptr)));

      // Test that we can get initial candlestick count (should be 0)
      final count = chartEngineGetCandlestickCount(engine);
      expect(count, equals(0));

      // Clean up
      destroyChartEngine(engine);
    });
  });

  group('Method Channel Integration Tests', () {
    test('Method channel calls are properly formatted', () async {
      // Initialize Flutter binding for testing
      TestWidgetsFlutterBinding.ensureInitialized();

      const channel = MethodChannel('com.abra/chart_view');

      // Mock successful responses
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(channel, (MethodCall methodCall) async {
            expect(
              methodCall.method,
              isIn([
                'createTexture',
                'resizeTexture',
                'disposeTexture',
                'feedTick',
                'setHighPrice',
                'setStartTime',
                'setZoomScales',
                'setPanOffsets',
                'getCandlestickCount',
              ]),
            );

            // Verify argument structure for specific methods
            if (methodCall.method == 'createTexture') {
              final args = methodCall.arguments as Map<String, dynamic>;
              expect(args, containsPair('width', isA<int>()));
              expect(args, containsPair('height', isA<int>()));
              expect(args, containsPair('timeIntervalSeconds', isA<int>()));
              return 1;
            }

            if (methodCall.method == 'feedTick') {
              final args = methodCall.arguments as Map<String, dynamic>;
              expect(args, containsPair('textureId', isA<int>()));
              expect(args, containsPair('price', isA<double>()));
              expect(args, containsPair('timestamp', isA<int>()));
              return null;
            }

            return null;
          });

      // Test createTexture call
      final textureId = await channel.invokeMethod<int>('createTexture', {
        'width': 800,
        'height': 600,
        'timeIntervalSeconds': 300,
        'includePriceAxis': false,
        'includeTimeAxis': false,
        'priceAxisWidth': 50,
        'timeAxisHeight': 30,
      });

      expect(textureId, equals(1));

      // Test feedTick call
      await channel.invokeMethod('feedTick', {
        'textureId': 1,
        'price': 150.25,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });
    });
  });
}
