import 'package:flutter_test/flutter_test.dart';
import 'package:abra/presentation/watchlist/watchlist_service.dart';
import 'package:abra/services/market_service.dart';
import 'package:abra/core/constants.dart';

void main() {
  group('WatchlistService Tests', () {
    late WatchlistService watchlistService;

    setUp(() {
      watchlistService = WatchlistService();
    });

    tearDown(() {
      watchlistService.dispose();
    });

    test('WatchlistService should be properly configured', () {
      // Test that the service can be instantiated without errors
      expect(watchlistService, isA<WatchlistService>());
      expect(
        MarketServiceConfig.baseUrl,
        equals('http://abraapp.undeclab.com'),
      );
    });

    test(
      'WatchlistService should handle authentication errors gracefully',
      () async {
        // This test verifies that the service handles unauthenticated requests properly
        final result = await watchlistService.loadWatchlists(
          userId: 'test-user',
        );

        // Should return empty list when not authenticated (rather than throwing)
        expect(result, isA<List<WatchlistDto>>());
        expect(result, isEmpty);
      },
    );

    test('WatchlistService should validate bulk price limits', () async {
      // Test that bulk prices enforces the 100 symbol limit
      final tooManySymbols = List.generate(101, (index) => 'SYMBOL$index');

      final result = await watchlistService.getBulkPrices(
        symbols: tooManySymbols,
      );

      // Should return null due to validation error
      expect(result, isNull);
    });

    test('WatchlistService should validate price analytics limits', () async {
      // Test that price analytics enforces the 50 symbol limit
      final tooManySymbols = List.generate(51, (index) => 'SYMBOL$index');

      final result = await watchlistService.getPriceAnalytics(
        symbols: tooManySymbols,
      );

      // Should return null due to validation error
      expect(result, isNull);
    });

    test('WatchlistDto should parse server response correctly', () {
      // Test with camelCase format (new API)
      final camelCaseData = {
        'id': 123,
        'name': 'My Watchlist',
        'userId': 'user-456',
        'createdAt': '2024-01-01T00:00:00Z',
        'isDefault': true,
      };

      final dto1 = WatchlistDto.fromJson(camelCaseData);
      expect(dto1.id, equals(123));
      expect(dto1.name, equals('My Watchlist'));
      expect(dto1.userId, equals('user-456'));
      expect(dto1.isDefault, equals(true));

      // Test with snake_case format (legacy compatibility)
      final snakeCaseData = {
        'id': 456,
        'name': 'Legacy Watchlist',
        'user_id': 'user-789',
        'created_at': '2024-01-02T00:00:00Z',
        'is_default': false,
      };

      final dto2 = WatchlistDto.fromJson(snakeCaseData);
      expect(dto2.id, equals(456));
      expect(dto2.name, equals('Legacy Watchlist'));
      expect(dto2.userId, equals('user-789'));
      expect(dto2.isDefault, equals(false));
    });

    test('WatchlistDto should handle missing fields gracefully', () {
      final incompleteData = {
        'id': 789,
        'name': 'Incomplete Watchlist',
        // Missing userId, createdAt, isDefault
      };

      expect(() => WatchlistDto.fromJson(incompleteData), returnsNormally);

      final dto = WatchlistDto.fromJson(incompleteData);
      expect(dto.id, equals(789));
      expect(dto.name, equals('Incomplete Watchlist'));
      expect(dto.userId, isEmpty);
      expect(dto.isDefault, equals(false));
    });

    test('WatchlistService endpoints should be correctly configured', () {
      // Verify that the service uses the correct base URL and endpoints
      expect(MarketServiceConfig.baseUrl, isNotEmpty);
      expect(MarketServiceConfig.watchlistEndpoint, equals('/api/watchlist'));
      expect(MarketServiceConfig.requestTimeout, isA<Duration>());
    });
  });

  group('WatchlistService API Endpoint Mapping', () {
    test('Should map to correct abra-servers endpoints', () {
      // This test documents the expected API endpoint mapping
      final expectedEndpoints = {
        'GET /api/watchlist': 'Get user watchlists',
        'POST /api/watchlist': 'Create new watchlist',
        'GET /api/watchlist/{id}': 'Get specific watchlist',
        'PUT /api/watchlist/{id}': 'Update watchlist',
        'DELETE /api/watchlist/{id}': 'Delete watchlist',
        'POST /api/watchlist/{id}/symbols': 'Add symbols to watchlist',
        'DELETE /api/watchlist/{id}/symbols/{symbol}':
            'Remove symbol from watchlist',
        'GET /api/watchlist/{id}/with-prices': 'Get watchlist with live prices',
        'PUT /api/watchlist/{id}/reorder': 'Reorder watchlist items',
        'POST /api/watchlist/bulk-prices': 'Get bulk prices (max 100 symbols)',
        'POST /api/watchlist/price-analytics':
            'Get price analytics (max 50 symbols)',
      };

      // Verify that we have documented all the expected endpoints
      expect(expectedEndpoints.length, equals(11));
      expect(expectedEndpoints.keys, contains('GET /api/watchlist'));
      expect(
        expectedEndpoints.keys,
        contains('POST /api/watchlist/bulk-prices'),
      );
    });
  });
}
