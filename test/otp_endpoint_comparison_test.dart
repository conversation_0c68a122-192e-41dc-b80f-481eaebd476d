import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/foundation.dart';
import 'package:abra/services/auth_client.dart';
import 'package:abra/core/constants.dart';
import 'dart:io';

void main() {
  group('OTP Endpoint Format Comparison Tests', () {
    late AuthClient authClient;

    setUpAll(() async {
      // Allow self-signed certificates for testing
      HttpOverrides.global = _TestHttpOverrides();
      
      // Initialize the auth client
      authClient = AuthClient();
      await authClient.initialize();
    });

    tearDownAll(() {
      HttpOverrides.global = null;
    });

    group('Endpoint Format Testing', () {
      test('Should test existing app OTP endpoints (phone format)', () async {
        debugPrint('🧪 Testing EXISTING app OTP endpoints...');
        debugPrint('   Send OTP: ${AuthServiceConfig.baseUrl}${AuthServiceConfig.authEndpoint}/phone/send-otp');
        debugPrint('   Verify OTP: ${AuthServiceConfig.baseUrl}${AuthServiceConfig.authEndpoint}/phone/signin');
        
        String testPhoneNumber = '+1234567890';
        String testOtp = '123456';
        
        // Test existing sendPhoneOtp method
        try {
          debugPrint('📞 Testing sendPhoneOtp...');
          final sendResult = await authClient.sendPhoneOtp(testPhoneNumber);
          
          if (sendResult.isSuccess) {
            debugPrint('✅ EXISTING sendPhoneOtp: SUCCESS - Server accepted request');
          } else {
            debugPrint('⚠️  EXISTING sendPhoneOtp: FAILED - ${sendResult.error}');
          }
        } catch (e) {
          debugPrint('❌ EXISTING sendPhoneOtp: ERROR - $e');
          
          // Analyze the error to understand server response
          if (e.toString().contains('404')) {
            debugPrint('   → Endpoint not found on server');
          } else if (e.toString().contains('400')) {
            debugPrint('   → Server rejected request (bad format/validation)');
          } else if (e.toString().contains('401') || e.toString().contains('403')) {
            debugPrint('   → Server requires authentication');
          } else if (e.toString().contains('500')) {
            debugPrint('   → Server internal error');
          } else {
            debugPrint('   → Network/SSL error: ${e.toString().substring(0, 100)}...');
          }
        }
        
        // Test existing verifyPhoneOtp method
        try {
          debugPrint('🔐 Testing verifyPhoneOtp...');
          final verifyResult = await authClient.verifyPhoneOtp(
            phoneNumber: testPhoneNumber,
            otp: testOtp,
          );
          
          if (verifyResult.isSuccess) {
            debugPrint('✅ EXISTING verifyPhoneOtp: SUCCESS - Server accepted request');
          } else {
            debugPrint('⚠️  EXISTING verifyPhoneOtp: FAILED - ${verifyResult.error}');
          }
        } catch (e) {
          debugPrint('❌ EXISTING verifyPhoneOtp: ERROR - $e');
          
          // Analyze the error
          if (e.toString().contains('404')) {
            debugPrint('   → Endpoint not found on server');
          } else if (e.toString().contains('400')) {
            debugPrint('   → Server rejected request (bad format/validation)');
          } else if (e.toString().contains('401') || e.toString().contains('403')) {
            debugPrint('   → Server requires authentication');
          } else if (e.toString().contains('500')) {
            debugPrint('   → Server internal error');
          } else {
            debugPrint('   → Network/SSL error: ${e.toString().substring(0, 100)}...');
          }
        }
      });

      test('Should test documentation OTP endpoints (docs format)', () async {
        debugPrint('📚 Testing DOCUMENTATION OTP endpoints...');
        debugPrint('   Send OTP: ${AuthServiceConfig.baseUrl}${AuthServiceConfig.authEndpoint}/otp');
        debugPrint('   Verify OTP: ${AuthServiceConfig.baseUrl}${AuthServiceConfig.authEndpoint}/verify');
        
        String testPhoneNumber = '+1234567890';
        String testOtp = '123456';
        
        // Test new sendOtp method (docs format)
        try {
          debugPrint('📞 Testing sendOtp (docs format)...');
          final sendResult = await authClient.sendOtp(testPhoneNumber);
          
          if (sendResult.isSuccess) {
            debugPrint('✅ DOCS sendOtp: SUCCESS - Server accepted request');
          } else {
            debugPrint('⚠️  DOCS sendOtp: FAILED - ${sendResult.error}');
          }
        } catch (e) {
          debugPrint('❌ DOCS sendOtp: ERROR - $e');
          
          // Analyze the error
          if (e.toString().contains('404')) {
            debugPrint('   → Endpoint not found on server');
          } else if (e.toString().contains('400')) {
            debugPrint('   → Server rejected request (bad format/validation)');
          } else if (e.toString().contains('401') || e.toString().contains('403')) {
            debugPrint('   → Server requires authentication');
          } else if (e.toString().contains('500')) {
            debugPrint('   → Server internal error');
          } else {
            debugPrint('   → Network/SSL error: ${e.toString().substring(0, 100)}...');
          }
        }
        
        // Test new verifyOtp method (docs format)
        try {
          debugPrint('🔐 Testing verifyOtp (docs format)...');
          final verifyResult = await authClient.verifyOtp(
            phoneNumber: testPhoneNumber,
            otp: testOtp,
          );
          
          if (verifyResult.isSuccess) {
            debugPrint('✅ DOCS verifyOtp: SUCCESS - Server accepted request');
          } else {
            debugPrint('⚠️  DOCS verifyOtp: FAILED - ${verifyResult.error}');
          }
        } catch (e) {
          debugPrint('❌ DOCS verifyOtp: ERROR - $e');
          
          // Analyze the error
          if (e.toString().contains('404')) {
            debugPrint('   → Endpoint not found on server');
          } else if (e.toString().contains('400')) {
            debugPrint('   → Server rejected request (bad format/validation)');
          } else if (e.toString().contains('401') || e.toString().contains('403')) {
            debugPrint('   → Server requires authentication');
          } else if (e.toString().contains('500')) {
            debugPrint('   → Server internal error');
          } else {
            debugPrint('   → Network/SSL error: ${e.toString().substring(0, 100)}...');
          }
        }
      });

      test('Should provide endpoint format recommendation', () {
        debugPrint('');
        debugPrint('📊 ENDPOINT FORMAT ANALYSIS:');
        debugPrint('');
        debugPrint('🔍 Based on the test results above:');
        debugPrint('');
        debugPrint('   EXISTING Format (/api/auth/phone/*):');
        debugPrint('   - sendPhoneOtp() → /api/auth/phone/send-otp');
        debugPrint('   - verifyPhoneOtp() → /api/auth/phone/signin');
        debugPrint('');
        debugPrint('   DOCUMENTATION Format (/api/auth/otp, /api/auth/verify):');
        debugPrint('   - sendOtp() → /api/auth/otp');
        debugPrint('   - verifyOtp() → /api/auth/verify');
        debugPrint('');
        debugPrint('🎯 RECOMMENDATION:');
        debugPrint('   1. If both formats work: Use DOCUMENTATION format (cleaner, matches docs)');
        debugPrint('   2. If only one works: Use the working format');
        debugPrint('   3. If neither works: Check server implementation');
        debugPrint('');
        debugPrint('📝 Next steps will be determined based on these results...');
      });
    });

    group('Payload Format Comparison', () {
      test('Should compare payload structures between formats', () {
        debugPrint('');
        debugPrint('📋 PAYLOAD STRUCTURE COMPARISON:');
        debugPrint('');
        
        // Existing format payloads
        final existingSendPayload = {
          'phoneNumber': '+1234567890',
          'createUser': true,
        };
        
        final existingVerifyPayload = {
          'phoneNumber': '+1234567890',
          'otpCode': '123456',
        };
        
        // Documentation format payloads
        final docsSendPayload = {
          'phoneNumber': '+1234567890',
        };
        
        final docsVerifyPayload = {
          'phoneNumber': '+1234567890',
          'otp': '123456',
        };
        
        debugPrint('   EXISTING Format Payloads:');
        debugPrint('   - Send: $existingSendPayload');
        debugPrint('   - Verify: $existingVerifyPayload');
        debugPrint('');
        debugPrint('   DOCUMENTATION Format Payloads:');
        debugPrint('   - Send: $docsSendPayload');
        debugPrint('   - Verify: $docsVerifyPayload');
        debugPrint('');
        debugPrint('   Key Differences:');
        debugPrint('   - Send: Existing includes "createUser" flag');
        debugPrint('   - Verify: Existing uses "otpCode", docs use "otp"');
      });
    });
  });
}

class _TestHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback = (X509Certificate cert, String host, int port) {
        debugPrint('⚠️ Bypassing SSL certificate verification for testing: $host:$port');
        return true;
      };
  }
}
