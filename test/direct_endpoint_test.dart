import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/foundation.dart';
import 'package:abra/core/constants.dart';
import 'dart:io';
import 'dart:convert';

void main() {
  group('Direct OTP Endpoint Testing', () {
    late HttpClient httpClient;

    setUpAll(() async {
      // Create HTTP client with SSL bypass for testing
      httpClient = HttpClient()
        ..badCertificateCallback = (X509Certificate cert, String host, int port) {
          debugPrint('⚠️ Bypassing SSL certificate verification for testing: $host:$port');
          return true;
        };
    });

    tearDownAll(() {
      httpClient.close();
    });

    Future<Map<String, dynamic>> makeRequest(String method, String endpoint, Map<String, dynamic> body) async {
      try {
        final uri = Uri.parse('${AuthServiceConfig.baseUrl}$endpoint');
        debugPrint('🌐 Making $method request to: $uri');
        
        final request = await httpClient.openUrl(method, uri);
        request.headers.set('Content-Type', 'application/json');
        request.headers.set('Accept', 'application/json');
        
        if (body.isNotEmpty) {
          final jsonBody = jsonEncode(body);
          request.write(jsonBody);
          debugPrint('📤 Request body: $jsonBody');
        }
        
        final response = await request.close();
        final responseBody = await response.transform(utf8.decoder).join();
        
        debugPrint('📥 Response status: ${response.statusCode}');
        debugPrint('📥 Response body: ${responseBody.length > 200 ? responseBody.substring(0, 200) + "..." : responseBody}');
        
        return {
          'statusCode': response.statusCode,
          'body': responseBody,
          'success': response.statusCode >= 200 && response.statusCode < 300,
        };
      } catch (e) {
        debugPrint('❌ Request failed: $e');
        return {
          'statusCode': 0,
          'body': e.toString(),
          'success': false,
          'error': e.toString(),
        };
      }
    }

    group('Existing App Format Tests', () {
      test('Should test existing sendPhoneOtp endpoint', () async {
        debugPrint('🧪 Testing EXISTING app format: sendPhoneOtp');
        debugPrint('   Endpoint: ${AuthServiceConfig.authEndpoint}/phone/send-otp');
        
        final body = {
          'phoneNumber': '+1234567890',
          'createUser': true,
        };
        
        final result = await makeRequest('POST', '${AuthServiceConfig.authEndpoint}/phone/send-otp', body);
        
        if (result['statusCode'] == 404) {
          debugPrint('❌ EXISTING sendPhoneOtp: Endpoint NOT FOUND on server');
        } else if (result['statusCode'] == 400) {
          debugPrint('✅ EXISTING sendPhoneOtp: Endpoint EXISTS but rejected test data (expected)');
        } else if (result['statusCode'] == 401 || result['statusCode'] == 403) {
          debugPrint('✅ EXISTING sendPhoneOtp: Endpoint EXISTS but requires auth (expected)');
        } else if (result['statusCode'] == 200) {
          debugPrint('✅ EXISTING sendPhoneOtp: Endpoint EXISTS and accepted request');
        } else if (result['statusCode'] == 500) {
          debugPrint('⚠️  EXISTING sendPhoneOtp: Endpoint EXISTS but server error');
        } else {
          debugPrint('⚠️  EXISTING sendPhoneOtp: Unexpected response: ${result['statusCode']}');
        }
      });

      test('Should test existing verifyPhoneOtp endpoint', () async {
        debugPrint('🧪 Testing EXISTING app format: verifyPhoneOtp');
        debugPrint('   Endpoint: ${AuthServiceConfig.authEndpoint}/phone/signin');
        
        final body = {
          'phoneNumber': '+1234567890',
          'otpCode': '123456',
        };
        
        final result = await makeRequest('POST', '${AuthServiceConfig.authEndpoint}/phone/signin', body);
        
        if (result['statusCode'] == 404) {
          debugPrint('❌ EXISTING verifyPhoneOtp: Endpoint NOT FOUND on server');
        } else if (result['statusCode'] == 400) {
          debugPrint('✅ EXISTING verifyPhoneOtp: Endpoint EXISTS but rejected test data (expected)');
        } else if (result['statusCode'] == 401 || result['statusCode'] == 403) {
          debugPrint('✅ EXISTING verifyPhoneOtp: Endpoint EXISTS but requires auth (expected)');
        } else if (result['statusCode'] == 200) {
          debugPrint('✅ EXISTING verifyPhoneOtp: Endpoint EXISTS and accepted request');
        } else if (result['statusCode'] == 500) {
          debugPrint('⚠️  EXISTING verifyPhoneOtp: Endpoint EXISTS but server error');
        } else {
          debugPrint('⚠️  EXISTING verifyPhoneOtp: Unexpected response: ${result['statusCode']}');
        }
      });
    });

    group('Documentation Format Tests', () {
      test('Should test documentation sendOtp endpoint', () async {
        debugPrint('🧪 Testing DOCUMENTATION format: sendOtp');
        debugPrint('   Endpoint: ${AuthServiceConfig.authEndpoint}/otp');
        
        final body = {
          'phoneNumber': '+1234567890',
        };
        
        final result = await makeRequest('POST', '${AuthServiceConfig.authEndpoint}/otp', body);
        
        if (result['statusCode'] == 404) {
          debugPrint('❌ DOCS sendOtp: Endpoint NOT FOUND on server');
        } else if (result['statusCode'] == 400) {
          debugPrint('✅ DOCS sendOtp: Endpoint EXISTS but rejected test data (expected)');
        } else if (result['statusCode'] == 401 || result['statusCode'] == 403) {
          debugPrint('✅ DOCS sendOtp: Endpoint EXISTS but requires auth (expected)');
        } else if (result['statusCode'] == 200) {
          debugPrint('✅ DOCS sendOtp: Endpoint EXISTS and accepted request');
        } else if (result['statusCode'] == 500) {
          debugPrint('⚠️  DOCS sendOtp: Endpoint EXISTS but server error');
        } else {
          debugPrint('⚠️  DOCS sendOtp: Unexpected response: ${result['statusCode']}');
        }
      });

      test('Should test documentation verifyOtp endpoint', () async {
        debugPrint('🧪 Testing DOCUMENTATION format: verifyOtp');
        debugPrint('   Endpoint: ${AuthServiceConfig.authEndpoint}/verify');
        
        final body = {
          'phoneNumber': '+1234567890',
          'otp': '123456',
        };
        
        final result = await makeRequest('POST', '${AuthServiceConfig.authEndpoint}/verify', body);
        
        if (result['statusCode'] == 404) {
          debugPrint('❌ DOCS verifyOtp: Endpoint NOT FOUND on server');
        } else if (result['statusCode'] == 400) {
          debugPrint('✅ DOCS verifyOtp: Endpoint EXISTS but rejected test data (expected)');
        } else if (result['statusCode'] == 401 || result['statusCode'] == 403) {
          debugPrint('✅ DOCS verifyOtp: Endpoint EXISTS but requires auth (expected)');
        } else if (result['statusCode'] == 200) {
          debugPrint('✅ DOCS verifyOtp: Endpoint EXISTS and accepted request');
        } else if (result['statusCode'] == 500) {
          debugPrint('⚠️  DOCS verifyOtp: Endpoint EXISTS but server error');
        } else {
          debugPrint('⚠️  DOCS verifyOtp: Unexpected response: ${result['statusCode']}');
        }
      });
    });

    group('Test Results Analysis', () {
      test('Should provide comprehensive analysis and recommendation', () {
        debugPrint('');
        debugPrint('📊 COMPREHENSIVE ENDPOINT ANALYSIS');
        debugPrint('═══════════════════════════════════════════════════════════════');
        debugPrint('');
        debugPrint('🔍 ENDPOINT AVAILABILITY SUMMARY:');
        debugPrint('');
        debugPrint('   EXISTING App Format:');
        debugPrint('   • Send OTP: /api/auth/phone/send-otp');
        debugPrint('   • Verify OTP: /api/auth/phone/signin');
        debugPrint('   • Payload: {"phoneNumber": "...", "createUser": true} / {"phoneNumber": "...", "otpCode": "..."}');
        debugPrint('');
        debugPrint('   DOCUMENTATION Format:');
        debugPrint('   • Send OTP: /api/auth/otp');
        debugPrint('   • Verify OTP: /api/auth/verify');
        debugPrint('   • Payload: {"phoneNumber": "..."} / {"phoneNumber": "...", "otp": "..."}');
        debugPrint('');
        debugPrint('🎯 DECISION CRITERIA:');
        debugPrint('   ✅ = Endpoint exists on server');
        debugPrint('   ❌ = Endpoint not found (404)');
        debugPrint('   ⚠️  = Endpoint exists but has issues');
        debugPrint('');
        debugPrint('📝 RECOMMENDATION LOGIC:');
        debugPrint('   1. If BOTH formats work → Use DOCUMENTATION format (cleaner, matches API docs)');
        debugPrint('   2. If ONLY existing works → Keep existing format');
        debugPrint('   3. If ONLY docs works → Switch to documentation format');
        debugPrint('   4. If NEITHER works → Check server implementation');
        debugPrint('');
        debugPrint('🚀 NEXT STEPS: Based on results above, we will choose the optimal format...');
      });
    });
  });
}
