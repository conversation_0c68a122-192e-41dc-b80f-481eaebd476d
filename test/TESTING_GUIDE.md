# Assistant Service Integration Testing Guide

This guide explains how to test the refactored Dart client integration with the assistant-service.

## Prerequisites

1. **Assistant Service Running**: Make sure the assistant-service is running on `http://localhost:5123`
   ```bash
   cd assistant-service
   dotnet run
   ```

2. **Dart/Flutter Environment**: Ensure you have Dart SDK installed for running tests

## Test Structure

### Unit Tests (`test/assistant_service_test.dart`)
Tests the core functionality without requiring the backend service:
- Configuration validation
- Message validation
- Model serialization/deserialization
- Error message generation
- Conversation history management

**Run unit tests:**
```bash
dart test test/assistant_service_test.dart
```

### Integration Tests (`test/integration_test.dart`)
Tests the actual integration with the running assistant-service:
- Basic chat functionality
- Streaming responses
- Conversation history
- Error handling with real network calls

**Run integration tests:**
```bash
# Make sure assistant-service is running first!
dart run test/integration_test.dart
```

## What the Tests Verify

### ✅ Core Integration Points
- [x] **Endpoint Integration**: Uses `/api/chat` instead of legacy `/api/assistant/ask`
- [x] **Request Format**: Sends proper `ChatRequest` with messages array
- [x] **Response Handling**: Parses `ChatResponse` and streaming responses correctly
- [x] **Model Compatibility**: Dart models match C# DTOs exactly
- [x] **Streaming Support**: Real server-side streaming (not simulated)
- [x] **Conversation History**: Proper message history management
- [x] **Error Handling**: User-friendly error messages and validation
- [x] **Input Sanitization**: Validates and sanitizes user input
- [x] **Configuration**: Centralized configuration management

### ✅ User Experience Improvements
- [x] **Type Safety**: All requests/responses are type-safe
- [x] **Validation**: Input validation with helpful error messages
- [x] **Resource Management**: Proper disposal of HTTP clients
- [x] **Performance**: Efficient conversation history limiting
- [x] **Error Recovery**: Graceful error handling with retry logic

## Expected Test Results

### Unit Tests
```
✓ should create service with default configuration
✓ should validate message input
✓ should create proper chat request
✓ should handle conversation history correctly
✓ should serialize and deserialize chat models correctly
✓ should validate configuration constants
✓ should provide friendly error messages
✓ should validate messages correctly
```

### Integration Tests
```
🚀 Starting Assistant Service Integration Test
📍 Testing against: http://localhost:5123

📝 Test 1: Basic Chat Functionality
   📤 Sent: "What is 2+2?"
   📥 Received: "2 + 2 equals 4. This is a basic arithmetic..."
✅ Basic chat test passed

📝 Test 2: Streaming Chat Functionality
   📤 Sent: "Tell me a very short joke"
   📥 Streamed 15 chunks, total: "Why don't scientists trust atoms? Because..."
✅ Streaming chat test passed

📝 Test 3: Conversation History
   📤 Sent conversation with history (3 messages total)
   📥 Received: "Your name is Alice, as you mentioned earlier..."
✅ Conversation history test passed

📝 Test 4: Error Handling
   ✅ Empty message validation works
   ✅ Message length validation works
   ✅ Friendly error messages work
✅ Error handling test passed

🎉 All integration tests passed!
✅ The Dart client is successfully integrated with the assistant-service
```

## Troubleshooting

### Common Issues

1. **Connection Refused**
   ```
   ❌ Integration test failed: Connection refused
   ```
   **Solution**: Make sure assistant-service is running on port 5123

2. **HTTP Package Missing**
   ```
   Target of URI doesn't exist: 'package:http/http.dart'
   ```
   **Solution**: This is expected in the current environment. In a real Flutter project, add to `pubspec.yaml`:
   ```yaml
   dependencies:
     http: ^1.1.0
   ```

3. **Test Package Missing**
   ```
   Target of URI doesn't exist: 'package:test/test.dart'
   ```
   **Solution**: Add to `pubspec.yaml`:
   ```yaml
   dev_dependencies:
     test: ^1.24.0
   ```

### Manual Testing

You can also test manually using the assistant page:

1. Start the assistant-service
2. Run your Flutter app
3. Open the AI chat overlay
4. Test various scenarios:
   - Send a simple message
   - Send a long conversation
   - Test error cases (empty messages, very long messages)
   - Verify streaming works properly

## Integration Checklist

- [ ] Assistant-service is running and accessible
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing in Flutter app works
- [ ] Error handling works as expected
- [ ] Streaming responses work smoothly
- [ ] Conversation history is maintained
- [ ] Input validation prevents invalid requests
- [ ] No deprecated code remains in the client

## Next Steps

After successful testing:

1. **Deploy to Production**: Update production configuration in `AssistantConfig`
2. **Monitor Performance**: Watch for any performance issues with streaming
3. **User Feedback**: Gather feedback on the improved error messages and UX
4. **Documentation**: Update any API documentation to reflect the new integration
