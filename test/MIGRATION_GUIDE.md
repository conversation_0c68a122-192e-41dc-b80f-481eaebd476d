# Client-Side Service Migration Guide

This guide explains how to migrate from the old complex client-side services to the new simplified server-side operations.

## Overview

The migration removes heavy computational work from Flutter clients and delegates it to the market service backend. This results in:

- **Better Performance**: Reduced client-side processing
- **Lower Battery Usage**: Less CPU-intensive operations on mobile
- **Improved Reliability**: Server-side caching and error handling
- **Reduced Network Traffic**: Bulk operations instead of individual calls
- **Simplified Code**: Less complex client-side logic

## Migration Steps

### 1. Replace WatchlistService with MarketService

**Old (Deprecated):**
```dart
import '../presentation/watchlist/watchlist_service.dart';

final service = WatchlistService();
final watchlists = await service.loadWatchlists(userId: userId);
await service.addSymbol(userId, listName, symbol);
```

**New (Recommended):**
```dart
import '../services/market_service.dart';
import '../services/simplified_watchlist_provider.dart';

// Use providers for state management
final watchlists = ref.watch(watchlistsProvider);
final marketService = ref.watch(marketServiceProvider);
await marketService.addSymbolToWatchlist(watchlistId, symbol);
```

### 2. Replace Individual Price Fetching with Bulk Operations

**Old (Deprecated):**
```dart
import '../brokers/services/finnhub.dart';

final finnhub = FinnhubService();
final quotes = <String, SymbolMeta>{};
for (final symbol in symbols) {
  final quote = await finnhub.fetchQuote(symbol);
  if (quote != null) quotes[symbol] = quote;
}
```

**New (Recommended):**
```dart
import '../services/market_service.dart';

final marketService = MarketService();
final response = await marketService.getBulkPrices(symbols);
final quotes = {for (var price in response.prices) price.symbol: price};
```

### 3. Replace Client-Side Price Calculations with Server-Side Analytics

**Old (Deprecated):**
```dart
import '../presentation/watchlist/providers/items_provider.dart';

final historicalData = HistoricalPriceData(symbol);
historicalData.addPricePoint(price, timestamp);
final priceChange = historicalData.priceChange;
final priceChangePercent = historicalData.priceChangePercent;
```

**New (Recommended):**
```dart
import '../services/market_service.dart';

final marketService = MarketService();
final analytics = await marketService.getPriceAnalytics([symbol]);
final symbolAnalytics = analytics.first;
final priceChange = symbolAnalytics.dayChange;
final priceChangePercent = symbolAnalytics.dayChangePercent;
```

### 4. Replace Complex WebSocket Management with Simplified Updates

**Old (Deprecated):**
```dart
import '../brokers/services/finnhub.dart';

final finnhub = FinnhubService();
await finnhub.subscribeToSymbols(symbols);
finnhub.getQuoteUpdates().listen((updates) {
  // Handle individual symbol updates
});
```

**New (Recommended):**
```dart
import '../services/simplified_realtime_service.dart';

final realtimeService = ref.watch(simplifiedRealtimeServiceProvider);
final subscriptionNotifier = ref.watch(symbolSubscriptionProvider.notifier);

// Subscribe to symbols
subscriptionNotifier.subscribeToMultiple(symbols);

// Listen to bulk updates
ref.listen(realtimePriceUpdatesProvider, (previous, next) {
  next.whenData((updates) {
    // Handle bulk price updates
  });
});
```

### 5. Update Watchlist UI Components

**Old (Deprecated):**
```dart
final symbolListAsync = ref.watch(symbolListProvider);
final quoteUpdates = ref.watch(quoteUpdatesProvider);
```

**New (Recommended):**
```dart
final watchlistWithPrices = ref.watch(watchlistWithPricesProvider);
final performanceMetrics = ref.watch(performanceMetricsProvider);
```

## Key Benefits of Migration

### Performance Improvements

1. **Reduced API Calls**: 
   - Old: N individual calls for N symbols
   - New: 1 bulk call for N symbols

2. **Server-Side Caching**:
   - Old: Client manages complex cache logic
   - New: Server handles caching with 2-minute expiration

3. **Parallel Processing**:
   - Old: Sequential client-side requests
   - New: Server-side parallel processing with semaphore control

### Network Efficiency

1. **Bulk Operations**:
   - Old: Multiple HTTP requests per watchlist
   - New: Single request with all data

2. **Optimized Responses**:
   - Old: Full API responses with unused data
   - New: Mobile-optimized DTOs with only needed fields

3. **Reduced WebSocket Connections**:
   - Old: Per-symbol WebSocket subscriptions
   - New: Periodic bulk updates (30-second intervals)

### Code Simplification

1. **Removed Complex Logic**:
   - Cache management
   - Price calculations
   - WebSocket connection handling
   - Error retry logic

2. **Centralized Operations**:
   - All market data operations in one service
   - Consistent error handling
   - Unified authentication

## Migration Checklist

- [ ] Replace `WatchlistService` usage with `MarketService`
- [ ] Update watchlist providers to use `SimplifiedWatchlistProvider`
- [ ] Replace individual `fetchQuote` calls with `getBulkPrices`
- [ ] Remove client-side price calculation logic
- [ ] Update WebSocket subscriptions to use `SimplifiedRealtimeService`
- [ ] Update UI components to use new providers
- [ ] Test bulk operations with your watchlists
- [ ] Verify performance improvements
- [ ] Remove deprecated service files

## Configuration

Update your market service base URL in `lib/services/market_service.dart`:

```dart
// Development
static const String _baseUrl = 'http://localhost:5000/api';

// Production
static const String _baseUrl = 'https://your-market-service.com/api';
```

## Testing

1. **Bulk Price Fetching**:
```dart
final response = await marketService.getBulkPrices(['AAPL', 'MSFT', 'GOOGL']);
print('Fetched ${response.prices.length} prices in ${response.processingTimeMs}ms');
print('Cache efficiency: ${response.cacheHits}/${response.totalRequested}');
```

2. **Watchlist Operations**:
```dart
final watchlists = await marketService.getWatchlists();
final watchlistWithPrices = await marketService.getWatchlistWithPrices(watchlists.first.id);
print('Watchlist has ${watchlistWithPrices.items.length} symbols with live prices');
```

3. **Real-time Updates**:
```dart
final service = SimplifiedRealtimeService();
service.subscribeToSymbols(['AAPL', 'MSFT']);
service.priceUpdates.listen((updates) {
  print('Received updates for ${updates.length} symbols');
});
```

## Troubleshooting

### Common Issues

1. **Authentication Errors**: Ensure JWT token is properly set in headers
2. **Network Timeouts**: Check server-side endpoint availability
3. **Cache Issues**: Use `bypassCache: true` for testing
4. **Missing Data**: Verify symbol format matches server expectations

### Performance Monitoring

Use the performance providers to monitor efficiency:

```dart
final metrics = ref.watch(performanceMetricsProvider);
print('Success rate: ${metrics['successRate']}%');
print('Total symbols: ${metrics['totalSymbols']}');

final realtimeMetrics = ref.watch(realtimePerformanceProvider);
print('Active subscriptions: ${realtimeMetrics['subscribedSymbolsCount']}');
```

## Next Steps

After migration:

1. Remove deprecated service files
2. Update documentation
3. Monitor performance improvements
4. Consider implementing additional server-side features:
   - Market summary endpoints
   - Advanced analytics
   - Real-time streaming (WebSocket/SSE)
   - Historical data endpoints
