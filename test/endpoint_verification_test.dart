import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/foundation.dart';
import 'package:abra/services/auth_client.dart';
import 'package:abra/services/market_service.dart';
import 'package:abra/hub/feeds/ideas_service.dart';
import 'package:abra/hub/assistant/assistant_service.dart';
import 'package:abra/core/constants.dart';

void main() {
  group('HTTPS Endpoint Verification Tests', () {
    late AuthClient authClient;
    late MarketService marketService;
    late ThreadsService threadsService;
    late AssistantService assistantService;

    setUpAll(() async {
      authClient = AuthClient();
      await authClient.initialize();
      
      marketService = MarketService();
      await marketService.initialize();
      
      threadsService = ThreadsService();
      
      assistantService = AssistantService(
        baseUrl: AssistantConfig.baseUrl,
      );
    });

    tearDownAll(() {
      marketService.dispose();
      threadsService.dispose();
    });

    group('Auth Service HTTPS Tests', () {
      test('Should use HTTPS base URL', () {
        expect(AuthServiceConfig.baseUrl, startsWith('https://'));
        expect(AuthServiceConfig.baseUrl, equals('https://abraapp.undeclab.com'));
        debugPrint('✅ Auth service using HTTPS: ${AuthServiceConfig.baseUrl}');
      });

      test('Should test auth endpoints connectivity', () async {
        try {
          // Test health endpoint first
          debugPrint('Testing auth health endpoint...');
          await authClient.testApiEndpoint();
          debugPrint('✅ Auth health endpoint accessible');
        } catch (e) {
          debugPrint('❌ Auth health endpoint failed: $e');
          // Don't fail the test, just log the issue
        }
      });

      test('Should test signup endpoint structure', () async {
        // Test that signup endpoint is properly configured
        final expectedSignupUrl = '${AuthServiceConfig.baseUrl}${AuthServiceConfig.authEndpoint}/signup';
        expect(expectedSignupUrl, equals('https://abraapp.undeclab.com/api/auth/signup'));
        debugPrint('✅ Signup endpoint URL: $expectedSignupUrl');
      });

      test('Should test login endpoint structure', () async {
        // Test that login endpoint is properly configured
        final expectedLoginUrl = '${AuthServiceConfig.baseUrl}${AuthServiceConfig.authEndpoint}/login';
        expect(expectedLoginUrl, equals('https://abraapp.undeclab.com/api/auth/login'));
        debugPrint('✅ Login endpoint URL: $expectedLoginUrl');
      });

      test('Should test token refresh endpoint structure', () async {
        // Test that token refresh endpoint is properly configured
        final expectedRefreshUrl = '${AuthServiceConfig.baseUrl}${AuthServiceConfig.authEndpoint}/token/refresh';
        expect(expectedRefreshUrl, equals('https://abraapp.undeclab.com/api/auth/token/refresh'));
        debugPrint('✅ Token refresh endpoint URL: $expectedRefreshUrl');
      });

      test('Should test profile endpoint structure', () async {
        // Test that profile endpoint is properly configured
        final expectedProfileUrl = '${AuthServiceConfig.baseUrl}${AuthServiceConfig.profileEndpoint}';
        expect(expectedProfileUrl, equals('https://abraapp.undeclab.com/api/user/me'));
        debugPrint('✅ Profile endpoint URL: $expectedProfileUrl');
      });
    });

    group('Market Data Service HTTPS Tests', () {
      test('Should use HTTPS base URL', () {
        expect(MarketServiceConfig.baseUrl, startsWith('https://'));
        expect(MarketServiceConfig.baseUrl, equals('https://abraapp.undeclab.com'));
        debugPrint('✅ Market service using HTTPS: ${MarketServiceConfig.baseUrl}');
      });

      test('Should test watchlist endpoints structure', () async {
        final baseUrl = MarketServiceConfig.baseUrl;
        
        // Test watchlist endpoints
        expect('$baseUrl/api/watchlist', equals('https://abraapp.undeclab.com/api/watchlist'));
        expect('$baseUrl/api/watchlist/1/with-prices', equals('https://abraapp.undeclab.com/api/watchlist/1/with-prices'));
        expect('$baseUrl/api/watchlist/bulk-prices', equals('https://abraapp.undeclab.com/api/watchlist/bulk-prices'));
        
        debugPrint('✅ Watchlist endpoints using HTTPS');
      });

      test('Should test market data endpoints structure', () async {
        final baseUrl = MarketServiceConfig.baseUrl;
        
        // Test market data endpoints
        expect('$baseUrl/api/marketdata/AAPL/fetch-live', equals('https://abraapp.undeclab.com/api/marketdata/AAPL/fetch-live'));
        expect('$baseUrl/api/marketdata/AAPL/history', equals('https://abraapp.undeclab.com/api/marketdata/AAPL/history'));
        
        debugPrint('✅ Market data endpoints using HTTPS');
      });
    });

    group('Thread Service Endpoint Tests', () {
      test('Should use correct thread endpoints', () {
        final baseUrl = AuthServiceConfig.baseUrl; // ThreadsService uses AuthServiceConfig.baseUrl
        
        // Verify the updated endpoints
        expect('$baseUrl/api/threads', equals('https://abraapp.undeclab.com/api/threads'));
        expect('$baseUrl/api/comments', equals('https://abraapp.undeclab.com/api/comments'));
        expect('$baseUrl/api/likes', equals('https://abraapp.undeclab.com/api/likes'));
        expect('$baseUrl/api/likes/share', equals('https://abraapp.undeclab.com/api/likes/share'));
        
        debugPrint('✅ Thread service endpoints updated correctly');
      });

      test('Should verify thread endpoint paths are correct', () {
        // Test that we're using /api/threads instead of /api/posts
        const threadsEndpoint = '/api/threads';
        const commentsEndpoint = '/api/comments';
        const likesEndpoint = '/api/likes';
        const sharesEndpoint = '/api/likes/share';
        
        expect(threadsEndpoint, equals('/api/threads'));
        expect(commentsEndpoint, equals('/api/comments'));
        expect(likesEndpoint, equals('/api/likes'));
        expect(sharesEndpoint, equals('/api/likes/share'));
        
        debugPrint('✅ Thread service using correct endpoint paths');
      });
    });

    group('Assistant Service HTTPS Tests', () {
      test('Should use HTTPS base URL', () {
        expect(AssistantConfig.baseUrl, startsWith('https://'));
        expect(AssistantConfig.baseUrl, equals('https://abraapp.undeclab.com'));
        debugPrint('✅ Assistant service using HTTPS: ${AssistantConfig.baseUrl}');
      });

      test('Should test assistant endpoints structure', () async {
        final baseUrl = AssistantConfig.baseUrl;
        
        // Test assistant endpoints
        expect('$baseUrl${AssistantConfig.askEndpoint}', equals('https://abraapp.undeclab.com/api/assistant/ask'));
        expect('$baseUrl${AssistantConfig.chatEndpoint}', equals('https://abraapp.undeclab.com/api/chat'));
        
        debugPrint('✅ Assistant endpoints using HTTPS');
      });
    });
  });

  group('Payload Field Name Tests', () {
    test('Should verify threadId is used instead of postId', () {
      // This test verifies that our payload structures use the correct field names
      final likePayload = {'threadId': 'test-thread-id'};
      final commentPayload = {'threadId': 'test-thread-id', 'content': 'test comment'};
      final sharePayload = {'threadId': 'test-thread-id'};
      
      expect(likePayload.containsKey('threadId'), isTrue);
      expect(likePayload.containsKey('postId'), isFalse);
      
      expect(commentPayload.containsKey('threadId'), isTrue);
      expect(commentPayload.containsKey('postId'), isFalse);
      
      expect(sharePayload.containsKey('threadId'), isTrue);
      expect(sharePayload.containsKey('postId'), isFalse);
      
      debugPrint('✅ Payload structures use threadId instead of postId');
    });
  });
}
