import 'package:flutter_test/flutter_test.dart';
import 'package:abra/services/auth_client.dart';
import 'package:abra/core/constants.dart';

void main() {
  group('Abra Auth Integration Tests', () {
    late AuthClient abraAuthClient;

    setUp(() {
      abraAuthClient = AuthClient();
    });

    tearDown(() {
      abraAuthClient.dispose();
    });

    test('AbraAuthClient should be properly configured', () {
      expect(abraAuthClient.baseUrl, equals(AuthServiceConfig.baseUrl));
    });

    test('AbraAuthClient health check should work', () async {
      // This test will check if the abra-servers auth-service is reachable
      final isHealthy = await abraAuthClient.checkHealth();

      // We don't assert true/false here since the service might not be running
      // in the test environment, but we verify the method doesn't throw
      expect(isHealthy, isA<bool>());
    });

    test('AbraUserProfile model should parse correctly', () {
      final testData = {
        'user_id': 'test-user-123',
        'full_name': 'Test User',
        'username': 'testuser',
        'broker_id': 'broker-123',
        'verified': true,
        'created_at': '2024-01-01T00:00:00Z',
      };

      final profile = AbraUserProfile.fromJson(testData);

      expect(profile.userId, equals('test-user-123'));
      expect(profile.fullName, equals('Test User'));
      expect(profile.username, equals('testuser'));
      expect(profile.brokerId, equals('broker-123'));
      expect(profile.verified, equals(true));
    });

    test('AbraAuthException should be properly formatted', () {
      const exception = AbraAuthException('Test error message');
      expect(exception.message, equals('Test error message'));
      expect(
        exception.toString(),
        equals('AbraAuthException: Test error message'),
      );
    });

    test('AuthServiceConfig should have correct endpoints', () {
      expect(AuthServiceConfig.baseUrl, isNotEmpty);
      expect(AuthServiceConfig.authEndpoint, equals('/api/auth'));
      expect(AuthServiceConfig.profileEndpoint, equals('/api/auth/profile'));
      expect(
        AuthServiceConfig.linkBrokerEndpoint,
        equals('/api/auth/profile/link-broker'),
      );
      expect(AuthServiceConfig.healthEndpoint, equals('/api/auth/health'));
    });

    test('Error handling should provide friendly messages', () {
      // Test network error
      final networkError = Exception('network connection failed');
      final friendlyMessage = AuthServiceConfig.getFriendlyErrorMessage(
        networkError,
      );
      expect(friendlyMessage, equals(AuthServiceConfig.networkErrorMessage));

      // Test auth error
      final authError = Exception('unauthorized access');
      final authMessage = AuthServiceConfig.getFriendlyErrorMessage(authError);
      expect(authMessage, equals(AuthServiceConfig.authErrorMessage));

      // Test timeout error
      final timeoutError = Exception('request timeout');
      final timeoutMessage = AuthServiceConfig.getFriendlyErrorMessage(
        timeoutError,
      );
      expect(timeoutMessage, equals(AuthServiceConfig.timeoutErrorMessage));
    });

    test('Profile model should handle abra-servers data', () {
      final abraData = {
        'user_id': 'test-123',
        'username': 'testuser',
        'full_name': 'Test User',
        'broker_id': 'broker-456',
        'verified': true,
      };

      // This should not throw an exception
      expect(() => abraData, returnsNormally);
    });
  });

  group('Integration Error Scenarios', () {
    test('Should handle malformed responses gracefully', () {
      // Test that AbraUserProfile handles missing fields
      final incompleteData = <String, dynamic>{
        'user_id': 'test-123',
        // Missing other required fields
      };

      expect(() => AbraUserProfile.fromJson(incompleteData), returnsNormally);

      final profile = AbraUserProfile.fromJson(incompleteData);
      expect(profile.userId, equals('test-123'));
      expect(profile.fullName, isEmpty);
      expect(profile.username, isEmpty);
    });
  });
}
