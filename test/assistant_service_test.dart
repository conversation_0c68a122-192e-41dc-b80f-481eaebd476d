import 'package:test/test.dart';
import 'package:http/http.dart' as http;
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'dart:convert';
import 'package:abra/hub/assistant/assistant_service.dart';
import 'package:abra/hub/assistant/models/chat_models.dart';
import 'package:abra/core/constants.dart';
import 'package:abra/services/auth_client.dart';

import 'assistant_service_test.mocks.dart';

@GenerateMocks([http.Client, AuthClient])
void main() {
  group('AssistantService', () {
    late MockClient mockClient;
    late MockAuthClient mockAuthClient;
    late AssistantService assistantService;

    setUp(() {
      mockClient = MockClient();
      mockAuthClient = MockAuthClient();

      // Setup default auth client behavior
      when(mockAuthClient.accessToken).thenReturn(null);
      when(mockAuthClient.isAuthenticated).thenReturn(false);
      when(mockAuthClient.currentUser).thenReturn(null);

      assistantService = AssistantService(
        baseUrl: 'http://localhost:5003',
        httpClient: mockClient,
        authClient: mockAuthClient,
      );
    });

    tearDown(() {
      assistantService.dispose();
    });

    group('testConnection', () {
      test(
        'should return true when health endpoint responds with 200',
        () async {
          // Arrange
          when(
            mockClient.get(
              Uri.parse('http://localhost:5003/api/assistant/health'),
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
              },
            ),
          ).thenAnswer((_) async => http.Response('OK', 200));

          // Act
          final result = await assistantService.testConnection();

          // Assert
          expect(result, true);
        },
      );

      test('should return false when health endpoint fails', () async {
        // Arrange
        when(
          mockClient.get(
            Uri.parse('http://localhost:5003/api/assistant/health'),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
          ),
        ).thenAnswer((_) async => http.Response('Not Found', 404));

        // Act
        final result = await assistantService.testConnection();

        // Assert
        expect(result, false);
      });

      test('should return false when request times out', () async {
        // Arrange
        when(
          mockClient.get(
            Uri.parse('http://localhost:5003/api/assistant/health'),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
          ),
        ).thenThrow(Exception('Connection timeout'));

        // Act
        final result = await assistantService.testConnection();

        // Assert
        expect(result, false);
      });
    });

    group('chat', () {
      test('should return response for valid request', () async {
        // Arrange
        final mockResponse = {
          'message': {
            'role': 'assistant',
            'content': 'Hello! How can I help you today?',
          },
          'done': true,
        };

        when(
          mockClient.post(
            Uri.parse('http://localhost:5003/api/chat'),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: anyNamed('body'),
          ),
        ).thenAnswer((_) async => http.Response(jsonEncode(mockResponse), 200));

        // Act
        final result = await assistantService.chat('Hello');

        // Assert
        expect(result, 'Hello! How can I help you today?');
      });

      test('should return error message for server error', () async {
        // Arrange
        when(
          mockClient.post(
            Uri.parse('http://localhost:5003/api/chat'),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: anyNamed('body'),
          ),
        ).thenAnswer((_) async => http.Response('Internal Server Error', 500));

        // Act
        final result = await assistantService.chat('Hello');

        // Assert
        expect(result, startsWith('❌'));
      });

      test('should include conversation history in request', () async {
        // Arrange
        final mockResponse = {
          'message': {
            'role': 'assistant',
            'content': 'Based on our previous conversation, I understand.',
          },
          'done': true,
        };

        when(
          mockClient.post(
            Uri.parse('http://localhost:5003/api/chat'),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: anyNamed('body'),
          ),
        ).thenAnswer((_) async => http.Response(jsonEncode(mockResponse), 200));

        final history = [
          ChatMessageHelper.system('You are a helpful assistant'),
          ChatMessageHelper.user('Previous question'),
          ChatMessageHelper.assistant('Previous answer'),
        ];

        // Act
        final result = await assistantService.chat(
          'Follow-up question',
          conversationHistory: history,
        );

        // Assert
        expect(result, 'Based on our previous conversation, I understand.');

        // Verify request body contains conversation history
        final capturedCall =
            verify(
              mockClient.post(
                Uri.parse('http://localhost:5003/api/chat'),
                headers: {
                  'Content-Type': 'application/json',
                  'Accept': 'application/json',
                },
                body: captureAnyNamed('body'),
              ),
            ).captured.single;

        final requestBody = jsonDecode(capturedCall);
        expect(requestBody['messages'], hasLength(4)); // 3 history + 1 new
      });
    });

    group('chatStream', () {
      test('should parse streaming response correctly', () async {
        // Arrange
        final streamingResponse = '''
{"message":{"role":"assistant","content":"Hello"},"done":false}
{"message":{"role":"assistant","content":" there!"},"done":false}
{"message":{"role":"assistant","content":""},"done":true}
''';

        when(
          mockClient.post(
            Uri.parse('http://localhost:5003/api/chat'),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'text/plain',
            },
            body: anyNamed('body'),
          ),
        ).thenAnswer((_) async => http.Response(streamingResponse, 200));

        // Act
        final chunks = <String>[];
        await for (final chunk in assistantService.chatStream('Hello')) {
          chunks.add(chunk);
        }

        // Assert
        expect(chunks, ['Hello', ' there!']);
      });

      test('should handle malformed streaming response', () async {
        // Arrange
        final streamingResponse = '''
{"message":{"role":"assistant","content":"Hello"},"done":false}
invalid json line
{"message":{"role":"assistant","content":" there!"},"done":true}
''';

        when(
          mockClient.post(
            Uri.parse('http://localhost:5003/api/chat'),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'text/plain',
            },
            body: anyNamed('body'),
          ),
        ).thenAnswer((_) async => http.Response(streamingResponse, 200));

        // Act
        final chunks = <String>[];
        await for (final chunk in assistantService.chatStream('Hello')) {
          chunks.add(chunk);
        }

        // Assert
        expect(chunks, ['Hello', ' there!']); // Should skip invalid line
      });
    });

    group('continueConversation', () {
      test('should return response and updated conversation', () async {
        // Arrange
        final mockResponse = {
          'message': {
            'role': 'assistant',
            'content': 'I can help you with that.',
          },
          'done': true,
        };

        when(
          mockClient.post(
            Uri.parse('http://localhost:5003/api/chat'),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: anyNamed('body'),
          ),
        ).thenAnswer((_) async => http.Response(jsonEncode(mockResponse), 200));

        final initialConversation = [
          ChatMessageHelper.system('You are a helpful assistant'),
        ];

        // Act
        final result = await assistantService.continueConversation(
          initialConversation,
          'Can you help me?',
        );

        // Assert
        expect(result.response, 'I can help you with that.');
        expect(result.conversation, hasLength(3)); // system + user + assistant
        expect(result.conversation[1].role, 'user');
        expect(result.conversation[1].content, 'Can you help me?');
        expect(result.conversation[2].role, 'assistant');
        expect(result.conversation[2].content, 'I can help you with that.');
      });
    });

    group('input validation', () {
      test('should reject empty messages', () async {
        // Act & Assert
        expect(() => assistantService.chat(''), throwsA(isA<ArgumentError>()));
        expect(
          () => assistantService.chat('   '),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('should reject messages that are too long', () async {
        // Arrange
        final longMessage = 'a' * (AssistantConfig.maxMessageLength + 1);

        // Act & Assert
        expect(
          () => assistantService.chat(longMessage),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('should sanitize input messages', () async {
        // Arrange
        final mockResponse = {
          'message': {'role': 'assistant', 'content': 'Response'},
          'done': true,
        };

        when(
          mockClient.post(
            Uri.parse('http://localhost:5003/api/chat'),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: anyNamed('body'),
          ),
        ).thenAnswer((_) async => http.Response(jsonEncode(mockResponse), 200));

        // Act
        final result = await assistantService.chat(
          '  Message with   extra   spaces  ',
        );

        // Assert
        expect(result, 'Response');

        // Verify sanitized message was sent
        final capturedCall =
            verify(
              mockClient.post(
                Uri.parse('http://localhost:5003/api/chat'),
                headers: {
                  'Content-Type': 'application/json',
                  'Accept': 'application/json',
                },
                body: captureAnyNamed('body'),
              ),
            ).captured.single;

        final requestBody = jsonDecode(capturedCall);
        final userMessage = requestBody['messages'].last;
        expect(userMessage['content'], 'Message with extra spaces');
      });
    });

    group('ask', () {
      test('should return response for valid request', () async {
        // Arrange
        when(
          mockClient.post(
            Uri.parse('http://localhost:5003/api/assistant/ask'),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: '{"prompt":"Hello"}',
          ),
        ).thenAnswer(
          (_) async => http.Response('Hello! How can I help you?', 200),
        );

        // Act
        final result = await assistantService.ask('Hello');

        // Assert
        expect(result, 'Hello! How can I help you?');
      });

      test('should return error message for server error', () async {
        // Arrange
        when(
          mockClient.post(
            Uri.parse('http://localhost:5003/api/assistant/ask'),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: '{"prompt":"Hello"}',
          ),
        ).thenAnswer((_) async => http.Response('Server Error', 500));

        // Act
        final result = await assistantService.ask('Hello');

        // Assert
        expect(result, contains('❌'));
      });
    });

    group('rate limiting', () {
      test('should track rate limits correctly', () {
        // Act & Assert
        for (int i = 0; i < 15; i++) {
          expect(assistantService.checkRateLimit('/api/assistant/ask'), isTrue);
        }

        // Next request should be rate limited
        expect(assistantService.checkRateLimit('/api/assistant/ask'), isFalse);
      });

      test('should return rate limit status', () {
        // Act
        final status = assistantService.getRateLimitStatus(
          '/api/assistant/ask',
        );

        // Assert
        expect(status['endpoint'], '/api/assistant/ask');
        expect(status['limit'], 15);
        expect(status['currentRequests'], 0);
        expect(status['remaining'], 15);
      });

      test('should clear rate limit history', () {
        // Arrange - make some requests
        for (int i = 0; i < 5; i++) {
          assistantService.checkRateLimit('/api/assistant/ask');
        }

        // Act
        assistantService.clearRateLimitHistory();

        // Assert
        final status = assistantService.getRateLimitStatus(
          '/api/assistant/ask',
        );
        expect(status['currentRequests'], 0);
      });
    });

    group('authentication', () {
      test('should include auth headers when authenticated', () {
        // Arrange
        when(mockAuthClient.accessToken).thenReturn('test-token');
        when(mockAuthClient.isAuthenticated).thenReturn(true);

        // Act
        final headers = assistantService.createHeaders();

        // Assert
        expect(headers['Authorization'], 'Bearer test-token');
      });

      test('should not include auth headers when not authenticated', () {
        // Arrange
        when(mockAuthClient.accessToken).thenReturn(null);
        when(mockAuthClient.isAuthenticated).thenReturn(false);

        // Act
        final headers = assistantService.createHeaders();

        // Assert
        expect(headers.containsKey('Authorization'), isFalse);
      });
    });

    group('configuration', () {
      test('should return current configuration', () {
        // Act
        final config = assistantService.getConfig();

        // Assert
        expect(config['baseUrl'], 'http://localhost:5003');
        expect(config['defaultModel'], AssistantConfig.defaultModel);
        expect(config['endpoints']['chat'], 'http://localhost:5003/api/chat');
        expect(
          config['endpoints']['ask'],
          'http://localhost:5003/api/assistant/ask',
        );
        expect(
          config['endpoints']['health'],
          'http://localhost:5003/api/assistant/health',
        );
        expect(config['authentication'], isA<Map<String, dynamic>>());
        expect(config['rateLimits'], isA<Map<String, dynamic>>());
      });
    });
  });
}
