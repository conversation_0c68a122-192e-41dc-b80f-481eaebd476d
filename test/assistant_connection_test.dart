/// Simple connection test for the assistant service
/// This test can run without Flutter dependencies
///
/// Usage: dart run test/assistant_connection_test.dart
library;

import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

const String baseUrl = 'http://abraapp.undeclab.com';

Future<void> main() async {
  debugPrint('🚀 Testing Assistant Service Connection');
  debugPrint('📍 Testing against: $baseUrl');
  debugPrint('');

  try {
    // Test 1: Health check
    debugPrint('📝 Test 1: Health Check');
    await testHealthCheck();
    debugPrint('✅ Health check passed\n');

    // Test 2: Direct Ollama connection
    await testDirectOllamaConnection();
    debugPrint('');

    // Test 3: Simple ask endpoint
    debugPrint('📝 Test 3: Simple Ask Endpoint');
    await testAskEndpoint();
    debugPrint('✅ Ask endpoint test passed\n');

    // Test 4: Chat endpoint (non-streaming)
    debugPrint('📝 Test 4: Chat Endpoint (Non-streaming)');
    await testChatEndpoint();
    debugPrint('✅ Chat endpoint test passed\n');

    debugPrint('🎉 All connection tests passed!');
    debugPrint('✅ The assistant service is accessible and working');
    debugPrint('✅ You can now proceed to use the assistant service');
  } catch (e) {
    debugPrint('❌ Connection test failed: $e');
    debugPrint('');
    debugPrint('💡 Make sure the assistant service is running on $baseUrl');
    debugPrint(
      '   Check if the Docker containers are up and nginx is routing correctly',
    );
    exit(1);
  }
}

Future<void> testHealthCheck() async {
  final response = await http
      .get(
        Uri.parse('$baseUrl/health'),
        headers: {'Accept': 'application/json'},
      )
      .timeout(const Duration(seconds: 10));

  if (response.statusCode != 200) {
    throw Exception(
      'Health check failed with status ${response.statusCode}: ${response.body}',
    );
  }

  debugPrint('   ✓ Health endpoint responded with: ${response.body.trim()}');
}

Future<void> testAskEndpoint() async {
  final request = {'prompt': 'What is 2+2? Please answer briefly.'};

  final response = await http
      .post(
        Uri.parse('$baseUrl/api/assistant/ask'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode(request),
      )
      .timeout(const Duration(seconds: 30));

  if (response.statusCode != 200) {
    throw Exception(
      'Ask endpoint failed with status ${response.statusCode}: ${response.body}',
    );
  }

  final responseData = jsonDecode(response.body);
  final reply = responseData['reply'] as String;

  if (reply.isEmpty) {
    throw Exception('Ask endpoint returned empty reply');
  }

  debugPrint(
    '   ✓ Ask endpoint responded with: ${reply.substring(0, reply.length > 50 ? 50 : reply.length)}${reply.length > 50 ? '...' : ''}',
  );
}

Future<void> testDirectOllamaConnection() async {
  debugPrint('📝 Test 2: Direct Ollama Connection Test');
  try {
    final ollamaResponse = await http
        .post(
          Uri.parse('http://10.128.0.5/api/generate'),
          headers: {'Content-Type': 'application/json'},
          body: jsonEncode({
            'model': 'llama3.2:1b',
            'prompt': 'Hello from test!',
            'stream': false,
          }),
        )
        .timeout(Duration(seconds: 30));

    if (ollamaResponse.statusCode == 200) {
      final data = jsonDecode(ollamaResponse.body);
      final response = data['response'] ?? 'No response';
      debugPrint(
        '   ✓ Direct Ollama connection works: ${response.substring(0, response.length > 50 ? 50 : response.length)}${response.length > 50 ? '...' : ''}',
      );
    } else {
      debugPrint(
        '   ❌ Direct Ollama connection failed: ${ollamaResponse.statusCode} - ${ollamaResponse.body}',
      );
    }
  } catch (e) {
    debugPrint('   ❌ Direct Ollama connection error: $e');
  }
}

Future<void> testChatEndpoint() async {
  final request = {
    'model': 'llama3.2:1b',
    'messages': [
      {'role': 'user', 'content': 'What is 3+3? Please answer briefly.'},
    ],
    'stream': true,
  };

  final response = await http
      .post(
        Uri.parse('$baseUrl/api/chat'),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode(request),
      )
      .timeout(const Duration(seconds: 30));

  if (response.statusCode != 200) {
    throw Exception(
      'Chat endpoint failed with status ${response.statusCode}: ${response.body}',
    );
  }

  final responseData = jsonDecode(response.body);
  final message = responseData['message'];
  final content = message['content'] as String;

  if (content.isEmpty) {
    throw Exception('Chat endpoint returned empty content');
  }

  debugPrint(
    '   ✓ Chat endpoint responded with: ${content.substring(0, content.length > 50 ? 50 : content.length)}${content.length > 50 ? '...' : ''}',
  );
}
