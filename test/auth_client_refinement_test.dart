import 'package:flutter_test/flutter_test.dart';
import 'package:abra/services/auth_client.dart';
import 'package:abra/core/constants.dart';

void main() {
  group('AuthClient Refinement Tests', () {
    late AuthClient authClient;

    setUp(() {
      authClient = AuthClient();
    });

    tearDown(() {
      authClient.dispose();
    });

    test('AuthClient should use consistent error handling', () async {
      // Test that all methods now use consistent error handling through _makeRequest

      // Test sign up with invalid data (should handle errors gracefully)
      final signUpResult = await authClient.signUpWithEmail(
        email: 'invalid-email',
        password: 'short',
      );

      expect(signUpResult.isFailure, isTrue);
      expect(signUpResult.error, isNotNull);
      expect(signUpResult.error, isNotEmpty); // Should contain error info
    });

    test('AuthClient should handle network errors consistently', () async {
      // Test that network errors are handled consistently across methods

      final signInResult = await authClient.signInWithEmail(
        email: '<EMAIL>',
        password: 'password123',
      );

      expect(signInResult.isFailure, isTrue);
      expect(signInResult.error, isNotNull);
    });

    test(
      'AuthClient should handle phone authentication consistently',
      () async {
        // Test phone OTP sending
        final phoneResult = await authClient.sendPhoneOtp('+**********');

        // Should either succeed or fail gracefully
        expect(phoneResult, isA<ServerAuthResult>());

        if (phoneResult.isFailure) {
          expect(phoneResult.error, isNotNull);
        }
      },
    );

    test('AuthClient should handle OAuth operations consistently', () async {
      // Test OAuth URL generation
      final oauthResult = await authClient.getOAuthUrl(provider: 'google');

      expect(oauthResult, isA<OAuthUrlResult>());

      if (!oauthResult.isSuccess) {
        expect(oauthResult.error, isNotNull);
      }
    });

    test('AuthClient should handle profile operations consistently', () async {
      // Test profile retrieval (should handle unauthenticated state)
      try {
        final profile = await authClient.getUserProfile();
        // Should either return null or throw AbraAuthException
        expect(profile, isNull);
      } on AbraAuthException catch (e) {
        expect(e.message, isNotEmpty);
      }
    });

    test('AuthClient should handle broker linking consistently', () async {
      // Test broker linking (should handle unauthenticated state)
      try {
        final result = await authClient.linkBroker(brokerId: 'test-broker');
        expect(result, isFalse); // Should fail when not authenticated
      } on AbraAuthException catch (e) {
        expect(e.message, isNotEmpty);
      }
    });

    test(
      'AuthClient health check should work with refined error handling',
      () async {
        // Health check should return boolean without throwing
        final isHealthy = await authClient.checkHealth();
        expect(isHealthy, isA<bool>());
      },
    );

    test('AuthClient should maintain singleton pattern', () {
      final client1 = AuthClient();
      final client2 = AuthClient();

      expect(identical(client1, client2), isTrue);
    });

    test('AuthClient should handle token refresh consistently', () async {
      // Test token refresh (should handle no refresh token state)
      final refreshResult = await authClient.refreshAccessToken();

      expect(refreshResult.isFailure, isTrue);
      expect(refreshResult.error, contains('No refresh token'));
    });

    test('AuthClient should handle sign out consistently', () async {
      // Test sign out (should work even when not authenticated)
      final result = await authClient.signOut();
      expect(result, isA<bool>());
    });
  });

  group('AuthClient Configuration Tests', () {
    test('AuthClient should use correct base URL', () {
      final client = AuthClient();
      expect(client.baseUrl, equals(AuthServiceConfig.baseUrl));
      expect(client.baseUrl, equals('http://abraapp.undeclab.com'));
    });

    test('AuthClient should use correct timeout configurations', () {
      // Verify that timeout configurations are properly used
      expect(AuthServiceConfig.requestTimeout, isA<Duration>());
      expect(AuthServiceConfig.healthCheckTimeout, isA<Duration>());
    });

    test('AuthClient should handle authentication state correctly', () {
      final client = AuthClient();

      // Initially should not be authenticated
      expect(client.isAuthenticated, isFalse);
      expect(client.accessToken, isNull);
      expect(client.refreshToken, isNull);
      expect(client.currentUser, isNull);
    });
  });
}
