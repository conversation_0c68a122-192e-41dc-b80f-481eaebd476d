import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:abra/presentation/watchlist/widgets/mini_chart.dart';

/// Test for the refactored mini chart widget
void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('MiniChart Widget Tests', () {
    testWidgets('should render placeholder when no data', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: MiniChart(
                symbol: 'TEST',
                width: 80,
                height: 40,
              ),
            ),
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pump();

      // Verify that the placeholder icon is shown
      expect(find.byIcon(Icons.show_chart), findsOneWidget);
    });

    testWidgets('should have correct dimensions', (WidgetTester tester) async {
      const testWidth = 100.0;
      const testHeight = 50.0;

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: MiniChart(
                symbol: 'AAPL',
                width: testWidth,
                height: testHeight,
              ),
            ),
          ),
        ),
      );

      await tester.pump();

      // Find the container/sized box with the specified dimensions
      final containerFinder = find.byType(Container);
      expect(containerFinder, findsOneWidget);

      final Container container = tester.widget(containerFinder);
      expect(container.constraints?.maxWidth, testWidth);
      expect(container.constraints?.maxHeight, testHeight);
    });

    testWidgets('should handle different symbols', (WidgetTester tester) async {
      const symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA'];

      for (final symbol in symbols) {
        await tester.pumpWidget(
          ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: MiniChart(
                  symbol: symbol,
                  width: 60,
                  height: 30,
                ),
              ),
            ),
          ),
        );

        await tester.pump();

        // Should render without errors for any symbol
        expect(find.byType(MiniChart), findsOneWidget);
      }
    });
  });

  group('MiniChartPainter Tests', () {
    test('should handle empty data gracefully', () {
      final painter = MiniChartPainter(data: []);
      
      // Should not throw when painting with empty data
      expect(() {
        painter.paint(
          MockCanvas(),
          const Size(100, 50),
        );
      }, returnsNormally);
    });

    test('should determine repaint correctly', () {
      final painter1 = MiniChartPainter(data: []);
      final painter2 = MiniChartPainter(data: []);
      
      // Same data should not require repaint
      expect(painter1.shouldRepaint(painter2), isFalse);
    });
  });
}

/// Mock canvas for testing
class MockCanvas implements Canvas {
  @override
  void noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}
