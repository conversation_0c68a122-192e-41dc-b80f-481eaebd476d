import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/foundation.dart';
import 'package:abra/presentation/symbol/symbol_service.dart';

/// Demonstration of the enhanced symbol search functionality
void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('Symbol Search Demo', () {
    late SymbolService symbolService;

    setUp(() {
      symbolService = SymbolService();
    });

    test('Demo: Enhanced Search Flow', () async {
      debugPrint('\n=== SYMBOL SEARCH DEMONSTRATION ===\n');

      // Test 1: Empty query handling
      debugPrint('1. Testing empty query...');
      final emptyResults = await symbolService.searchSymbols('');
      debugPrint('   Empty query results: ${emptyResults.length} symbols');
      expect(emptyResults, isEmpty);

      // Test 2: Whitespace query handling
      debugPrint('\n2. Testing whitespace query...');
      final whitespaceResults = await symbolService.searchSymbols('   ');
      debugPrint('   Whitespace query results: ${whitespaceResults.length} symbols');
      expect(whitespaceResults, isEmpty);

      // Test 3: Real search query
      debugPrint('\n3. Testing real search query: "AAPL"...');
      try {
        final searchResults = await symbolService.searchSymbols('AAPL');
        debugPrint('   Search results: ${searchResults.length} symbols found');
        
        if (searchResults.isNotEmpty) {
          debugPrint('   First result:');
          final first = searchResults.first;
          debugPrint('     Symbol: ${first.symbol}');
          debugPrint('     Description: ${first.description}');
          debugPrint('     Broker: ${first.broker}');
          debugPrint('     Price: ${first.price}');
          debugPrint('     Timestamp: ${first.timestamp}');
        }
        
        expect(searchResults, isA<List>());
      } catch (e) {
        debugPrint('   Search failed (expected in test environment): $e');
        expect(e.toString(), contains('User not authenticated'));
      }

      // Test 4: Different search query
      debugPrint('\n4. Testing search query: "GOOGL"...');
      try {
        final googleResults = await symbolService.searchSymbols('GOOGL');
        debugPrint('   Google search results: ${googleResults.length} symbols found');
        expect(googleResults, isA<List>());
      } catch (e) {
        debugPrint('   Search failed (expected in test environment): $e');
      }

      debugPrint('\n=== SEARCH FLOW EXPLANATION ===');
      debugPrint('The enhanced search follows this flow:');
      debugPrint('1. Check if query is empty/whitespace → return empty list');
      debugPrint('2. Try broker search endpoint: /api/brokers/symbols/search?q=query');
      debugPrint('3. If broker search succeeds → convert results to SymbolMeta with prices');
      debugPrint('4. If broker search fails → fallback to searching popular symbols');
      debugPrint('5. Popular symbols search filters by symbol name and description');
      debugPrint('6. Return results with proper error handling throughout');
      debugPrint('\n=== DEMO COMPLETE ===\n');
    });

    test('Demo: Search Result Structure', () async {
      debugPrint('\n=== SEARCH RESULT STRUCTURE DEMO ===\n');

      try {
        final results = await symbolService.searchSymbols('MSFT');
        
        debugPrint('Search query: "MSFT"');
        debugPrint('Results count: ${results.length}');
        
        if (results.isNotEmpty) {
          debugPrint('\nFirst result structure:');
          final result = results.first;
          
          debugPrint('SymbolMeta {');
          debugPrint('  symbol: "${result.symbol}"');
          debugPrint('  description: "${result.description}"');
          debugPrint('  price: ${result.price}');
          debugPrint('  change: ${result.change}');
          debugPrint('  volume: ${result.volume}');
          debugPrint('  broker: "${result.broker}"');
          debugPrint('  timestamp: ${result.timestamp}');
          debugPrint('  bid: ${result.bid}');
          debugPrint('  ask: ${result.ask}');
          debugPrint('}');
          
          // Validate structure
          expect(result.symbol, isNotEmpty);
          expect(result.timestamp, isNotNull);
          expect(result.description, isNotNull);
          expect(result.broker, isNotNull);
        }
        
      } catch (e) {
        debugPrint('Search failed (expected): $e');
      }

      debugPrint('\n=== STRUCTURE DEMO COMPLETE ===\n');
    });

    test('Demo: Search vs Popular Symbols', () async {
      debugPrint('\n=== SEARCH VS POPULAR SYMBOLS COMPARISON ===\n');

      try {
        // Get popular symbols
        debugPrint('1. Fetching popular symbols...');
        final popularSymbols = await symbolService.getPopularSymbols();
        debugPrint('   Popular symbols count: ${popularSymbols.length}');
        
        if (popularSymbols.isNotEmpty) {
          debugPrint('   Popular symbols: ${popularSymbols.map((s) => s.symbol).join(', ')}');
        }

        // Search for a specific symbol
        debugPrint('\n2. Searching for "AAPL"...');
        final searchResults = await symbolService.searchSymbols('AAPL');
        debugPrint('   Search results count: ${searchResults.length}');
        
        if (searchResults.isNotEmpty) {
          debugPrint('   Search results: ${searchResults.map((s) => s.symbol).join(', ')}');
        }

        debugPrint('\n3. Key differences:');
        debugPrint('   - Popular symbols: Top 10 most active by volume');
        debugPrint('   - Search results: Filtered by query relevance');
        debugPrint('   - Both include price data when available');
        debugPrint('   - Both have fallback mechanisms for reliability');

      } catch (e) {
        debugPrint('Operations failed (expected): $e');
      }

      debugPrint('\n=== COMPARISON DEMO COMPLETE ===\n');
    });
  });
}
